# Git 相关文件
.git
.gitignore
.gitattributes

# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE 和编辑器配置
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/*.log

# 数据库文件
*.sqlite3
*.db

# 测试相关
.coverage
.pytest_cache/
.tox/
htmlcov/
.nox/
coverage.xml
*.cover
.hypothesis/

# 文档
docs/
*.md
README*
CHANGELOG*
LICENSE*

# Docker 相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 临时文件
*.tmp
*.temp
.cache/

# 媒体文件（开发时上传的文件）
media/

# 静态文件（会在容器中重新收集）
staticfiles/

# 备份文件
*.bak
*.backup

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js 相关（如果有前端构建）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 其他开发工具
.mypy_cache/
.dmypy.json
dmypy.json
