# 依赖目录
node_modules
.pnpm-store
.pnpm-debug.log*

# 构建输出
dist
.astro

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器和IDE
.vscode
.idea
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# 测试覆盖率
coverage
*.lcov

# 临时文件
*.tmp
*.temp

# Docker相关
Dockerfile
.dockerignore
docker-compose.yml
docker-compose.yaml

# 文档
README.md
*.md

# 环境变量文件 - 使用Docker环境变量替代
.env
.env.local
.env.*.local

# 其他
.npmrc
.yarnrc
