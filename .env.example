# 数据库配置
DB_NAME=threat_intelligence_db
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_HOST=localhost
DB_PORT=5432

# Django配置
SECRET_KEY=your_secret_key_here
DEBUG=True

# 微信登录配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 阿里云短信服务配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
ALIYUN_SMS_SIGN_NAME=威胁情报数据中心
ALIYUN_SMS_TEMPLATE_REGISTER=SMS_123456789
ALIYUN_SMS_TEMPLATE_LOGIN=SMS_123456789
ALIYUN_SMS_TEMPLATE_RESET=SMS_123456789

# 配置阿里云 OSS
ALIYUN_OSS_ACCESS_KEY_ID=LTAI5tKE1DoaWWfduZ4aNjV6
ALIYUN_OSS_ACCESS_KEY_SECRET=******************************
ALIYUN_OSS_BUCKET_NAME=sierting-datacenter
ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com

# 允许访问的域名
ALLOWED_HOSTS=*********

# CORS 允许的域名
CORS_ALLOWED_ORIGINS=http://localhost:4321,http://127.0.0.1:4321,http://localhost:3000,http://127.0.0.1:3000,https://threat.solarsecurity.cn,https://threat-admin.solarsecurity.cn

# CSRF 可信来源
CSRF_TRUSTED_ORIGINS=https://threat-admin.solarsecurity.cn,http://localhost:4321,http://127.0.0.1:4321,http://localhost:3000,http://127.0.0.1:3000
