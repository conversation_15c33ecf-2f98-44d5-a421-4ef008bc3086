# 威胁情报数据中心后端 Dockerfile (使用官方 uv Docker 镜像)
# 基于官方 uv Docker 镜像

# 使用官方 uv 镜像作为基础镜像
FROM ghcr.io/astral-sh/uv:python3.13-bookworm-slim

# 设置 Debian 加速源（使用阿里云镜像）
RUN cp /etc/apt/sources.list.d/debian.sources /etc/apt/sources.list.d/debian.sources.bak && \
    sed -i 's|deb.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy \
    UV_CACHE_DIR=/app/.cache

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # PostgreSQL 客户端库
    libpq-dev \
    # 编译工具（用于某些 Python 包）
    gcc \
    g++ \
    # 其他必要工具
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 创建虚拟环境并安装依赖
RUN uv sync --frozen --no-cache

# 创建应用用户（非 root 用户，提高安全性）
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 复制应用代码
COPY . .

# 创建必要的目录并设置权限
RUN mkdir -p logs media staticfiles .cache && \
    chown -R appuser:appuser /app

# 切换到非 root 用户
USER appuser

# 激活虚拟环境并收集静态文件
RUN uv run python manage.py collectstatic --noinput

# 暴露端口
EXPOSE 8000

# 声明挂载点
VOLUME ["/app/media", "/app/staticfiles", "/app/logs"]



# 启动命令（使用 Gunicorn 生产级 WSGI 服务器）
CMD ["uv", "run", "gunicorn", "config.wsgi:application", "--bind", "0.0.0.0:8000", "--workers", "4"]
