# 威胁情报数据中心 Dockerfile
# 基于 Astro 框架的多阶段构建

# 第一阶段：构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@10.6.5

# 复制 package.json 和 pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 设置构建时环境变量
ARG PUBLIC_API_BASE_URL=https://threat-admin.solarsecurity.cn/api/v1
ENV PUBLIC_API_BASE_URL=${PUBLIC_API_BASE_URL}

# 构建应用
RUN pnpm run build

# 第二阶段：生产阶段
FROM node:20-alpine AS production

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@10.6.5

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S astro -u 1001

# 复制 package.json 和 pnpm-lock.yaml (不复制.env文件，使用Docker环境变量)
COPY package.json pnpm-lock.yaml ./

# 只安装生产依赖
RUN pnpm install --prod --frozen-lockfile

# 从构建阶段复制构建产物
COPY --from=builder --chown=astro:nodejs /app/dist ./dist

# 切换到非root用户
USER astro

# 设置环境变量
ENV HOST=0.0.0.0
ENV PORT=4321

# 暴露端口
EXPOSE 4321

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:4321/ || exit 1

# 启动应用
CMD ["node", "./dist/server/entry.mjs"]