"""
用户认证模块的Django Admin配置 - 简化版本
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, VerificationCode


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    # 列表页显示的核心字段
    list_display = ('username', 'email', 'phone', 'get_groups_display', 'is_active', 'is_staff', 'created_at')

    # 过滤器
    list_filter = ('is_active', 'is_staff', 'is_superuser', 'groups', 'created_at')

    # 搜索字段
    search_fields = ('username', 'email', 'phone', 'nickname')

    # 排序
    ordering = ('-created_at',)

    # 简化的字段分组 - 默认展开
    fieldsets = (
        ('基本信息', {
            'classes': ('wide',),
            'fields': ('username', 'password', 'email', 'phone')
        }),
        ('权限', {
            'classes': ('wide',),
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('其他信息', {
            'classes': ('wide',),
            'fields': ('nickname', 'avatar', 'bio', 'last_login_method', 'wechat_openid'),
        }),
    )

    # 添加用户时的字段
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'email', 'phone', 'password1', 'password2'),
        }),
    )

    # 只读字段
    readonly_fields = ('last_login_method', 'created_at', 'last_login')

    def get_groups_display(self, obj):
        """获取用户所属的组，用于在列表页显示"""
        return ", ".join([group.name for group in obj.groups.all()]) if obj.groups.exists() else "-"

    get_groups_display.short_description = '用户组'


@admin.register(VerificationCode)
class VerificationCodeAdmin(admin.ModelAdmin):
    # 列表页显示字段
    list_display = ('phone', 'code', 'code_type', 'is_used', 'failed_attempts', 'created_at', 'expires_at')

    # 排序
    ordering = ('-created_at',)

    # 只读字段
    readonly_fields = ('created_at', 'updated_at')

    # 禁用添加和修改权限（验证码应由系统自动生成）
    def has_add_permission(self, request):
        """禁用验证码的添加权限"""
        _ = request  # 忽略未使用参数
        return False

    def has_change_permission(self, request, obj=None):
        """禁用验证码的修改权限"""
        _ = request, obj  # 忽略未使用参数
        return False


# 自定义Admin站点标题
admin.site.site_header = '威胁情报数据中心'
admin.site.site_title = '管理后台'
admin.site.index_title = '系统管理'