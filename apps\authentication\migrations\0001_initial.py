# Generated by Django 5.2.3 on 2025-07-03 15:26

import django.contrib.auth.models
import django.contrib.auth.validators
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="VerificationCode",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "phone",
                    models.CharField(
                        help_text="接收验证码的手机号",
                        max_length=11,
                        verbose_name="手机号",
                    ),
                ),
                (
                    "code",
                    models.Char<PERSON><PERSON>(
                        help_text="6位数字验证码", max_length=6, verbose_name="验证码"
                    ),
                ),
                (
                    "code_type",
                    models.CharField(
                        choices=[
                            ("register", "注册验证码"),
                            ("login", "登录验证码"),
                            ("reset_password", "重置密码验证码"),
                        ],
                        help_text="验证码的用途类型",
                        max_length=20,
                        verbose_name="验证码类型",
                    ),
                ),
                ("expires_at", models.DateTimeField(verbose_name="过期时间")),
                (
                    "is_used",
                    models.BooleanField(default=False, verbose_name="是否已使用"),
                ),
            ],
            options={
                "verbose_name": "验证码",
                "verbose_name_plural": "验证码",
                "db_table": "auth_verification_code",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="用户的手机号码",
                        max_length=11,
                        null=True,
                        unique=True,
                        verbose_name="手机号",
                    ),
                ),
                (
                    "avatar",
                    models.URLField(
                        blank=True,
                        help_text="用户头像的URL地址",
                        null=True,
                        verbose_name="头像",
                    ),
                ),
                (
                    "department",
                    models.CharField(
                        blank=True,
                        help_text="用户所属部门",
                        max_length=100,
                        null=True,
                        verbose_name="部门",
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        blank=True,
                        help_text="用户的个人简介",
                        null=True,
                        verbose_name="个人简介",
                    ),
                ),
                (
                    "agree_newsletter",
                    models.BooleanField(
                        default=False,
                        help_text="是否同意接收威胁情报更新和安全资讯",
                        verbose_name="同意邮件通知",
                    ),
                ),
                (
                    "last_login_method",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("account", "账号密码"),
                            ("phone", "手机验证码"),
                            ("wechat", "微信登录"),
                        ],
                        help_text="用户最后一次登录使用的方式",
                        max_length=20,
                        null=True,
                        verbose_name="最后登录方式",
                    ),
                ),
                (
                    "wechat_openid",
                    models.CharField(
                        blank=True,
                        help_text="微信登录的OpenID",
                        max_length=100,
                        null=True,
                        unique=True,
                        verbose_name="微信OpenID",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户",
                "verbose_name_plural": "用户",
                "db_table": "auth_user",
            },
            managers=[
                ("objects", django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
