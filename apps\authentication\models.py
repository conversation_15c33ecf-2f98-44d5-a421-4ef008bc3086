"""
用户认证相关的数据模型
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from config.models import BaseModel
import random
import string


class User(AbstractUser,BaseModel):
    """
    扩展的用户模型
    继承Django的AbstractUser，添加额外的字段
    """

    # 用户昵称
    nickname = models.CharField(
        max_length=12,
        null=True,
        blank=True,
        verbose_name='昵称',
        help_text='用户的昵称'
    )

    # 手机号字段
    phone = models.CharField(
        max_length=11,
        unique=True,
        null=True,
        blank=True,
        verbose_name='手机号',
        help_text='用户的手机号码'
    )
    
    # 头像字段
    avatar = models.ImageField(
        upload_to='avatars/',
        null=True,
        blank=True,
        verbose_name='头像',
        help_text='用户头像图片'
    )

    # 个人简介
    bio = models.TextField(
        null=True,
        blank=True,
        verbose_name='个人简介',
        help_text='用户的个人简介'
    )

    # 最后登录方式
    last_login_method = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        choices=[
            ('account', '账号密码'),
            ('phone', '手机验证码'),
            ('wechat', '微信登录'),
        ],
        verbose_name='最后登录方式',
        help_text='用户最后一次登录使用的方式'
    )
    
    # 微信OpenID
    wechat_openid = models.CharField(
        max_length=100,
        unique=True,
        null=True,
        blank=True,
        verbose_name='微信OpenID',
        help_text='微信登录的OpenID'
    )

    # 企业名称
    company_name = models.CharField(
        max_length=100,
        verbose_name='企业名称',
        help_text='用户所属企业或组织名称'
    )

    # 邮件通知设置
    agree_newsletter = models.BooleanField(
        default=False,
        verbose_name='同意邮件通知',
        help_text='是否同意接收威胁情报更新和安全资讯'
    )
    
    class Meta:
        verbose_name = '用户'
        verbose_name_plural = '用户'
        db_table = 'auth_user'
    
    def __str__(self):
        return self.username


class VerificationCode(BaseModel):
    """
    验证码模型
    用于存储手机验证码和邮箱验证码
    """
    CODE_TYPE_CHOICES = [
        ('register', '注册验证码'),
        ('login', '登录验证码'),
        ('reset_password', '重置密码验证码'),
    ]

    # 最大验证失败次数
    MAX_FAILED_ATTEMPTS = 3

    phone = models.CharField(
        max_length=11,
        verbose_name='手机号',
        help_text='接收验证码的手机号'
    )

    code = models.CharField(
        max_length=6,
        verbose_name='验证码',
        help_text='6位数字验证码'
    )

    code_type = models.CharField(
        max_length=20,
        choices=CODE_TYPE_CHOICES,
        verbose_name='验证码类型',
        help_text='验证码的用途类型'
    )

    expires_at = models.DateTimeField(
        verbose_name='过期时间'
    )

    is_used = models.BooleanField(
        default=False,
        verbose_name='是否已使用'
    )

    failed_attempts = models.PositiveIntegerField(
        default=0,
        verbose_name='验证失败次数',
        help_text='验证码验证失败的次数，达到3次后验证码将失效'
    )

    class Meta:
        verbose_name = '验证码'
        verbose_name_plural = '验证码'
        db_table = 'auth_verification_code'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.phone} - {self.code} ({dict(self.CODE_TYPE_CHOICES).get(self.code_type, self.code_type)})'
    
    @classmethod
    def generate_code(cls, phone, code_type, expires_minutes=5):
        """
        生成验证码
        
        Args:
            phone: 手机号
            code_type: 验证码类型
            expires_minutes: 过期时间（分钟）
        
        Returns:
            VerificationCode: 验证码实例
        """
        # 生成6位随机数字验证码
        code = ''.join(random.choices(string.digits, k=6))
        
        # 计算过期时间
        expires_at = timezone.now() + timezone.timedelta(minutes=expires_minutes)
        
        # 创建验证码记录
        verification_code = cls.objects.create(
            phone=phone,
            code=code,
            code_type=code_type,
            expires_at=expires_at
        )
        
        return verification_code
    
    def is_valid(self):
        """
        检查验证码是否有效

        Returns:
            bool: 验证码是否有效
        """
        return (
            not self.is_used and
            timezone.now() < self.expires_at and
            self.failed_attempts < self.MAX_FAILED_ATTEMPTS
        )
    
    def mark_as_used(self):
        """
        标记验证码为已使用
        """
        self.is_used = True
        self.save()

    def increment_failed_attempts(self):
        """
        增加验证失败次数
        """
        self.failed_attempts += 1
        self.save()
