"""
用户认证相关的序列化器 - 只保留登录功能
"""

import re
from rest_framework import serializers
from django.contrib.auth import get_user_model
from utils import exceptions

User = get_user_model()


class UserLoginSerializer(serializers.Serializer):
    """
    用户登录序列化器
    """

    username = serializers.CharField(max_length=150, help_text="用户名")
    password = serializers.CharField(write_only=True, help_text="密码")

    def validate_username(self, value):
        """验证用户名"""
        if not value.strip():
            raise exceptions.CustomException("用户名不能为空")
        return value.strip()

    def validate_password(self, value):
        """验证密码"""
        if not value:
            raise exceptions.CustomException("密码不能为空")
        return value


# 移除了微信登录和用户注册响应序列化器


class JWTTokenResponseSerializer(serializers.Serializer):
    """JWT Token响应序列化器"""

    access_token = serializers.Char<PERSON>ield(help_text="访问Token")
    refresh_token = serializers.CharField(help_text="刷新Token")
    token_type = serializers.CharField(help_text="Token类型", default="Bearer")
    expires_in = serializers.IntegerField(help_text="Token过期时间（秒）")


class UserLoginResponseSerializer(serializers.Serializer):
    """用户登录响应序列化器"""

    access_token = serializers.CharField(help_text="访问Token")
    refresh_token = serializers.CharField(help_text="刷新Token")
    token_type = serializers.CharField(help_text="Token类型", default="Bearer")
    expires_in = serializers.IntegerField(help_text="Token过期时间（秒）")
    user = serializers.DictField(help_text="用户基本信息")

    class UserInfo(serializers.Serializer):
        """用户基本信息"""

        id = serializers.IntegerField(help_text="用户ID")
        username = serializers.CharField(help_text="用户名")
        email = serializers.EmailField(help_text="邮箱地址", allow_blank=True)
        avatar = serializers.CharField(
            help_text="用户头像URL", allow_blank=True, allow_null=True
        )


class PhoneLoginSerializer(serializers.Serializer):
    """
    手机号登录序列化器
    """

    phone = serializers.CharField(max_length=11, min_length=11, help_text="手机号码")
    verification_code = serializers.CharField(max_length=6, min_length=6, help_text="验证码")

    def validate_phone(self, value):
        """验证手机号"""
        # 使用正则验证手机号
        if not re.match(r"^1[3-9]\d{9}$", value):
            raise exceptions.CustomException("请输入有效的手机号码")
        return value.strip()

    def validate_verification_code(self, value):
        """验证验证码"""
        if not value.strip():
            raise exceptions.CustomException("验证码不能为空")
        if not value.isdigit():
            raise exceptions.CustomException("验证码必须是数字")
        return value.strip()


class SendVerificationCodeSerializer(serializers.Serializer):
    """
    发送验证码序列化器
    """

    phone = serializers.CharField(max_length=11, min_length=11, help_text="手机号码")
    code_type = serializers.ChoiceField(
        choices=["register", "login", "reset_password"],
        help_text="验证码类型：register-注册，login-登录，reset_password-重置密码",
    )

    def validate_phone(self, value):
        """验证手机号"""

        # 使用正则验证手机号
        if not re.match(r"^1[3-9]\d{9}$", value):
            raise exceptions.CustomException("请输入有效的手机号码")

        return value.strip()


class UserProfileSerializer(serializers.ModelSerializer):
    """
    用户信息序列化器
    """
    class Meta:
        model = User
        fields = [
            'id', 'username', 'nickname', 'email', 'phone', 'company_name',
            'avatar', 'bio', 'is_active', 'last_login_method', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'username', 'is_active', 'date_joined', 'last_login', 'last_login_method']

    def to_representation(self, instance):
        """自定义输出格式"""
        data = super().to_representation(instance)
        # 处理头像URL
        if instance.avatar:
            if hasattr(instance.avatar, "url"):
                data['avatar'] = instance.avatar.url
            else:
                data['avatar'] = str(instance.avatar)
        else:
            data['avatar'] = None
        return data


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """
    用户信息更新序列化器
    """
    class Meta:
        model = User
        fields = ['nickname', 'email', 'phone', 'avatar', 'bio']
        extra_kwargs = {
            'nickname': {'required': False},
            'email': {'required': False},
            'phone': {'required': False},
            'avatar': {'required': False},
            'bio': {'required': False},
        }

    def validate_phone(self, value):
        """验证手机号"""
        if value:
            # 使用正则验证手机号
            if not re.match(r"^1[3-9]\d{9}$", value):
                raise exceptions.CustomException("请输入有效的手机号码")

            # 检查手机号是否已被其他用户使用
            if User.objects.filter(phone=value).exclude(id=self.instance.id).exists():
                raise exceptions.CustomException("手机号已被其他用户使用")

        return value

    def validate_email(self, value):
        """验证邮箱"""
        if value:
            # 检查邮箱是否已被其他用户使用
            if User.objects.filter(email=value).exclude(id=self.instance.id).exists():
                raise exceptions.CustomException("邮箱已被其他用户使用")

        return value


class UserRegisterSerializer(serializers.Serializer):
    """
    用户注册序列化器
    """
    username = serializers.CharField(max_length=150, help_text="用户名")
    company_name = serializers.CharField(max_length=100, help_text="企业名称")
    phone = serializers.CharField(max_length=11, min_length=11, help_text="手机号码")
    verification_code = serializers.CharField(max_length=6, min_length=6, help_text="验证码")
    password = serializers.CharField(min_length=8, max_length=128, help_text="密码")
    confirm_password = serializers.CharField(min_length=8, max_length=128, help_text="确认密码")
    agree_terms = serializers.BooleanField(help_text="同意服务条款")
    agree_newsletter = serializers.BooleanField(default=False, help_text="同意接收邮件通知")

    def validate_username(self, value):
        """验证用户名"""
        if not value.strip():
            raise exceptions.CustomException("用户名不能为空")

        # 检查用户名是否已存在
        if User.objects.filter(username=value.strip()).exists():
            raise exceptions.CustomException("用户名已存在")

        return value.strip()

    def validate_company_name(self, value):
        """验证企业名称"""
        if not value.strip():
            raise exceptions.CustomException("企业名称不能为空")

        if len(value.strip()) < 2:
            raise exceptions.CustomException("企业名称至少2个字符")

        if len(value.strip()) > 100:
            raise exceptions.CustomException("企业名称不能超过100个字符")

        return value.strip()

    def validate_phone(self, value):
        """验证手机号"""
        # 使用正则验证手机号
        if not re.match(r"^1[3-9]\d{9}$", value):
            raise exceptions.CustomException("请输入有效的手机号码")

        # 检查手机号是否已存在
        if User.objects.filter(phone=value.strip()).exists():
            raise exceptions.CustomException("手机号已被注册")

        return value.strip()

    def validate_verification_code(self, value):
        """验证验证码"""
        if not value.strip():
            raise exceptions.CustomException("验证码不能为空")
        if not value.isdigit():
            raise exceptions.CustomException("验证码必须是数字")
        return value.strip()

    def validate_password(self, value):
        """验证密码"""
        if len(value) < 8:
            raise exceptions.CustomException("密码长度不能少于8位")
        return value

    def validate_agree_terms(self, value):
        """验证同意条款"""
        if not value:
            raise exceptions.CustomException("必须同意服务条款")
        return value

    def validate(self, attrs):
        """验证密码确认"""
        if attrs['password'] != attrs['confirm_password']:
            raise exceptions.CustomException("两次输入的密码不一致")
        return attrs


class ChangePasswordSerializer(serializers.Serializer):
    """
    修改密码序列化器
    """
    old_password = serializers.CharField(write_only=True, help_text="当前密码")
    new_password = serializers.CharField(min_length=8, max_length=128, write_only=True, help_text="新密码")
    confirm_password = serializers.CharField(min_length=8, max_length=128, write_only=True, help_text="确认新密码")

    def validate_old_password(self, value):
        """验证当前密码"""
        if not value:
            raise exceptions.CustomException("当前密码不能为空")
        return value

    def validate_new_password(self, value):
        """验证新密码"""
        if not value:
            raise exceptions.CustomException("新密码不能为空")
        if len(value) < 8:
            raise exceptions.CustomException("新密码长度不能少于8位")
        return value

    def validate_confirm_password(self, value):
        """验证确认密码"""
        if not value:
            raise exceptions.CustomException("确认密码不能为空")
        return value

    def validate(self, attrs):
        """验证密码一致性和安全性"""
        old_password = attrs.get('old_password')
        new_password = attrs.get('new_password')
        confirm_password = attrs.get('confirm_password')

        # 验证新密码和确认密码是否一致
        if new_password != confirm_password:
            raise exceptions.CustomException("新密码和确认密码不一致")

        # 验证新密码不能与旧密码相同
        if old_password == new_password:
            raise exceptions.CustomException("新密码不能与当前密码相同")

        return attrs
