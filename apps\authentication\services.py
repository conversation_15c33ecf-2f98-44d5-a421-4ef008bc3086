"""
用户认证相关的业务逻辑服务
"""

import logging
from django.utils import timezone
from django.conf import settings
from .models import VerificationCode, User
import requests
import json
from alibabacloud_dysmsapi20170525.client import Client as DysmsapiClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
from alibabacloud_tea_util import models as util_models

logger = logging.getLogger(__name__)


class SMSService:
    """
    阿里云短信服务类

    用于发送验证码短信
    """

    @staticmethod
    def _create_client():
        """
        创建阿里云短信客户端

        Returns:
            DysmsapiClient: 阿里云短信客户端
        """
        config = open_api_models.Config(
            access_key_id=getattr(settings, "ALIYUN_ACCESS_KEY_ID", ""),
            access_key_secret=getattr(settings, "ALIYUN_ACCESS_KEY_SECRET", ""),
        )
        # 短信服务的endpoint
        config.endpoint = "dysmsapi.aliyuncs.com"
        return DysmsapiClient(config)

    @staticmethod
    def send_verification_code(phone, code, code_type):
        """
        发送验证码短信

        Args:
            phone: 手机号
            code: 验证码
            code_type: 验证码类型

        Returns:
            bool: 发送是否成功
        """
        try:
            # 在开发环境下，直接返回成功并打印验证码
            if settings.DEBUG:
                logger.info(
                    f"[开发模式] 发送验证码到 {phone}: {code} (类型: {code_type})"
                )
                print(f"[SMS] 验证码已发送到 {phone}: {code}")
                return True

            # 生产环境下，调用阿里云短信服务
            client = SMSService._create_client()

            # 根据验证码类型选择短信模板
            template_code_map = {
                "register": getattr(
                    settings, "ALIYUN_SMS_TEMPLATE_REGISTER", "SMS_123456789"
                ),
                "login": getattr(
                    settings, "ALIYUN_SMS_TEMPLATE_LOGIN", "SMS_123456789"
                ),
                "reset_password": getattr(
                    settings, "ALIYUN_SMS_TEMPLATE_RESET", "SMS_123456789"
                ),
            }

            template_code = template_code_map.get(
                code_type, template_code_map["register"]
            )

            # 构建发送短信请求
            send_sms_request = dysmsapi_models.SendSmsRequest(
                phone_numbers=phone,
                sign_name=getattr(settings, "ALIYUN_SMS_SIGN_NAME", "威胁情报数据中心"),
                template_code=template_code,
                template_param=json.dumps({"code": code}),
            )

            # 发送短信
            runtime = util_models.RuntimeOptions()
            response = client.send_sms_with_options(send_sms_request, runtime)

            # 检查发送结果
            if response.body.code == "OK":
                logger.info(f"短信发送成功: {phone}")
                return True
            else:
                logger.error(
                    f"短信发送失败: {response.body.code} - {response.body.message}"
                )
                return False

        except Exception as e:
            logger.error(f"发送短信验证码异常: {e}")
            return False


class VerificationCodeService:
    """
    验证码服务类
    """

    @staticmethod
    def send_code(phone, code_type):
        """
        发送验证码

        Args:
            phone: 手机号
            code_type: 验证码类型

        Returns:
            tuple: (是否成功, 错误信息)
        """
        try:
            # 检查发送频率限制（1分钟内只能发送一次）
            one_minute_ago = timezone.now() - timezone.timedelta(minutes=1)
            recent_code = VerificationCode.objects.filter(
                phone=phone, code_type=code_type, created_at__gte=one_minute_ago
            ).first()

            if recent_code:
                return False, "验证码发送过于频繁，请稍后再试"

            # 生成验证码
            verification_code = VerificationCode.generate_code(phone, code_type)

            # 发送短信
            sms_sent = SMSService.send_verification_code(
                phone, verification_code.code, code_type
            )

            if not sms_sent:
                # 如果短信发送失败，删除验证码记录
                verification_code.delete()
                return False, "验证码发送失败，请稍后重试"

            return True, "验证码发送成功"

        except Exception as e:
            logger.error(f"发送验证码服务异常: {e}")
            return False, "系统异常，请稍后重试"

    @staticmethod
    def verify_code(phone, code, code_type):
        """
        验证验证码

        Args:
            phone: 手机号
            code: 验证码
            code_type: 验证码类型

        Returns:
            tuple: (是否有效, 错误信息, 验证码对象)
        """
        try:
            # 查找最新的验证码（不按code过滤，因为我们需要跟踪失败次数）
            verification_code = VerificationCode.objects.filter(
                phone=phone, code_type=code_type
            ).latest("created_at")

            # 检查验证码是否已达到最大失败次数
            if verification_code.failed_attempts >= VerificationCode.MAX_FAILED_ATTEMPTS:
                logger.warning(f"验证码已达到最大失败次数 - 手机号: {phone}, 失败次数: {verification_code.failed_attempts}")
                return False, "验证码已失效，请重新获取", None

            # 检查验证码是否过期或已使用（不包括失败次数检查，因为上面已经检查了）
            if verification_code.is_used:
                return False, "验证码已使用", None

            if timezone.now() >= verification_code.expires_at:
                return False, "验证码已过期", None

            # 验证验证码是否匹配
            if verification_code.code != code:
                # 增加失败次数
                verification_code.increment_failed_attempts()
                logger.warning(f"验证码验证失败 - 手机号: {phone}, 失败次数: {verification_code.failed_attempts}")

                # 检查是否达到最大失败次数
                if verification_code.failed_attempts >= VerificationCode.MAX_FAILED_ATTEMPTS:
                    logger.warning(f"验证码达到最大失败次数，已失效 - 手机号: {phone}")
                    return False, "验证码已失效，请重新获取", None
                else:
                    return False, "验证码不正确", None

            return True, "验证码有效", verification_code

        except VerificationCode.DoesNotExist:
            return False, "验证码不正确", None
        except Exception as e:
            logger.error(f"验证码验证异常: {e}")
            return False, "系统异常，请稍后重试", None


class WechatService:
    """
    微信登录服务类
    """

    @staticmethod
    def get_access_token(code):
        """
        通过授权码获取访问令牌

        Args:
            code: 微信授权码

        Returns:
            dict: 包含access_token和openid的字典，失败时返回None
        """
        try:
            # 微信OAuth2.0获取access_token的API
            url = "https://api.weixin.qq.com/sns/oauth2/access_token"
            params = {
                "appid": getattr(settings, "WECHAT_APP_ID", ""),
                "secret": getattr(settings, "WECHAT_APP_SECRET", ""),
                "code": code,
                "grant_type": "authorization_code",
            }

            response = requests.get(url, params=params)
            result = response.json()

            if "access_token" in result and "openid" in result:
                return result
            else:
                logger.error(f"微信获取access_token失败: {result}")
                return None

        except Exception as e:
            logger.error(f"微信登录服务异常: {e}")
            return None

    @staticmethod
    def get_user_info(access_token, openid):
        """
        获取微信用户信息

        Args:
            access_token: 访问令牌
            openid: 用户openid

        Returns:
            dict: 用户信息，失败时返回None
        """
        try:
            url = "https://api.weixin.qq.com/sns/userinfo"
            params = {"access_token": access_token, "openid": openid, "lang": "zh_CN"}

            response = requests.get(url, params=params)
            result = response.json()

            if "errcode" not in result:
                return result
            else:
                logger.error(f"微信获取用户信息失败: {result}")
                return None

        except Exception as e:
            logger.error(f"获取微信用户信息异常: {e}")
            return None

    @staticmethod
    def login_or_register(code):
        """
        微信登录或注册

        Args:
            code: 微信授权码

        Returns:
            tuple: (用户对象, 是否为新用户, 错误信息)
        """
        try:
            # 获取access_token
            token_result = WechatService.get_access_token(code)
            if not token_result:
                return None, False, "微信授权失败"

            access_token = token_result["access_token"]
            openid = token_result["openid"]

            # 检查用户是否已存在
            try:
                user = User.objects.get(wechat_openid=openid)
                # 更新最后登录方式
                user.last_login_method = "wechat"
                user.save()
                return user, False, None
            except User.DoesNotExist:
                pass

            # 获取微信用户信息
            user_info = WechatService.get_user_info(access_token, openid)
            if not user_info:
                return None, False, "获取微信用户信息失败"

            # 创建新用户
            username = f"wx_{openid[:10]}"  # 使用openid前10位作为用户名
            nickname = user_info.get("nickname", "微信用户")
            avatar = user_info.get("headimgurl", "")

            # 确保用户名唯一
            counter = 1
            original_username = username
            while User.objects.filter(username=username).exists():
                username = f"{original_username}_{counter}"
                counter += 1

            user = User.objects.create_user(
                username=username,
                nickname=nickname,
                avatar=avatar,
                wechat_openid=openid,
                last_login_method="wechat",
            )

            return user, True, None

        except Exception as e:
            logger.error(f"微信登录或注册异常: {e}")
            return None, False, "系统异常，请稍后重试"


class PhoneLoginService:
    """
    手机号登录服务类
    """

    @staticmethod
    def login_with_phone(phone, verification_code):
        """
        使用手机号和验证码登录

        Args:
            phone: 手机号
            verification_code: 验证码

        Returns:
            tuple: (用户对象, 是否为新用户, 错误信息)
        """
        try:
            # 验证验证码
            is_valid, message, code_obj = VerificationCodeService.verify_code(
                phone, verification_code, "login"
            )

            if not is_valid:
                return None, False, message

            # 检查验证码对象是否存在
            if code_obj is None:
                return None, False, "验证码验证失败"

            # 标记验证码为已使用
            code_obj.is_used = True
            code_obj.save()

            # 查找用户
            try:
                user = User.objects.get(phone=phone)
            except User.DoesNotExist:
                # 用户不存在，返回错误
                return None, False, "用户不存在，请先注册"

            # 更新最后登录方式
            user.last_login_method = "phone"
            user.save()

            return user, False, None

        except Exception as e:
            logger.error(f"手机号登录异常: {e}")
            return None, False, "系统异常，请稍后重试"


class UserRegisterService:
    """
    用户注册服务类
    """

    @staticmethod
    def register_user(username, company_name, phone, verification_code, password, agree_newsletter=False):
        """
        用户注册

        Args:
            username: 用户名
            company_name: 企业名称
            phone: 手机号
            verification_code: 验证码
            password: 密码
            agree_newsletter: 是否同意接收邮件通知

        Returns:
            tuple: (用户对象, 错误信息)
        """
        try:
            # 验证验证码
            is_valid, message, code_obj = VerificationCodeService.verify_code(
                phone, verification_code, 'register'
            )

            if not is_valid:
                return None, message

            # 检查验证码对象是否存在
            if code_obj is None:
                return None, "验证码验证失败"

            # 创建用户
            user = User.objects.create_user(
                username=username,
                phone=phone,
                password=password,
                last_login_method='account'
            )

            # 设置企业名称和账号状态
            user.company_name = company_name
            user.is_active = False  # 新注册用户默认为待审核状态
            user.agree_newsletter = agree_newsletter
            user.save()

            # 标记验证码为已使用
            code_obj.is_used = True
            code_obj.save()

            logger.info(f"用户注册成功 - 用户名: {username}, 手机号: {phone}")

            return user, None

        except Exception as e:
            logger.error(f"用户注册异常: {e}")
            return None, "系统异常，请稍后重试"


class UserService:
    """
    用户服务类
    """

    @staticmethod
    def check_username_available(username):
        """
        检查用户名是否可用

        Args:
            username: 用户名

        Returns:
            bool: 是否可用
        """
        return not User.objects.filter(username=username).exists()

    @staticmethod
    def check_phone_available(phone):
        """
        检查手机号是否可用

        Args:
            phone: 手机号

        Returns:
            bool: 是否可用
        """
        return not User.objects.filter(phone=phone).exists()
