"""
用户认证应用的测试用例
"""

from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from unittest.mock import patch
from .models import VerificationCode
from .services import VerificationCodeService, PhoneLoginService, UserRegisterService

User = get_user_model()


class VerificationCodeModelTest(TestCase):
    """验证码模型测试"""

    def setUp(self):
        """测试前准备"""
        self.phone = "13800138000"
        self.code_type = "login"

    def test_generate_code(self):
        """测试生成验证码"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        
        self.assertEqual(code.phone, self.phone)
        self.assertEqual(code.code_type, self.code_type)
        self.assertEqual(len(code.code), 6)
        self.assertTrue(code.code.isdigit())
        self.assertEqual(code.failed_attempts, 0)
        self.assertFalse(code.is_used)
        self.assertTrue(code.is_valid())

    def test_is_valid_with_failed_attempts(self):
        """测试验证码有效性检查（包含失败次数）"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        
        # 初始状态应该有效
        self.assertTrue(code.is_valid())
        
        # 增加失败次数但未达到最大值
        code.failed_attempts = 2
        code.save()
        self.assertTrue(code.is_valid())
        
        # 达到最大失败次数
        code.failed_attempts = VerificationCode.MAX_FAILED_ATTEMPTS
        code.save()
        self.assertFalse(code.is_valid())

    def test_increment_failed_attempts(self):
        """测试增加失败次数"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        
        initial_attempts = code.failed_attempts
        code.increment_failed_attempts()
        
        self.assertEqual(code.failed_attempts, initial_attempts + 1)

    def test_mark_as_used(self):
        """测试标记为已使用"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        
        self.assertFalse(code.is_used)
        code.mark_as_used()
        self.assertTrue(code.is_used)
        self.assertFalse(code.is_valid())


class VerificationCodeServiceTest(TestCase):
    """验证码服务测试"""

    def setUp(self):
        """测试前准备"""
        self.phone = "13800138000"
        self.code_type = "login"

    def tearDown(self):
        """测试后清理"""
        VerificationCode.objects.filter(phone=self.phone).delete()

    @patch('apps.authentication.services.SMSService.send_verification_code')
    def test_send_code_success(self, mock_sms):
        """测试发送验证码成功"""
        mock_sms.return_value = True
        
        success, message = VerificationCodeService.send_code(self.phone, self.code_type)
        
        self.assertTrue(success)
        self.assertEqual(message, "验证码发送成功")
        self.assertTrue(VerificationCode.objects.filter(phone=self.phone).exists())

    def test_verify_code_success(self):
        """测试验证码验证成功"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        
        is_valid, message, code_obj = VerificationCodeService.verify_code(
            self.phone, code.code, self.code_type
        )
        
        self.assertTrue(is_valid)
        self.assertEqual(message, "验证码有效")
        self.assertEqual(code_obj.id, code.id)

    def test_verify_code_incorrect(self):
        """测试验证码不正确"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        
        is_valid, message, code_obj = VerificationCodeService.verify_code(
            self.phone, "000000", self.code_type
        )
        
        self.assertFalse(is_valid)
        self.assertEqual(message, "验证码不正确")
        self.assertIsNone(code_obj)
        
        # 检查失败次数是否增加
        code.refresh_from_db()
        self.assertEqual(code.failed_attempts, 1)

    def test_verify_code_max_failed_attempts(self):
        """测试验证码达到最大失败次数"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        
        # 连续3次错误验证
        for i in range(3):
            is_valid, message, code_obj = VerificationCodeService.verify_code(
                self.phone, "000000", self.code_type
            )
            self.assertFalse(is_valid)
            
            code.refresh_from_db()
            if i < 2:  # 前两次应该返回"验证码不正确"
                self.assertEqual(message, "验证码不正确")
            else:  # 第三次应该返回"验证码已失效"
                self.assertEqual(message, "验证码已失效，请重新获取")
        
        # 验证失败次数
        self.assertEqual(code.failed_attempts, 3)
        
        # 即使输入正确验证码也应该失败
        is_valid, message, code_obj = VerificationCodeService.verify_code(
            self.phone, code.code, self.code_type
        )
        self.assertFalse(is_valid)
        self.assertEqual(message, "验证码已失效，请重新获取")

    def test_verify_code_not_exist(self):
        """测试验证码不存在"""
        is_valid, message, code_obj = VerificationCodeService.verify_code(
            self.phone, "123456", self.code_type
        )
        
        self.assertFalse(is_valid)
        self.assertEqual(message, "验证码不正确")
        self.assertIsNone(code_obj)

    def test_verify_code_expired(self):
        """测试验证码过期"""
        # 创建一个已过期的验证码
        expired_time = timezone.now() - timezone.timedelta(minutes=10)
        code = VerificationCode.objects.create(
            phone=self.phone,
            code="123456",
            code_type=self.code_type,
            expires_at=expired_time
        )
        
        is_valid, message, code_obj = VerificationCodeService.verify_code(
            self.phone, code.code, self.code_type
        )
        
        self.assertFalse(is_valid)
        self.assertEqual(message, "验证码已过期")
        self.assertIsNone(code_obj)

    def test_verify_code_already_used(self):
        """测试验证码已使用"""
        code = VerificationCode.generate_code(self.phone, self.code_type)
        code.mark_as_used()
        
        is_valid, message, code_obj = VerificationCodeService.verify_code(
            self.phone, code.code, self.code_type
        )
        
        self.assertFalse(is_valid)
        self.assertEqual(message, "验证码已使用")
        self.assertIsNone(code_obj)


class PhoneLoginServiceTest(TestCase):
    """手机号登录服务测试"""

    def setUp(self):
        """测试前准备"""
        self.phone = "13800138000"
        self.user = User.objects.create_user(
            username="testuser",
            phone=self.phone,
            password="testpass123"
        )

    def tearDown(self):
        """测试后清理"""
        VerificationCode.objects.filter(phone=self.phone).delete()
        User.objects.filter(phone=self.phone).delete()

    def test_login_with_phone_success(self):
        """测试手机号登录成功"""
        code = VerificationCode.generate_code(self.phone, "login")
        
        user, is_new, error = PhoneLoginService.login_with_phone(
            self.phone, code.code
        )
        
        self.assertEqual(user.id, self.user.id)
        self.assertFalse(is_new)
        self.assertIsNone(error)
        
        # 验证码应该被标记为已使用
        code.refresh_from_db()
        self.assertTrue(code.is_used)

    def test_login_with_phone_invalid_code(self):
        """测试手机号登录验证码错误"""
        VerificationCode.generate_code(self.phone, "login")
        
        user, is_new, error = PhoneLoginService.login_with_phone(
            self.phone, "000000"
        )
        
        self.assertIsNone(user)
        self.assertFalse(is_new)
        self.assertEqual(error, "验证码不正确")

    def test_login_with_phone_user_not_exist(self):
        """测试手机号登录用户不存在"""
        phone = "13900139000"
        code = VerificationCode.generate_code(phone, "login")
        
        user, is_new, error = PhoneLoginService.login_with_phone(
            phone, code.code
        )
        
        self.assertIsNone(user)
        self.assertFalse(is_new)
        self.assertEqual(error, "用户不存在，请先注册")
        
        # 清理测试数据
        VerificationCode.objects.filter(phone=phone).delete()
