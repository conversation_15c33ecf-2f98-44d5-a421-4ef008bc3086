"""
用户认证应用的URL配置
"""
from django.urls import path
from .views import TokenCreateView, TokenRefreshView, SendVerificationCodeView, PhoneLoginView, UserProfileView, UserRegisterView, ChangePasswordView

# URL模式 - 登录、Token刷新和验证码功能
urlpatterns = [
    # 认证相关路由
    path('token/login/', TokenCreateView.as_view(), name='token_login'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('phone-login/', PhoneLoginView.as_view(), name='phone_login'),
    path('register/', UserRegisterView.as_view(), name='user_register'),
    # 用户信息相关路由
    path('users/me/', UserProfileView.as_view(), name='user_profile'),
    path('users/set_password/', ChangePasswordView.as_view(), name='change_password'),
    # 验证码相关路由
    path('send-verification-code/', SendVerificationCodeView.as_view(), name='send_verification_code'),
]

# 可用的API端点：
# POST  /api/v1/auth/token/login/ - 用户登录（JWT）
# POST  /api/v1/auth/token/refresh/ - Token刷新（JWT）
# POST  /api/v1/auth/phone-login/ - 手机号登录
# POST  /api/v1/auth/register/ - 用户注册
# GET   /api/v1/auth/users/me/ - 获取当前用户信息
# PATCH /api/v1/auth/users/me/ - 更新当前用户信息
# POST  /api/v1/auth/users/set_password/ - 修改密码
# POST  /api/v1/auth/send-verification-code/ - 发送验证码
