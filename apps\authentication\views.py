"""
用户认证相关视图
"""

import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_spectacular.utils import extend_schema, OpenApiResponse
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

from utils.response import StandardResponseSerializer, StandardResponse
from .serializers import (
    UserLoginSerializer,
    UserLoginResponseSerializer,
    JWTTokenResponseSerializer,
    SendVerificationCodeSerializer,
    PhoneLoginSerializer,
    UserProfileSerializer,
    UserProfileUpdateSerializer,
    UserRegisterSerializer,
    ChangePasswordSerializer,
)

# 导入验证码服务
from .services import VerificationCodeService, PhoneLoginService, UserRegisterService

logger = logging.getLogger(__name__)


@extend_schema(
    tags=["用户认证"],
    summary="用户登录",
    description="用户登录接口，使用用户名和密码进行身份验证。",
    request=UserLoginSerializer,
    responses={
        200: OpenApiResponse(
            response=UserLoginResponseSerializer,
            description="登录成功，返回JWT Token和用户信息",
        ),
        400: OpenApiResponse(
            response=StandardResponseSerializer,
            description="请求参数错误或登录凭据无效",
        ),
    },
)
@method_decorator(csrf_exempt, name="dispatch")
class TokenCreateView(APIView):
    """用户登录视图"""

    permission_classes = [AllowAny]

    def post(self, request: Request) -> Response:
        # 使用序列化器验证请求数据
        serializer = UserLoginSerializer(data=request.data)
        if not serializer.is_valid():
            return StandardResponse.validation_error(errors=serializer.errors)

        username: str = serializer.validated_data["username"]  # type: ignore
        password: str = serializer.validated_data["password"]  # type: ignore

        logger.info(f"登录请求 - 用户名: {username}")

        # 先检查用户是否存在
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            logger.warning(f"登录失败 - 用户名不存在: {username}")
            return StandardResponse.error(message="用户名或密码错误")

        # 检查密码
        if not user.check_password(password):
            logger.warning(f"登录失败 - 密码错误: {username}")
            return StandardResponse.error(message="用户名或密码错误")

        # 检查账号状态
        if not user.is_active:
            return StandardResponse.error(message="您的账号正在审核中，请耐心等待。审核通过后您将收到通知并可正常登录。")

        # 生成JWT Token
        refresh = RefreshToken.for_user(user)
        expires_in = int(settings.SIMPLE_JWT["ACCESS_TOKEN_LIFETIME"].total_seconds())

        logger.info(f"登录成功 - 用户: {user.username}")

        # 处理头像URL
        avatar_url = None
        if hasattr(user, 'avatar') and user.avatar:  # type: ignore
            # 如果头像字段有值，获取完整的URL
            if hasattr(user.avatar, "url"):  # type: ignore
                avatar_url = user.avatar.url  # type: ignore
            else:
                # 如果是字符串类型（URL字段），直接使用
                avatar_url = str(user.avatar)  # type: ignore

        return StandardResponse.success(
            data={
                "access_token": str(refresh.access_token),
                "refresh_token": str(refresh),
                "token_type": "Bearer",
                "expires_in": expires_in,
                "user": {
                    "id": user.pk,
                    "username": user.username,
                    "nickname": user.nickname,  # type: ignore
                    "email": user.email,
                    "avatar": avatar_url,
                },
            },
            message="登录成功",
        )


@extend_schema(
    tags=["用户认证"],
    summary="刷新Token",
    description="使用刷新Token获取新的访问Token",
    responses={
        200: OpenApiResponse(
            response=JWTTokenResponseSerializer, description="Token刷新成功"
        ),
        400: OpenApiResponse(
            response=StandardResponseSerializer, description="刷新Token无效"
        ),
    },
)
@method_decorator(csrf_exempt, name="dispatch")
class TokenRefreshView(APIView):
    """Token刷新视图"""

    permission_classes = [AllowAny]

    def post(self, request: Request) -> Response:
        refresh_token = request.data.get("refresh_token")  # type: ignore

        if not refresh_token or not isinstance(refresh_token, str):
            return StandardResponse.error(message="刷新Token不能为空")

        try:
            refresh = RefreshToken(refresh_token)  # type: ignore

            # 如果配置了轮换刷新token，生成新的refresh token
            new_refresh_token = refresh_token
            if settings.SIMPLE_JWT.get("ROTATE_REFRESH_TOKENS", False):
                refresh.set_jti()
                refresh.set_exp()
                new_refresh_token = str(refresh)

            expires_in = int(
                settings.SIMPLE_JWT["ACCESS_TOKEN_LIFETIME"].total_seconds()
            )

            logger.info("Token刷新成功")

            return StandardResponse.success(
                data={
                    "access_token": str(refresh.access_token),
                    "refresh_token": new_refresh_token,
                    "token_type": "Bearer",
                    "expires_in": expires_in,
                },
                message="Token刷新成功",
            )

        except TokenError:
            logger.warning("Token刷新失败")
            return StandardResponse.error(message="刷新Token无效或已过期")


@extend_schema(
    tags=["用户认证"],
    summary="发送验证码",
    description="发送手机验证码，支持注册、登录和重置密码场景",
    request=SendVerificationCodeSerializer,
    responses={
        200: OpenApiResponse(
            response=StandardResponseSerializer,
            description="验证码发送成功",
        ),
        400: OpenApiResponse(
            response=StandardResponseSerializer,
            description="请求参数错误或发送失败",
        ),
    },
)
@method_decorator(csrf_exempt, name="dispatch")
class SendVerificationCodeView(APIView):
    """发送验证码视图"""

    permission_classes = [AllowAny]

    def post(self, request: Request) -> Response:
        # 使用序列化器验证请求数据
        serializer = SendVerificationCodeSerializer(data=request.data)
        if not serializer.is_valid():
            return StandardResponse.validation_error(errors=serializer.errors)

        phone: str = serializer.validated_data["phone"]  # type: ignore
        code_type: str = serializer.validated_data["code_type"]  # type: ignore

        logger.info(f"验证码请求 - 手机号: {phone}, 类型: {code_type}")

        # 发送验证码
        success, message = VerificationCodeService.send_code(phone, code_type)

        if success:
            logger.info(f"验证码发送成功 - 手机号: {phone}")
            return StandardResponse.success(message=message)
        else:
            logger.warning(f"验证码发送失败 - 手机号: {phone}, 原因: {message}")
            return StandardResponse.error(message=message)


@extend_schema(
    tags=["用户认证"],
    summary="手机号登录",
    description="使用手机号和验证码进行登录。",
    request=PhoneLoginSerializer,
    responses={
        200: OpenApiResponse(
            response=UserLoginResponseSerializer,
            description="登录成功，返回JWT Token和用户信息",
        ),
        400: OpenApiResponse(
            response=StandardResponseSerializer,
            description="请求参数错误、验证码无效或用户不存在",
        ),
    },
)
@method_decorator(csrf_exempt, name="dispatch")
class PhoneLoginView(APIView):
    """手机号登录视图"""

    permission_classes = [AllowAny]

    def post(self, request: Request) -> Response:
        # 验证请求数据
        serializer = PhoneLoginSerializer(data=request.data)
        if not serializer.is_valid():
            return StandardResponse.validation_error(errors=serializer.errors)

        phone: str = serializer.validated_data["phone"]  # type: ignore
        verification_code: str = serializer.validated_data["verification_code"]  # type: ignore

        logger.info(f"手机号登录请求 - 手机号: {phone}")

        # 手机号登录
        user, _, error_message = PhoneLoginService.login_with_phone(
            phone, verification_code
        )

        if not user:
            logger.warning(f"手机号登录失败 - 手机号: {phone}, 原因: {error_message}")
            return StandardResponse.error(message=error_message or "登录失败")

        if not user.is_active:
            return StandardResponse.error(message="您的账号正在审核中，请耐心等待。审核通过后您将收到通知并可正常登录。")

        # 生成JWT Token
        refresh = RefreshToken.for_user(user)
        expires_in = int(settings.SIMPLE_JWT["ACCESS_TOKEN_LIFETIME"].total_seconds())

        logger.info(f"手机号登录成功 - 用户: {user.username}")

        # 处理头像URL
        avatar_url = None
        if hasattr(user, 'avatar') and user.avatar:  # type: ignore
            if hasattr(user.avatar, "url"):  # type: ignore
                avatar_url = user.avatar.url  # type: ignore
            else:
                avatar_url = str(user.avatar)  # type: ignore

        return StandardResponse.success(
            data={
                "access_token": str(refresh.access_token),
                "refresh_token": str(refresh),
                "token_type": "Bearer",
                "expires_in": expires_in,
                "user": {
                    "id": user.pk,
                    "username": user.username,
                    "nickname": user.nickname,  # type: ignore
                    "email": user.email,
                    "phone": user.phone,  # type: ignore
                    "avatar": avatar_url,
                },
            },
            message="登录成功",
        )


@extend_schema(
    tags=["用户认证"],
    summary="用户信息管理",
    description="获取和更新当前登录用户的详细信息",
    responses={
        200: OpenApiResponse(
            response=UserProfileSerializer,
            description="成功获取或更新用户信息",
        ),
        400: OpenApiResponse(
            response=StandardResponseSerializer,
            description="请求参数错误",
        ),
        401: OpenApiResponse(
            response=StandardResponseSerializer,
            description="未认证或Token无效",
        ),
    },
)
class UserProfileView(APIView):
    """用户信息管理视图"""

    permission_classes = [IsAuthenticated]

    @extend_schema(
        summary="获取当前用户信息",
        description="获取当前登录用户的详细信息",
        responses={
            200: OpenApiResponse(
                response=UserProfileSerializer,
                description="成功获取用户信息",
            ),
        },
    )
    def get(self, request):
        """获取当前用户信息"""
        user = request.user
        serializer = UserProfileSerializer(user)

        logger.info(f"获取用户信息 - 用户: {user.username}")

        return StandardResponse.success(
            data=serializer.data, message="获取用户信息成功"
        )

    @extend_schema(
        summary="更新当前用户信息",
        description="更新当前登录用户的个人信息，支持部分更新",
        request=UserProfileUpdateSerializer,
        responses={
            200: OpenApiResponse(
                response=UserProfileSerializer,
                description="成功更新用户信息",
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="请求参数错误",
            ),
        },
    )
    def patch(self, request):
        """更新当前用户信息"""
        user = request.user

        # 使用更新序列化器验证数据
        serializer = UserProfileUpdateSerializer(user, data=request.data, partial=True)
        if not serializer.is_valid():
            return StandardResponse.validation_error(errors=serializer.errors)

        # 保存更新
        updated_user = serializer.save()

        logger.info(f"用户信息更新成功 - 用户: {user.username}")

        # 返回完整的用户信息
        response_serializer = UserProfileSerializer(updated_user)
        return StandardResponse.success(
            data=response_serializer.data, message="用户信息更新成功"
        )


@extend_schema(
    tags=["用户认证"],
    summary="用户注册",
    description="用户注册接口，使用用户名、手机号和验证码进行注册。",
    request=UserRegisterSerializer,
    responses={
        200: OpenApiResponse(
            response=UserProfileSerializer,
            description="注册成功，返回用户信息",
        ),
        400: OpenApiResponse(
            response=StandardResponseSerializer,
            description="请求参数错误或注册失败",
        ),
    },
)
@method_decorator(csrf_exempt, name="dispatch")
class UserRegisterView(APIView):
    """用户注册视图"""

    permission_classes = [AllowAny]

    def post(self, request: Request) -> Response:
        # 验证请求数据
        serializer = UserRegisterSerializer(data=request.data)
        if not serializer.is_valid():
            return StandardResponse.validation_error(errors=serializer.errors)

        username: str = serializer.validated_data["username"]  # type: ignore
        company_name: str = serializer.validated_data["company_name"]  # type: ignore
        phone: str = serializer.validated_data["phone"]  # type: ignore
        verification_code: str = serializer.validated_data["verification_code"]  # type: ignore
        password: str = serializer.validated_data["password"]  # type: ignore
        agree_newsletter: bool = serializer.validated_data.get("agree_newsletter", False)  # type: ignore

        logger.info(f"用户注册请求 - 用户名: {username}, 企业: {company_name}, 手机号: {phone}")

        # 用户注册
        user, error_message = UserRegisterService.register_user(
            username, company_name, phone, verification_code, password, agree_newsletter
        )

        if not user:
            logger.warning(f"用户注册失败 - 用户名: {username}, 原因: {error_message}")
            return StandardResponse.error(message=error_message or "注册失败，请稍后重试")

        logger.info(f"用户注册成功 - 用户: {user.username}")

        # 返回用户信息
        user_serializer = UserProfileSerializer(user)
        return StandardResponse.success(data=user_serializer.data, message="注册成功")


@extend_schema(
    tags=["用户认证"],
    summary="修改密码",
    description="修改当前登录用户的密码，需要提供当前密码进行验证",
    request=ChangePasswordSerializer,
    responses={
        200: OpenApiResponse(
            response=StandardResponseSerializer,
            description="密码修改成功",
        ),
        400: OpenApiResponse(
            response=StandardResponseSerializer,
            description="请求参数错误或当前密码错误",
        ),
        401: OpenApiResponse(
            response=StandardResponseSerializer,
            description="未认证或Token无效",
        ),
    },
)
@method_decorator(csrf_exempt, name="dispatch")
class ChangePasswordView(APIView):
    """修改密码视图"""

    permission_classes = [IsAuthenticated]

    def post(self, request: Request) -> Response:
        """修改密码"""
        user = request.user

        # 验证请求数据
        serializer = ChangePasswordSerializer(data=request.data)
        if not serializer.is_valid():
            return StandardResponse.validation_error(errors=serializer.errors)

        logger.info(f"密码修改请求 - 用户: {user.username}")

        try:
            # 验证当前密码是否正确
            old_password: str = serializer.validated_data["old_password"]  # type: ignore
            if not user.check_password(old_password):
                logger.warning(
                    f"密码修改失败 - 用户: {user.username}, 原因: 当前密码错误"
                )
                return StandardResponse.error(message="当前密码错误")

            # 设置新密码
            new_password: str = serializer.validated_data["new_password"]  # type: ignore
            user.set_password(new_password)
            user.save()

            logger.info(f"密码修改成功 - 用户: {user.username}")

            return StandardResponse.success(message="密码修改成功")

        except Exception as e:
            logger.warning(f"密码修改失败 - 用户: {user.username}, 原因: {str(e)}")
            return StandardResponse.error(message=str(e))
