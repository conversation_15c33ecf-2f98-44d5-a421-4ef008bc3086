"""
博客应用的Django Admin配置
"""
from django.contrib import admin
from .models import BlogCategory, BlogTag, BlogPost


@admin.register(BlogCategory)
class BlogCategoryAdmin(admin.ModelAdmin):
    """
    博客分类管理界面
    """
    list_display = ['name', 'slug', 'order', 'is_active', 'created_at']
    list_editable = ['order']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['order']


@admin.register(BlogTag)
class BlogTagAdmin(admin.ModelAdmin):
    """
    博客标签管理界面
    """
    list_display = ['name', 'slug', 'color', 'created_at']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(BlogPost)
class BlogPostAdmin(admin.ModelAdmin):
    """
    博客文章管理界面
    """
    list_display = ['title', 'category', 'is_published', 'view_count', 'created_at']
    list_filter = ['category', 'is_published', 'created_at']
    readonly_fields = ['created_at', 'updated_at', 'view_count']
    ordering = ['-created_at']

    # 分页大小
    list_per_page = 30
