"""
创建博客示例数据的管理命令
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.blog.models import BlogCategory, BlogTag, BlogPost

User = get_user_model()


class Command(BaseCommand):
    help = '创建博客示例数据，与前端模拟数据保持一致'

    def handle(self, *args, **options):
        self.stdout.write('开始创建博客示例数据...')

        # 创建分类数据
        categories_data = [
            {
                'name': '威胁分析',
                'slug': 'threat-analysis',
                'description': '深度分析各类网络安全威胁，包括恶意软件、攻击手法等'
            },
            {
                'name': 'APT研究',
                'slug': 'apt-research',
                'description': '高级持续性威胁组织的研究和分析'
            },
            {
                'name': '情报技术',
                'slug': 'intelligence-tech',
                'description': '威胁情报收集、分析和应用技术'
            },
            {
                'name': '安全运营',
                'slug': 'security-operations',
                'description': '安全运营中心建设和运营实践'
            },
            {
                'name': '安全架构',
                'slug': 'security-architecture',
                'description': '企业安全架构设计和实施'
            },
            {
                'name': '云安全',
                'slug': 'cloud-security',
                'description': '云计算环境的安全防护技术'
            }
        ]

        categories = {}
        for cat_data in categories_data:
            category, created = BlogCategory.objects.get_or_create(
                slug=cat_data['slug'],
                defaults=cat_data
            )
            categories[cat_data['slug']] = category
            if created:
                self.stdout.write(f'创建分类: {category.name}')

        # 创建标签数据
        tags_data = [
            '勒索软件', '威胁情报', '安全报告', 'APT', '攻击技术', '防护策略',
            '暗网', '数据分析', 'SOC', '安全运营', '企业安全', '零信任',
            '安全架构', '云安全', '威胁防护', '安全技术'
        ]

        tags = {}
        for tag_name in tags_data:
            tag, created = BlogTag.objects.get_or_create(
                name=tag_name,
                defaults={'slug': tag_name.lower().replace(' ', '-')}
            )
            tags[tag_name] = tag
            if created:
                self.stdout.write(f'创建标签: {tag.name}')

        # 创建或获取作者
        authors_data = [
            {'username': 'zhang_anquan', 'first_name': '张', 'last_name': '安全'},
            {'username': 'li_yanjiu', 'first_name': '李', 'last_name': '研究'},
            {'username': 'wang_qingbao', 'first_name': '王', 'last_name': '情报'},
            {'username': 'zhao_yunying', 'first_name': '赵', 'last_name': '运营'},
            {'username': 'sun_jiagou', 'first_name': '孙', 'last_name': '架构'},
            {'username': 'zhou_yunan', 'first_name': '周', 'last_name': '云安'},
        ]

        authors = {}
        for author_data in authors_data:
            author, created = User.objects.get_or_create(
                username=author_data['username'],
                defaults={
                    'nickname': f"{author_data['first_name']}{author_data['last_name']}",
                    'email': f"{author_data['username']}@example.com",
                    'avatar': f'https://images.unsplash.com/photo-{1472099645785 + hash(author_data["username"]) % 1000000}?w=40&h=40&fit=crop&auto=format'
                }
            )
            authors[author_data['username']] = author
            if created:
                self.stdout.write(f'创建作者: {author.nickname or author.username}')

        # 创建文章数据（与前端模拟数据保持一致）
        posts_data = [
            {
                'title': '2024年勒索软件威胁态势分析报告',
                'slug': 'ransomware-threat-landscape-2024',
                'category': 'threat-analysis',
                'author': 'zhang_anquan',
                'tags': ['勒索软件', '威胁情报', '安全报告'],
                'publish_date': '2024-01-15',
                'cover_image': 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=800&h=400&fit=crop&auto=format',
                'excerpt': '深入分析2024年全球勒索软件攻击趋势，包括新兴威胁组织、攻击手法演进以及防护策略建议。',
                'content': '''
## 概述

2024年，勒索软件攻击继续呈现高发态势，攻击手法不断演进，给全球企业和组织带来了严重威胁。本报告基于全年威胁情报数据，深入分析勒索软件的发展趋势、主要威胁组织以及防护策略。

## 主要发现

- 勒索软件攻击数量同比增长35%
- 平均赎金要求达到500万美元
- 制造业和医疗行业成为主要目标
- 双重勒索策略成为主流

## 威胁组织分析

LockBit、BlackCat、Cl0p等组织在2024年表现最为活跃，采用RaaS（勒索软件即服务）模式，降低了攻击门槛，导致攻击事件激增。

## 防护建议

1. 建立完善的数据备份和恢复机制
2. 加强员工安全意识培训
3. 部署端点检测和响应(EDR)解决方案
4. 实施零信任网络架构
5. 定期进行安全评估和渗透测试
                '''
            },
            {
                'title': 'APT组织攻击技术演进与防护策略',
                'slug': 'apt-attack-techniques-evolution',
                'category': 'apt-research',
                'author': 'li_yanjiu',
                'tags': ['APT', '攻击技术', '防护策略'],
                'publish_date': '2024-01-12',
                'cover_image': 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=800&h=400&fit=crop&auto=format',
                'excerpt': '分析当前主要APT组织的攻击技术特点，包括社会工程学、零日漏洞利用、横向移动等手法。',
                'content': '''
## APT攻击概述

高级持续性威胁(APT)攻击是由国家级或高技能攻击者发起的长期、隐蔽的网络攻击活动。这类攻击通常具有明确的政治、经济或军事目标。

## 攻击技术演进

### 1. 初始访问技术

- 鱼叉式钓鱼邮件
- 水坑攻击
- 供应链攻击
- 零日漏洞利用

### 2. 持久化技术

- 注册表修改
- 计划任务
- 服务安装
- 启动项修改

## 主要APT组织

APT29(Cozy Bear)、APT28(Fancy Bear)、APT1等组织在近年来表现活跃，攻击目标主要集中在政府机构、国防部门和关键基础设施。
                '''
            }
        ]

        for post_data in posts_data:
            # 检查文章是否已存在
            if BlogPost.objects.filter(slug=post_data['slug']).exists():
                self.stdout.write(f'文章已存在: {post_data["title"]}')
                continue

            # 创建文章
            post = BlogPost.objects.create(
                title=post_data['title'],
                slug=post_data['slug'],
                content=post_data['content'],
                excerpt=post_data['excerpt'],
                cover_image=post_data['cover_image'],
                category=categories[post_data['category']],
                author=authors[post_data['author']],
                publish_date=timezone.datetime.strptime(post_data['publish_date'], '%Y-%m-%d').date(),
                is_published=True,
                is_featured=True
            )

            # 添加标签
            for tag_name in post_data['tags']:
                if tag_name in tags:
                    post.tags.add(tags[tag_name])

            self.stdout.write(f'创建文章: {post.title}')

        self.stdout.write(self.style.SUCCESS('博客示例数据创建完成！'))
