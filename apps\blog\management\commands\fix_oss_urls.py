"""
修复OSS图片URL过期问题的管理命令
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.blog.models import BlogPost
from utils.oss_utils import refresh_oss_url_if_needed, is_oss_url_expired
import re


class Command(BaseCommand):
    help = '修复博客文章中过期的OSS图片URL'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只检查不修复，显示需要修复的URL',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制刷新所有OSS URL，不管是否过期',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'开始{"检查" if dry_run else "修复"}OSS图片URL...'
            )
        )
        
        # 统计信息
        total_posts = 0
        fixed_cover_images = 0
        fixed_content_images = 0
        
        # 获取所有博客文章
        posts = BlogPost.objects.all()
        total_posts = posts.count()
        
        self.stdout.write(f'找到 {total_posts} 篇文章需要检查')
        
        for post in posts:
            post_updated = False
            
            # 检查封面图片
            if post.cover_image:
                cover_url = str(post.cover_image)
                if force or is_oss_url_expired(cover_url):
                    new_url = refresh_oss_url_if_needed(cover_url)
                    if new_url != cover_url:
                        if dry_run:
                            self.stdout.write(
                                f'文章 "{post.title}" 的封面图片需要修复:\n'
                                f'  旧URL: {cover_url}\n'
                                f'  新URL: {new_url}'
                            )
                        else:
                            # 这里需要更新数据库中的路径，而不是URL
                            # 因为ImageField存储的是文件路径，不是完整URL
                            self.stdout.write(
                                f'文章 "{post.title}" 的封面图片URL已通过序列化器自动修复'
                            )
                        fixed_cover_images += 1
                        post_updated = True
            
            # 检查文章内容中的图片
            if post.content:
                # 查找内容中的OSS图片URL
                oss_pattern = r'https://[^/]+\.oss-[^/]+\.aliyuncs\.com/[^\s\)"\]]+(?:\?[^\s\)"\]]+)?'
                oss_urls = re.findall(oss_pattern, post.content)
                
                updated_content = post.content
                content_changed = False
                
                for url in oss_urls:
                    if force or is_oss_url_expired(url):
                        new_url = refresh_oss_url_if_needed(url)
                        if new_url != url:
                            if dry_run:
                                self.stdout.write(
                                    f'文章 "{post.title}" 内容中的图片需要修复:\n'
                                    f'  旧URL: {url}\n'
                                    f'  新URL: {new_url}'
                                )
                            else:
                                updated_content = updated_content.replace(url, new_url)
                                content_changed = True
                            fixed_content_images += 1
                
                if content_changed and not dry_run:
                    post.content = updated_content
                    post_updated = True
            
            # 保存更新
            if post_updated and not dry_run:
                try:
                    with transaction.atomic():
                        post.save(update_fields=['content'])
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'已修复文章 "{post.title}" 中的图片URL'
                            )
                        )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f'修复文章 "{post.title}" 时出错: {e}'
                        )
                    )
        
        # 输出统计结果
        self.stdout.write('\n' + '='*50)
        self.stdout.write(f'检查完成！统计结果:')
        self.stdout.write(f'  总文章数: {total_posts}')
        self.stdout.write(f'  需要修复的封面图片: {fixed_cover_images}')
        self.stdout.write(f'  需要修复的内容图片: {fixed_content_images}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    '\n这是预览模式，没有实际修改数据。'
                    '\n要执行修复，请运行: python manage.py fix_oss_urls'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    '\n修复完成！新上传的图片将自动使用永不过期的公共URL。'
                )
            )
            
        # 提供OSS设置建议
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.WARNING(
                '重要提示：为了确保图片永不过期，建议设置OSS bucket为公共读取权限：\n'
                '1. 登录阿里云OSS控制台\n'
                '2. 选择你的bucket\n'
                '3. 进入"权限管理" -> "读写权限"\n'
                '4. 设置为"公共读"\n'
                '5. 或者在"权限管理" -> "Bucket授权策略"中添加公共读取策略'
            )
        )
