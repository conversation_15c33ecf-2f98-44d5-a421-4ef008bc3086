# Generated by Django 5.2.3 on 2025-07-09 09:54

import django.db.models.deletion
import mdeditor.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BlogCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(help_text='博客分类的名称', max_length=100, unique=True, verbose_name='分类名称')),
                ('slug', models.SlugField(help_text='用于URL的唯一标识符', max_length=100, unique=True, verbose_name='URL标识')),
                ('description', models.TextField(blank=True, help_text='分类的详细描述', verbose_name='分类描述')),
                ('badge_class', models.CharField(default='badge badge-outline', help_text='前端显示的徽章CSS类名', max_length=50, verbose_name='徽章样式')),
                ('order', models.PositiveIntegerField(default=0, help_text='分类显示顺序，数字越小越靠前', verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, help_text='是否在前端显示此分类', verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '博客分类',
                'verbose_name_plural': '博客分类',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='BlogTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(help_text='博客标签的名称', max_length=50, unique=True, verbose_name='标签名称')),
                ('slug', models.SlugField(help_text='用于URL的唯一标识符', unique=True, verbose_name='URL标识')),
                ('description', models.TextField(blank=True, help_text='标签的详细描述', verbose_name='标签描述')),
                ('color', models.CharField(default='#6B7280', help_text='标签的十六进制颜色代码', max_length=7, verbose_name='标签颜色')),
            ],
            options={
                'verbose_name': '博客标签',
                'verbose_name_plural': '博客标签',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BlogPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('title', models.CharField(help_text='博客文章的标题', max_length=200, verbose_name='文章标题')),
                ('slug', models.SlugField(help_text='用于URL的唯一标识符', max_length=200, unique=True, verbose_name='URL标识')),
                ('excerpt', models.TextField(blank=True, help_text='文章的简短摘要，用于列表页显示', max_length=500, verbose_name='文章摘要')),
                ('content', mdeditor.fields.MDTextField(help_text='文章的详细内容，支持Markdown格式', verbose_name='文章内容')),
                ('cover_image', models.URLField(blank=True, help_text='文章封面图片的URL地址', verbose_name='封面图片')),
                ('publish_date', models.DateTimeField(blank=True, help_text='文章的发布时间', null=True, verbose_name='发布时间')),
                ('is_published', models.BooleanField(default=False, help_text='文章是否已发布', verbose_name='是否发布')),
                ('is_featured', models.BooleanField(default=False, help_text='是否在首页推荐显示', verbose_name='是否推荐')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='文章的浏览次数', verbose_name='浏览次数')),
                ('meta_description', models.TextField(blank=True, help_text='用于搜索引擎优化的描述', max_length=160, verbose_name='SEO描述')),
                ('meta_keywords', models.CharField(blank=True, help_text='用于搜索引擎优化的关键词，用逗号分隔', max_length=255, verbose_name='SEO关键词')),
                ('author', models.ForeignKey(help_text='文章的作者', on_delete=django.db.models.deletion.CASCADE, related_name='blog_posts', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
                ('category', models.ForeignKey(blank=True, help_text='文章所属的分类', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posts', to='blog.blogcategory', verbose_name='文章分类')),
                ('tags', models.ManyToManyField(blank=True, help_text='文章的标签', related_name='posts', to='blog.blogtag', verbose_name='文章标签')),
            ],
            options={
                'verbose_name': '博客文章',
                'verbose_name_plural': '博客文章',
                'ordering': ['-publish_date', '-created_at'],
                'indexes': [models.Index(fields=['slug'], name='blog_blogpo_slug_361555_idx'), models.Index(fields=['is_published', 'publish_date'], name='blog_blogpo_is_publ_a91cce_idx'), models.Index(fields=['category', 'is_published'], name='blog_blogpo_categor_66fea9_idx')],
            },
        ),
    ]
