# Generated by Django 5.2.3 on 2025-07-09 11:51

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blog', '0002_remove_blogpost_slug'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='blogpost',
            options={'ordering': ['-created_at'], 'verbose_name': '博客文章', 'verbose_name_plural': '博客文章'},
        ),
        migrations.RemoveIndex(
            model_name='blogpost',
            name='blog_blogpo_is_publ_a91cce_idx',
        ),
        migrations.RemoveField(
            model_name='blogpost',
            name='is_featured',
        ),
        migrations.RemoveField(
            model_name='blogpost',
            name='publish_date',
        ),
        migrations.AddIndex(
            model_name='blogpost',
            index=models.Index(fields=['is_published', 'created_at'], name='blog_blogpo_is_publ_62546a_idx'),
        ),
    ]
