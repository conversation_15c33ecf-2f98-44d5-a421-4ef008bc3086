"""
博客应用的数据模型
"""
from django.db import models

from django.utils.text import slugify
from django.urls import reverse
from config.models import BaseModel
from mdeditor.fields import MDTextField
import uuid
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from django.db.models import QuerySet




class BlogCategory(BaseModel):
    """
    博客分类模型
    """
    if TYPE_CHECKING:
        posts: 'QuerySet[BlogPost]'

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="分类名称",
        help_text="博客分类的名称"
    )

    slug = models.SlugField(
        max_length=100,
        unique=True,
        verbose_name="URL标识",
        help_text="用于URL的唯一标识符"
    )

    description = models.TextField(
        blank=True,
        verbose_name="分类描述",
        help_text="分类的详细描述"
    )

    order = models.PositiveIntegerField(
        default=0,
        verbose_name="排序",
        help_text="分类显示顺序，数字越小越靠前"
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name="是否启用",
        help_text="是否在前端显示此分类"
    )

    class Meta:
        verbose_name = "博客分类"
        verbose_name_plural = "博客分类"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """自动生成slug"""
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def post_count(self):
        """获取该分类下的文章数量"""
        return self.posts.filter(is_published=True).count()


class BlogTag(BaseModel):
    """
    博客标签模型
    """
    if TYPE_CHECKING:
        posts: 'QuerySet[BlogPost]'

    name = models.CharField(
        max_length=50,
        unique=True,
        verbose_name="标签名称",
        help_text="博客标签的名称"
    )

    slug = models.SlugField(
        max_length=50,
        unique=True,
        verbose_name="URL标识",
        help_text="用于URL的唯一标识符"
    )

    description = models.TextField(
        blank=True,
        verbose_name="标签描述",
        help_text="标签的详细描述"
    )

    color = models.CharField(
        max_length=7,
        default="#6B7280",
        verbose_name="标签颜色",
        help_text="标签的十六进制颜色代码"
    )

    class Meta:
        verbose_name = "博客标签"
        verbose_name_plural = "博客标签"
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        """自动生成slug"""
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    @property
    def post_count(self):
        """获取使用该标签的文章数量"""
        return self.posts.filter(is_published=True).count()


class BlogPost(BaseModel):
    """
    博客文章模型
    """
    # 基本信息
    title = models.CharField(
        max_length=200,
        verbose_name="文章标题",
        help_text="博客文章的标题"
    )

    excerpt = models.TextField(
        max_length=500,
        blank=True,
        verbose_name="文章摘要",
        help_text="文章的简短摘要，用于列表页显示"
    )

    content = MDTextField(
        verbose_name="文章内容",
        help_text="文章的详细内容，支持Markdown格式"
    )

    cover_image = models.ImageField(
        upload_to='blog/covers/',
        blank=True,
        null=True,
        verbose_name="封面图片",
        help_text="文章封面图片文件"
    )

    # 关联信息
    category = models.ForeignKey(
        BlogCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posts',
        verbose_name="文章分类",
        help_text="文章所属的分类"
    )

    tags = models.ManyToManyField(
        BlogTag,
        blank=True,
        related_name='posts',
        verbose_name="文章标签",
        help_text="文章的标签"
    )

    # 发布信息
    is_published = models.BooleanField(
        default=False,
        verbose_name="是否发布",
        help_text="文章是否已发布"
    )

    # 统计信息
    view_count = models.PositiveIntegerField(
        default=0,
        verbose_name="浏览次数",
        help_text="文章的浏览次数"
    )

    # SEO信息
    meta_description = models.TextField(
        max_length=160,
        blank=True,
        verbose_name="SEO描述",
        help_text="用于搜索引擎优化的描述"
    )

    meta_keywords = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="SEO关键词",
        help_text="用于搜索引擎优化的关键词，用逗号分隔"
    )

    class Meta:
        verbose_name = "博客文章"
        verbose_name_plural = "博客文章"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['is_published', 'created_at']),
            models.Index(fields=['category', 'is_published']),
        ]

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        """保存文章"""
        super().save(*args, **kwargs)

    @property
    def reading_time(self):
        """估算阅读时间（分钟）"""
        import re
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', self.content)
        # 计算字数（中文按字符计算，英文按单词计算）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))

        # 中文阅读速度约300字/分钟，英文约200词/分钟
        total_time = (chinese_chars / 300) + (english_words / 200)
        return max(1, round(total_time))

    def get_prev_post(self):
        """获取上一篇文章"""
        return BlogPost.objects.filter(
            is_published=True,
            created_at__lt=self.created_at
        ).order_by('-created_at').first()

    def get_next_post(self):
        """获取下一篇文章"""
        return BlogPost.objects.filter(
            is_published=True,
            created_at__gt=self.created_at
        ).order_by('created_at').first()

    def increment_view_count(self):
        """增加浏览次数"""
        self.view_count += 1
        self.save(update_fields=['view_count'])
