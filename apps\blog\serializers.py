"""
博客应用的序列化器
"""
from rest_framework import serializers
from .models import BlogCategory, BlogTag, BlogPost
from utils.serializer_fields import PublicImageField


class BlogCategorySerializer(serializers.ModelSerializer):
    """
    博客分类序列化器
    """
    post_count = serializers.ReadOnlyField()
    
    class Meta:
        model = BlogCategory
        fields = [
            'id', 'name', 'slug', 'description',
            'order', 'post_count'
        ]


class BlogTagSerializer(serializers.ModelSerializer):
    """
    博客标签序列化器
    """
    post_count = serializers.ReadOnlyField()
    
    class Meta:
        model = BlogTag
        fields = ['id', 'name', 'slug', 'description', 'color', 'post_count']


class BlogPostListSerializer(serializers.ModelSerializer):
    """
    博客文章列表序列化器（用于列表页面）
    """
    category = BlogCategorySerializer(read_only=True)
    tags = BlogTagSerializer(many=True, read_only=True)
    reading_time = serializers.ReadOnlyField()
    cover_image = PublicImageField(read_only=True)

    class Meta:
        model = BlogPost
        fields = [
            'id', 'title', 'excerpt', 'cover_image',
            'category', 'tags',
            'view_count', 'reading_time',
            'created_at', 'updated_at'
        ]

    def to_representation(self, instance):
        """自定义输出格式以匹配前端期望"""
        data = super().to_representation(instance)

        # 转换标签格式
        data['tags'] = [tag['name'] for tag in data['tags']]

        # 添加分类名称
        if data['category']:
            data['category'] = data['category']['name']

        return data


class BlogPostDetailSerializer(serializers.ModelSerializer):
    """
    博客文章详情序列化器（用于详情页面）
    """
    category = BlogCategorySerializer(read_only=True)
    tags = BlogTagSerializer(many=True, read_only=True)
    reading_time = serializers.ReadOnlyField()
    prev_post = serializers.SerializerMethodField()
    next_post = serializers.SerializerMethodField()
    cover_image = PublicImageField(read_only=True)

    class Meta:
        model = BlogPost
        fields = [
            'id', 'title', 'content', 'excerpt', 'cover_image',
            'category', 'tags', 'view_count',
            'reading_time', 'meta_description', 'meta_keywords',
            'created_at', 'updated_at',
            'prev_post', 'next_post'
        ]

    def get_prev_post(self, obj):
        """获取上一篇文章"""
        prev_post = obj.get_prev_post()
        if prev_post:
            return {
                'id': prev_post.pk,
                'title': prev_post.title,
                'category': prev_post.category.name if prev_post.category else None
            }
        return None

    def get_next_post(self, obj):
        """获取下一篇文章"""
        next_post = obj.get_next_post()
        if next_post:
            return {
                'id': next_post.pk,
                'title': next_post.title,
                'category': next_post.category.name if next_post.category else None
            }
        return None

    def to_representation(self, instance):
        """自定义输出格式以匹配前端期望"""
        data = super().to_representation(instance)

        # 转换标签格式
        data['tags'] = [tag['name'] for tag in data['tags']]

        # 添加分类名称
        if data['category']:
            data['category'] = data['category']['name']

        return data

