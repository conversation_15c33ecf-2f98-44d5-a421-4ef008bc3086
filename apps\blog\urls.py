"""
博客应用的URL配置
"""
from django.urls import path, include
from django.http import HttpResponse
from rest_framework.routers import DefaultRouter
from .views import BlogCategoryViewSet, BlogTagViewSet, BlogPostViewSet

# 创建DRF路由器
router = DefaultRouter()
router.register(r'categories', BlogCategoryViewSet, basename='blog-category')
router.register(r'tags', BlogTagViewSet, basename='blog-tag')
router.register(r'posts', BlogPostViewSet, basename='blog-post')

# 应用命名空间
app_name = 'blog'

# URL模式
urlpatterns = [
    # DRF路由
    path('', include(router.urls)),
]

# 可用的API端点：
# GET /api/v1/blog/categories/ - 博客分类列表
# GET /api/v1/blog/categories/{slug}/ - 博客分类详情
# GET /api/v1/blog/categories/{slug}/posts/ - 特定分类下的文章列表
#
# GET /api/v1/blog/tags/ - 博客标签列表
# GET /api/v1/blog/tags/{slug}/ - 博客标签详情
# GET /api/v1/blog/tags/{slug}/posts/ - 特定标签下的文章列表
#
# GET /api/v1/blog/posts/ - 博客文章列表
# GET /api/v1/blog/posts/{id}/ - 博客文章详情
# GET /api/v1/blog/posts/{id}/prev-next/ - 获取上一篇/下一篇文章
# GET /api/v1/blog/posts/search/?q=关键词 - 搜索文章
