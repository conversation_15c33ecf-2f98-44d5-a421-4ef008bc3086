"""
博客应用的视图
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django_filters import rest_framework as filters_rest
from django.shortcuts import get_object_or_404
from django.db.models import Q
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, OpenApiResponse
from drf_spectacular.types import OpenApiTypes
from utils.viewsets import StandardModelViewSet
from utils.response import StandardResponse
from utils.permissions import get_permissions_for_action
from .models import BlogCategory, BlogTag, BlogPost
from .serializers import (
    BlogCategorySerializer, BlogTagSerializer,
    BlogPostListSerializer, BlogPostDetailSerializer
)


class BlogPostFilter(filters_rest.FilterSet):
    """
    博客文章自定义过滤器
    """
    category = filters_rest.CharFilter(field_name='category__slug', lookup_expr='exact')

    class Meta:
        model = BlogPost
        fields = ['category', 'tags']


@extend_schema_view(
    list=extend_schema(
        tags=['博客管理'],
        summary='获取博客分类列表',
        description='获取所有激活状态的博客分类列表，支持搜索和排序',
        parameters=[
            OpenApiParameter(
                name='search',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='搜索关键词，可搜索分类名称和描述'
            ),
            OpenApiParameter(
                name='ordering',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='排序字段，可选值：order, name, created_at, -order, -name, -created_at'
            ),
        ]
    ),
    retrieve=extend_schema(
        tags=['博客管理'],
        summary='获取博客分类详情',
        description='根据分类slug获取博客分类的详细信息'
    ),
)
class BlogCategoryViewSet(StandardModelViewSet):
    """
    博客分类视图集

    提供博客分类的只读操作，包括：
    - 获取分类列表（支持搜索和排序）
    - 获取分类详情
    - 获取特定分类下的文章列表
    """
    queryset = BlogCategory.objects.filter(is_active=True)
    serializer_class = BlogCategorySerializer
    lookup_field = 'slug'
    http_method_names = ['get', 'head', 'options']  # 只允许读取操作

    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['order', 'name', 'created_at']
    ordering = ['order', 'name']

    @extend_schema(
        tags=['博客管理'],
        summary='获取分类下的文章列表',
        description='获取指定分类下所有已发布的文章列表，支持分页',
        responses={
            200: OpenApiResponse(
                response=BlogPostListSerializer(many=True),
                description='成功获取分类下的文章列表'
            ),
            404: OpenApiResponse(description='分类不存在')
        }
    )
    @action(detail=True, methods=['get'])
    def posts(self, request, slug=None):
        """获取特定分类下的文章列表"""
        category = self.get_object()
        posts = BlogPost.objects.filter(
            category=category,
            is_published=True
        ).select_related('category').prefetch_related('tags')

        # 应用分页
        page = self.paginate_queryset(posts)
        if page is not None:
            serializer = BlogPostListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = BlogPostListSerializer(posts, many=True)
        return StandardResponse.success(
            data=serializer.data,
            message=f"获取分类 {category.name} 下的文章成功"
        )


@extend_schema_view(
    list=extend_schema(
        tags=['博客管理'],
        summary='获取博客标签列表',
        description='获取所有博客标签列表，支持搜索和排序',
        parameters=[
            OpenApiParameter(
                name='search',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='搜索关键词，可搜索标签名称和描述'
            ),
            OpenApiParameter(
                name='ordering',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='排序字段，可选值：name, created_at, -name, -created_at'
            ),
        ]
    ),
    retrieve=extend_schema(
        tags=['博客管理'],
        summary='获取博客标签详情',
        description='根据标签slug获取博客标签的详细信息'
    ),
)
class BlogTagViewSet(StandardModelViewSet):
    """
    博客标签视图集

    提供博客标签的只读操作，包括：
    - 获取标签列表（支持搜索和排序）
    - 获取标签详情
    - 获取特定标签下的文章列表
    """
    queryset = BlogTag.objects.all()
    serializer_class = BlogTagSerializer
    lookup_field = 'slug'
    http_method_names = ['get', 'head', 'options']  # 只允许读取操作

    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    @extend_schema(
        tags=['博客管理'],
        summary='获取标签下的文章列表',
        description='获取指定标签下所有已发布的文章列表，支持分页',
        responses={
            200: OpenApiResponse(
                response=BlogPostListSerializer(many=True),
                description='成功获取标签下的文章列表'
            ),
            404: OpenApiResponse(description='标签不存在')
        }
    )
    @action(detail=True, methods=['get'])
    def posts(self, request, slug=None):
        """获取特定标签下的文章列表"""
        tag = self.get_object()
        posts = BlogPost.objects.filter(
            tags=tag,
            is_published=True
        ).select_related('category').prefetch_related('tags')

        # 应用分页
        page = self.paginate_queryset(posts)
        if page is not None:
            serializer = BlogPostListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = BlogPostListSerializer(posts, many=True)
        return StandardResponse.success(
            data=serializer.data,
            message=f"获取标签 {tag.name} 下的文章成功"
        )


@extend_schema_view(
    list=extend_schema(
        tags=['博客管理'],
        summary='获取博客文章列表',
        description='获取所有已发布的博客文章列表，支持分类过滤、标签过滤、搜索和排序',
        parameters=[
            OpenApiParameter(
                name='category',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='按分类slug过滤文章'
            ),
            OpenApiParameter(
                name='tags',
                type=OpenApiTypes.INT,
                location=OpenApiParameter.QUERY,
                description='按标签ID过滤文章'
            ),

            OpenApiParameter(
                name='search',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='搜索关键词，可搜索标题、内容、摘要、关键词'
            ),
            OpenApiParameter(
                name='ordering',
                type=OpenApiTypes.STR,
                location=OpenApiParameter.QUERY,
                description='排序字段，可选值：view_count, created_at, -view_count, -created_at'
            ),
        ]
    ),
    retrieve=extend_schema(
        tags=['博客管理'],
        summary='获取博客文章详情',
        description='根据文章ID获取博客文章的详细信息，同时增加浏览次数'
    ),
)
class BlogPostViewSet(StandardModelViewSet):
    """
    博客文章视图集

    提供博客文章的只读操作，包括：
    - 获取文章列表（支持分类、标签过滤，搜索和排序）
    - 获取文章详情（自动增加浏览次数）
    - 获取文章的上一篇/下一篇
    """
    queryset = BlogPost.objects.filter(is_published=True).select_related(
        'category'
    ).prefetch_related('tags')
    lookup_field = 'pk'
    http_method_names = ['get', 'head', 'options']  # 只允许读取操作

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = BlogPostFilter
    search_fields = ['title', 'content', 'excerpt', 'meta_keywords']
    ordering_fields = ['view_count', 'created_at']
    ordering = ['-created_at']

    def get_permissions(self):
        """
        根据动作设置权限
        - retrieve 操作需要用户登录
        - 其他操作允许匿名访问
        """
        return get_permissions_for_action(self.action)

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'retrieve':
            return BlogPostDetailSerializer
        return BlogPostListSerializer

    @extend_schema(
        tags=['博客管理'],
        summary='获取文章详情',
        description='获取博客文章的详细信息，同时自动增加浏览次数',
        responses={
            200: OpenApiResponse(
                response=BlogPostDetailSerializer,
                description='成功获取文章详情'
            ),
            404: OpenApiResponse(description='文章不存在')
        }
    )
    def retrieve(self, request, *args, **kwargs):
        """获取文章详情，同时增加浏览次数"""
        instance = self.get_object()

        # 增加浏览次数
        instance.increment_view_count()

        serializer = self.get_serializer(instance)
        return StandardResponse.success(
            data=serializer.data,
            message="获取文章详情成功"
        )

    @extend_schema(
        tags=['博客管理'],
        summary='获取相邻文章',
        description='获取指定文章的上一篇和下一篇文章信息',
        responses={
            200: OpenApiResponse(
                description='成功获取相邻文章信息'
            ),
            404: OpenApiResponse(description='文章不存在')
        }
    )
    @action(detail=True, methods=['get'])
    def prev_next(self, request, pk=None):
        """获取上一篇和下一篇文章"""
        post = self.get_object()

        prev_post = post.get_prev_post()
        next_post = post.get_next_post()

        data = {}
        if prev_post:
            data['prev'] = {
                'id': prev_post.pk,
                'title': prev_post.title,
                'category': prev_post.category.name if prev_post.category else None
            }

        if next_post:
            data['next'] = {
                'id': next_post.pk,
                'title': next_post.title,
                'category': next_post.category.name if next_post.category else None
            }

        return StandardResponse.success(
            data=data,
            message="获取相邻文章成功"
        )

