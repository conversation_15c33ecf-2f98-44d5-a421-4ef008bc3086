from django.contrib import admin
from django.db.models.fields.json import <PERSON><PERSON><PERSON><PERSON>
from django import forms
from jsoneditor.forms import JSONEditor
from .models import RansomwareGroup, Tools, NegotiationRecord, RansomNote, IOCIndicator, Victim


class RansomwareGroupAdminForm(forms.ModelForm):
    """
    勒索软件组织管理表单
    为不同的JSON字段提供定制化的编辑器配置
    """

    class Meta:
        model = RansomwareGroup
        fields = '__all__'
        widgets = {
            # JSON字段使用JSON编辑器
            'aliases': JSONEditor(),
            'locations': JSONEditor(),
            'tools_used': JSONEditor(),
            'ttps': JSONEditor(),
            'vulnerabilities': JSONEditor(),
            'data_sources': JSONEditor(),
        }


@admin.register(RansomwareGroup)
class RansomwareGroupAdmin(admin.ModelAdmin):
    """
    勒索软件组织管理界面
    """
    form = RansomwareGroupAdminForm

    list_display = [
        'name', 'slug', 'avg_delay_days', 'info_stealer_percentage', 'status', 'threat_level', 'victim_count',
        'first_seen', 'last_activity'
    ]

    list_filter = [
        'status', 'threat_level',
        'first_seen', 'last_activity', 'created_at'
    ]

    search_fields = [
        'name', 'slug', 'aliases', 'description'
    ]

    readonly_fields = [
        'id', 'created_at', 'updated_at'
    ]

    prepopulated_fields = {
        'slug': ('name',)
    }

    fieldsets = (
        ('系统信息', {
            'fields': (
                'id', 'created_at', 'updated_at'
            )
        }),
        ('基本信息', {
            'fields': (
                'name', 'logo', 'slug', 'avg_delay_days', 'info_stealer_percentage',
                'aliases', 'description', 'external_information_source', 'status',
                'first_seen', 'last_activity'
            )
        }),
        ('技术特征', {
            'fields': (
                'locations', 'tools_used', 'ttps', 'vulnerabilities'
            )
        }),
        ('关联信息', {
            'fields': (
                'threat_level', 'victim_count', 'data_sources'
            )
        }),
    )

    ordering = ['-last_activity', '-created_at']

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related()

    class Media:
        """添加自定义CSS和JS"""
        css = {
            'all': ('admin/css/json-editor.css',)
        }


@admin.register(Tools)
class ToolsAdmin(admin.ModelAdmin):
    """
    应急工具管理界面
    """

    list_display = [
        'name', 'ransomware_group', 'file', 'created_at', 'updated_at'
    ]

    list_filter = [
        'ransomware_group', 'created_at', 'updated_at'
    ]

    search_fields = [
        'name', 'description', 'content', 'ransomware_group__name', 'seo_keywords', 'seo_description'
    ]

    readonly_fields = [
        'id', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('系统信息', {
            'fields': (
                'id', 'created_at', 'updated_at'
            )
        }),
        ('基本信息', {
            'fields': (
                'name', 'ransomware_group', 'description', 'content'
            )
        }),
        ('文件信息', {
            'fields': (
                'file',
            )
        }),
        ('SEO优化', {
            'fields': (
                'seo_keywords', 'seo_description'
            ),
            'classes': ('collapse',),
            'description': '搜索引擎优化相关字段，用于提升工具页面的搜索排名'
        }),
    )

    ordering = ['-created_at']

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('ransomware_group')

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        """自定义字段表单"""
        if db_field.name == 'content':
            kwargs['widget'] = forms.Textarea(attrs={
                'rows': 10,
                'cols': 80,
                'class': 'vLargeTextField'
            })
        return super().formfield_for_dbfield(db_field, request, **kwargs)


class NegotiationRecordAdminForm(forms.ModelForm):
    """
    谈判记录管理表单
    """

    class Meta:
        model = NegotiationRecord
        fields = '__all__'
        widgets = {
            'messages': JSONEditor(),
        }


@admin.register(NegotiationRecord)
class NegotiationRecordAdmin(admin.ModelAdmin):
    """
    谈判记录管理界面
    """
    form = NegotiationRecordAdminForm

    list_display = [
        'id', 'group', 'paid', 'initialransom', 'negotiatedransom',
        'message_count', 'created_at'
    ]

    list_filter = [
        'paid', 'group', 'created_at'
    ]

    search_fields = [
        'group__name', 'initialransom', 'negotiatedransom'
    ]

    readonly_fields = [
        'id', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('系统信息', {
            'fields': (
                'id', 'created_at', 'updated_at'
            )
        }),
        ('基本信息', {
            'fields': (
                'group', 'paid'
            )
        }),
        ('赎金信息', {
            'fields': (
                'initialransom', 'negotiatedransom'
            )
        }),
        ('谈判过程', {
            'fields': (
                'message_count', 'messages'
            )
        }),
    )

    ordering = ['-created_at']

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('group')


@admin.register(RansomNote)
class RansomNoteAdmin(admin.ModelAdmin):
    """
    勒索信管理界面
    """

    list_display = [
        'note_name', 'extension', 'group', 'created_at'
    ]

    list_filter = [
        'group', 'extension', 'created_at'
    ]

    search_fields = [
        'note_name', 'content', 'group__name'
    ]

    readonly_fields = [
        'id', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('系统信息', {
            'fields': (
                'id', 'created_at', 'updated_at'
            )
        }),
        ('基本信息', {
            'fields': (
                'group', 'note_name', 'extension', 'content'
            )
        }),
    )

    ordering = ['-created_at']

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('group')

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        """自定义字段表单"""
        if db_field.name == 'content':
            kwargs['widget'] = forms.Textarea(attrs={
                'rows': 15,
                'cols': 100,
                'class': 'vLargeTextField'
            })
        return super().formfield_for_dbfield(db_field, request, **kwargs)


class IOCIndicatorAdminForm(forms.ModelForm):
    """
    IOC指标管理表单
    """

    class Meta:
        model = IOCIndicator
        fields = '__all__'
        widgets = {
            'ioc_types': JSONEditor(),
            'iocs': JSONEditor(),
        }


@admin.register(IOCIndicator)
class IOCIndicatorAdmin(admin.ModelAdmin):
    """
    IOC指标管理界面
    """
    form = IOCIndicatorAdminForm

    list_display = [
        'id', 'group', 'get_ioc_types_display', 'get_iocs_count', 'created_at'
    ]

    list_filter = [
        'group', 'created_at'
    ]

    search_fields = [
        'group__name', 'ioc_types', 'iocs'
    ]

    readonly_fields = [
        'id', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('系统信息', {
            'fields': (
                'id', 'created_at', 'updated_at'
            )
        }),
        ('基本信息', {
            'fields': (
                'group', 'ioc_types', 'iocs'
            )
        }),
    )

    ordering = ['-created_at']

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related('group')

    def get_ioc_types_display(self, obj):
        """显示IOC类型"""
        if obj.ioc_types:
            return ', '.join(obj.ioc_types[:3]) + ('...' if len(obj.ioc_types) > 3 else '')
        return '-'
    get_ioc_types_display.short_description = 'IOC类型'

    def get_iocs_count(self, obj):
        """显示IOC数量"""
        if obj.iocs:
            total = sum(len(values) if isinstance(values, list) else 1 for values in obj.iocs.values())
            return f'{total} 个指标'
        return '0 个指标'
    get_iocs_count.short_description = 'IOC数量'


class VictimAdminForm(forms.ModelForm):
    """
    受害者管理表单
    """

    class Meta:
        model = Victim
        fields = '__all__'
        widgets = {
            'duplicates': JSONEditor(),
            'extrainfos': JSONEditor(),
            'infostealer': JSONEditor(),
            'press': JSONEditor(),
        }


@admin.register(Victim)
class VictimAdmin(admin.ModelAdmin):
    """
    受害者管理界面
    """
    form = VictimAdminForm

    list_display = [
        'post_title', 'discovered', 'attack_date', 'country', 'activity', 'created_at'
    ]

    list_filter = [
        'country', 'activity', 'created_at'
    ]

    search_fields = [
        'post_title', 'description', 'website', 'post_url'
    ]

    readonly_fields = [
        'id', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('系统信息', {
            'fields': (
                'id', 'created_at', 'updated_at'
            )
        }),
        ('基本信息', {
            'fields': (
                'group', 'post_title', 'discovered', 'attack_date', 'description', 'website', 'post_url'
            )
        }),
        ('地理和活动信息', {
            'fields': (
                'country', 'activity'
            )
        }),
        ('扩展信息', {
            'fields': (
                'duplicates', 'extrainfos', 'screenshot', 'infostealer', 'press', 'permalink'
            )
        }),
    )

    ordering = ['-created_at']

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).prefetch_related('group')