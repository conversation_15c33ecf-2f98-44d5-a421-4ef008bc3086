from django_filters import rest_framework
from .models import RansomwareGroup


class GroupFilter(rest_framework.FilterSet):
    alias = rest_framework.CharFilter(field_name='aliases', lookup_expr='icontains')
    tool = rest_framework.CharFilter(field_name='tools_used', lookup_expr='icontains')
    region = rest_framework.CharFilter(field_name='target_regions', lookup_expr='icontains')
    industry = rest_framework.CharFilter(field_name='target_industries', lookup_expr='icontains')
    ransomware_family = rest_framework.CharFilter(field_name='ransomware_families', lookup_expr='icontains')
    site = rest_framework.CharFilter(field_name='known_sites', lookup_expr='icontains')

    class Meta:
        model = RansomwareGroup
        fields = [
            'status',
            'threat_level',
            'alias',
            'tool',
            'region',
            'industry',
            'ransomware_family',
            'site',
        ]
