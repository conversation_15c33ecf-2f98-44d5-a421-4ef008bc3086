from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from apps.group.models import RansomwareGroup


class Command(BaseCommand):
    help = '创建一条真实的勒索软件组织数据记录'

    def handle(self, *args, **options):
        # 检查是否已存在LockBit记录
        if RansomwareGroup.objects.filter(name="LockBit").exists():
            self.stdout.write(
                self.style.WARNING('LockBit组织记录已存在，跳过创建')
            )
            return

        # 创建基于真实LockBit勒索软件组织的数据
        lockbit_data = {
            # 基本信息
            'name': 'LockBit',
            # 'logo': None,  # 示例数据中暂不设置logo
            'aliases': [
                'LockBit 2.0',
                'LockBit 3.0',
                'LockBit Black',
                'ABCD ransomware'
            ],
            'description': '''LockBit是一个活跃的勒索软件即服务(RaaS)组织，首次出现于2019年。
该组织以其快速加密能力和双重勒索策略而闻名，不仅加密受害者文件，还威胁公开泄露敏感数据。
LockBit采用联盟模式运营，为其他网络犯罪分子提供勒索软件工具和基础设施。
该组织持续更新其恶意软件，推出了多个版本包括LockBit 2.0和3.0，增强了规避检测和加密效率。''',
            'external_information_source': '''## 外部信息来源

### 官方安全报告
- [FBI LockBit勒索软件公告](https://www.fbi.gov/news/stories/lockbit-ransomware)
- [CISA LockBit威胁简报](https://www.cisa.gov/news-events/cybersecurity-advisories)
- [Europol LockBit行动报告](https://www.europol.europa.eu/media-press/newsroom/news)

### 安全厂商分析
- [Trend Micro LockBit分析报告](https://www.trendmicro.com/en_us/research/22/e/lockbit-ransomware-group-augments-its-latest-variant.html)
- [Sophos LockBit研究](https://news.sophos.com/en-us/2022/10/04/lockbit-3-0-black-attacks/)
- [CrowdStrike LockBit情报](https://www.crowdstrike.com/blog/lockbit-3-0-ransomware/)

### 威胁情报平台
- MITRE ATT&CK Framework: [S0576](https://attack.mitre.org/software/S0576/)
- VirusTotal IOC数据库
- 暗网监控平台数据''',
            'status': 'active',
            'first_seen': timezone.make_aware(datetime(2019, 9, 1)),
            'last_activity': timezone.make_aware(datetime.now() - timedelta(days=7)),

            # 技术特征
            'ransomware_families': [
                'LockBit',
                'LockBit 2.0',
                'LockBit 3.0',
                'LockBit Black'
            ],
            'encryption_algorithms': [
                'AES-256',
                'RSA-2048',
                'ChaCha20'
            ],
            'attack_vectors': [
                'RDP暴力破解',
                '钓鱼邮件',
                '漏洞利用',
                '供应链攻击',
                '内部威胁',
                'VPN漏洞利用',
                '远程桌面服务',
                '网络横向移动'
            ],
            'target_industries': [
                '制造业',
                '医疗保健',
                '金融服务',
                '教育',
                '政府机构',
                '零售业',
                '能源',
                '交通运输',
                '房地产',
                '法律服务'
            ],
            'target_regions': [
                '北美',
                '欧洲',
                '亚太地区',
                '南美',
                '中东',
                '非洲'
            ],
            'operating_systems': [
                'Windows',
                'Linux',
                'VMware ESXi',
                'FreeBSD'
            ],
            'locations': [
                'lockbitapt6vx57t3eeqjofwgcglmutr3a35nygvokja5uuccip4ykyd.onion',
                'lockbitapt72iw55njgnqgskg5yp75ry7rirtdg4m7i42artsbqd.onion',
                'lockbitblog.onion',
                'lockbitsupp.onion'
            ],
            'victim': [
                {
                    'name': 'Acme Corporation',
                    'industry': '制造业',
                    'country': '美国',
                    'attack_date': '2023-10-15',
                    'ransom_amount': 500000,
                    'status': '已支付'
                },
                {
                    'name': 'Global Healthcare Inc',
                    'industry': '医疗保健',
                    'country': '加拿大',
                    'attack_date': '2023-11-02',
                    'ransom_amount': 1200000,
                    'status': '拒绝支付'
                },
                {
                    'name': 'TechStart Ltd',
                    'industry': '科技',
                    'country': '英国',
                    'attack_date': '2023-11-20',
                    'ransom_amount': 300000,
                    'status': '谈判中'
                }
            ],
            'negotiation_record': [
                {
                    'victim': 'Acme Corporation',
                    'initial_demand': 1000000,
                    'final_amount': 500000,
                    'negotiation_duration_days': 5,
                    'key_points': [
                        '初始要求100万美元',
                        '受害者提出财务困难',
                        '组织同意50%折扣',
                        '提供部分文件解密测试',
                        '最终达成50万美元协议'
                    ]
                },
                {
                    'victim': 'Global Healthcare Inc',
                    'initial_demand': 2000000,
                    'final_amount': 0,
                    'negotiation_duration_days': 10,
                    'key_points': [
                        '初始要求200万美元',
                        '受害者拒绝支付',
                        '威胁公开患者数据',
                        '联系当地媒体施压',
                        '最终公开部分数据'
                    ]
                }
            ],
            'note': [
                {
                    'filename': 'LockBit_Readme.txt',
                    'content': '''您的文件已被LockBit勒索软件加密！

所有重要文件都已被加密，包括数据库、文档、照片等。
不要尝试自行解密，这可能导致数据永久丢失。

要恢复您的文件，您需要购买我们的解密工具。

联系我们：
- 访问我们的Tor网站：lockbitapt6vx57t3eeqjofwgcglmutr3a35nygvokja5uuccip4ykyd.onion
- 使用您的唯一ID：[VICTIM_ID]

支付说明：
1. 下载Tor浏览器
2. 访问我们的网站
3. 输入您的ID
4. 按照指示支付比特币
5. 获得解密工具

警告：
- 72小时内不支付，价格将翻倍
- 7天内不支付，我们将公开您的数据
- 联系执法部门或安全公司将导致数据被删除

我们是专业的，支付后会立即提供解密工具。'''
                },
                {
                    'filename': 'HOW_TO_RESTORE_FILES.txt',
                    'content': '''LockBit 3.0 - 文件恢复指南

您的网络已被LockBit 3.0勒索软件感染。
所有重要文件都已使用军用级加密算法加密。

恢复步骤：
1. 不要重启计算机或删除任何文件
2. 不要使用任何反病毒软件扫描
3. 立即联系我们获取解密密钥

联系方式：
- Tor网站：[ONION_LINK]
- 备用联系：[TOX_ID]

支付信息：
- 接受比特币和门罗币
- 提供24/7客户支持
- 免费解密测试（最多3个文件）

时间限制：
- 72小时内支付享受50%折扣
- 超过7天将公开您的敏感数据

我们保证支付后立即提供有效的解密工具。'''
                }
            ],

            # 运营信息
            'ransom_amount_min': 10000.00,
            'ransom_amount_max': 10000000.00,
            'payment_methods': [
                'bitcoin',
                'monero'
            ],
            'contact_methods': [
                'Tor网站',
                '加密聊天',
                'TOX ID',
                '暗网邮箱'
            ],

            'negotiation_tactics': '''LockBit组织在谈判中通常采用以下策略：
1. 初始要求较高赎金，但愿意协商降价
2. 设置严格的支付期限，通常为72小时到7天
3. 威胁在期限内公开泄露数据
4. 提供"客户服务"，包括解密测试和技术支持
5. 对大型企业采用更激进的策略，包括联系媒体和客户
6. 提供"折扣"给快速支付的受害者''',

            # 关联信息
            'ioc_indicators': {
                'domains': [
                    'lockbitapt6vx57t3eeqjofwgcglmutr3a35nygvokja5uuccip4ykyd.onion',
                    'lockbitapt72iw55njgnqpymggskg5yp75ry7rirtdg4m7i42artsbqd.onion'
                ],
                'file_extensions': [
                    '.lockbit',
                    '.abcd',
                    '.lockbit2',
                    '.lockbit3'
                ],
                'registry_keys': [
                    'HKLM\\SOFTWARE\\LockBit',
                    'HKCU\\SOFTWARE\\LockBit'
                ],
                'mutex_names': [
                    'Global\\LockBitMutex',
                    'LockBit_Mutex'
                ]
            },
            'malware_samples': [
                'a1b2c3d4e5f6789012345678901234567890abcd',
                'b2c3d4e5f6789012345678901234567890abcdef1',
                'c3d4e5f6789012345678901234567890abcdef12'
            ],
            'threat_level': 'critical',
            'victim_count': 1500,
            'data_sources': [
                '暗网监控',
                'Telegram频道',
                '安全厂商报告',
                '执法部门通报',
                '受害者报告',
                '威胁情报平台'
            ],
            'related_groups': [],
            'communication_channel': [
                'Telegram频道',
                'Discord服务器',
                'Tor暗网论坛',
                '加密邮件',
                'TOX聊天',
                'Jabber/XMPP',
                'Wickr加密消息',
                '暗网市场私信'
            ]
        }

        # 创建记录
        try:
            ransomware_group = RansomwareGroup.objects.create(**lockbit_data)
            self.stdout.write(
                self.style.SUCCESS(
                    f'成功创建勒索软件组织记录: {ransomware_group.name} (ID: {ransomware_group.id})'
                )
            )

            # 显示创建的记录信息
            self.stdout.write('\n=== 创建的记录详情 ===')
            self.stdout.write(f'组织名称: {ransomware_group.name}')
            self.stdout.write(f'别名: {ransomware_group.get_aliases_display()}')
            self.stdout.write(f'状态: {ransomware_group.get_status_display()}')
            self.stdout.write(f'威胁等级: {ransomware_group.get_threat_level_display()}')
            self.stdout.write(f'赎金范围: {ransomware_group.get_ransom_range_display()}')
            self.stdout.write(f'目标行业: {ransomware_group.get_target_industries_display()}')
            self.stdout.write(f'已知受害者: {ransomware_group.victim_count}')
            self.stdout.write(f'首次发现: {ransomware_group.first_seen}')
            self.stdout.write(f'最后活动: {ransomware_group.last_activity}')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'创建记录时发生错误: {str(e)}')
            )
