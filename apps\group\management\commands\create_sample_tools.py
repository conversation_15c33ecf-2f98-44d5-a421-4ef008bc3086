from django.core.management.base import BaseCommand
from apps.group.models import RansomwareGroup, Tools


class Command(BaseCommand):
    help = '创建示例应急工具数据'

    def handle(self, *args, **options):
        # 获取或创建一个勒索软件组织
        try:
            group = RansomwareGroup.objects.get(name='LockBit')
        except RansomwareGroup.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('未找到名为 "LockBit" 的勒索软件组织，请先创建组织数据')
            )
            return

        # 创建示例工具数据
        tools_data = [
            {
                'name': 'LockBit解密工具',
                'description': '专门针对LockBit勒索软件的解密工具，可以帮助受害者恢复被加密的文件。支持多种文件格式，操作简单，安全可靠。',
                'content': '''
# LockBit解密工具

## 工具描述
这是一个专门针对LockBit勒索软件的解密工具，可以帮助受害者恢复被加密的文件。

## 使用方法
1. 下载工具到受感染的系统
2. 以管理员权限运行工具
3. 选择需要解密的文件或目录
4. 等待解密过程完成

## 注意事项
- 请在使用前备份重要文件
- 确保系统已断开网络连接
- 建议在隔离环境中使用

## 支持的文件类型
- 文档文件：.doc, .docx, .pdf, .txt
- 图片文件：.jpg, .png, .gif, .bmp
- 数据库文件：.sql, .db, .mdb
- 其他常见文件格式
                ''',
            },
            {
                'name': 'LockBit检测脚本',
                'description': '自动检测系统是否感染LockBit勒索软件的Python脚本，提供全面的系统扫描和威胁评估功能。',
                'content': '''
# LockBit检测脚本

## 功能说明
该脚本可以检测系统是否感染了LockBit勒索软件，并提供初步的威胁评估。

## 检测内容
- 文件系统扫描
- 注册表项检查
- 网络连接分析
- 进程监控

## 使用指南
```bash
python lockbit_detector.py --scan-all
```

## 输出报告
脚本会生成详细的检测报告，包括：
- 感染状态
- 受影响文件列表
- 建议的应对措施
                ''',
            },
            {
                'name': 'LockBit清除工具',
                'description': '专业的LockBit勒索软件清除工具，能够彻底清理恶意文件、注册表项和系统配置，恢复系统正常状态。',
                'content': '''
# LockBit清除工具

## 工具功能
专门用于清除LockBit勒索软件残留文件和注册表项的清理工具。

## 清理范围
- 恶意文件删除
- 注册表项清理
- 系统服务恢复
- 网络配置重置

## 安全提醒
- 使用前请确保已备份重要数据
- 建议在专业人员指导下使用
- 清理后需要重启系统

## 兼容性
- Windows 10/11
- Windows Server 2016/2019/2022
                ''',
            },
            {
                'name': 'LockBit防护配置',
                'description': '全面的LockBit勒索软件防护配置指南，包含系统加固、访问控制、数据备份和监控告警的最佳实践。',
                'content': '''
# LockBit防护配置指南

## 预防措施
为了防止LockBit勒索软件感染，建议采取以下防护措施：

### 1. 系统加固
- 及时安装系统补丁
- 禁用不必要的服务
- 配置防火墙规则

### 2. 访问控制
- 实施最小权限原则
- 启用多因素认证
- 定期审查用户权限

### 3. 数据备份
- 建立3-2-1备份策略
- 定期测试备份恢复
- 离线备份存储

### 4. 监控告警
- 部署EDR解决方案
- 配置异常行为检测
- 建立应急响应流程

## 配置文件
提供了详细的防护配置模板和脚本。
                ''',
            },
            {
                'name': 'LockBit IOC提取器',
                'description': '自动提取LockBit勒索软件威胁指标的工具，支持多种输出格式，可与SIEM平台和威胁情报系统集成。',
                'content': '''
# LockBit IOC提取器

## 工具说明
自动提取LockBit勒索软件相关的威胁指标（IOC），用于威胁狩猎和防护配置。

## 提取内容
- 文件哈希值（MD5, SHA1, SHA256）
- 网络指标（IP地址、域名、URL）
- 注册表键值
- 文件路径和名称

## 输出格式
- STIX/TAXII格式
- JSON格式
- CSV格式
- YARA规则

## 集成支持
- SIEM平台集成
- 威胁情报平台
- 安全编排工具
                ''',
            }
        ]

        created_count = 0
        for tool_data in tools_data:
            tool, created = Tools.objects.get_or_create(
                name=tool_data['name'],
                ransomware_group=group,
                defaults={
                    'description': tool_data['description'],
                    'content': tool_data['content']
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'成功创建工具: {tool.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'工具已存在: {tool.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'完成！共创建了 {created_count} 个新工具')
        )
