"""
删除数据库中所有勒索组织相关数据的管理命令
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from apps.group.models import RansomwareGroup, Tools, NegotiationRecord, RansomNote


class Command(BaseCommand):
    help = '删除数据库中所有勒索组织相关的数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='确认删除所有勒索组织数据（必须提供此参数才能执行删除操作）',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要删除的数据统计，不实际执行删除操作',
        )

    def handle(self, *args, **options):
        # 获取当前数据统计
        groups_count = RansomwareGroup.objects.count()
        tools_count = Tools.objects.count()
        negotiation_records_count = NegotiationRecord.objects.count()
        ransom_notes_count = RansomNote.objects.count()

        self.stdout.write(
            self.style.WARNING('\n=== 当前数据库中勒索组织相关数据统计 ===')
        )
        self.stdout.write(f'勒索组织数量: {groups_count}')
        self.stdout.write(f'应急工具数量: {tools_count}')
        self.stdout.write(f'谈判记录数量: {negotiation_records_count}')
        self.stdout.write(f'勒索信数量: {ransom_notes_count}')
        self.stdout.write(f'总计: {groups_count + tools_count + negotiation_records_count + ransom_notes_count} 条记录\n')

        # 如果是dry-run模式，只显示统计信息
        if options['dry_run']:
            self.stdout.write(
                self.style.SUCCESS('这是一次试运行，没有实际删除任何数据。')
            )
            return

        # 检查是否有数据需要删除
        if groups_count == 0 and tools_count == 0 and negotiation_records_count == 0 and ransom_notes_count == 0:
            self.stdout.write(
                self.style.SUCCESS('数据库中没有勒索组织相关数据需要删除。')
            )
            return

        # 检查确认参数
        if not options['confirm']:
            self.stdout.write(
                self.style.ERROR(
                    '⚠️  警告：此操作将永久删除所有勒索组织相关数据！\n'
                    '如果确认要执行删除操作，请添加 --confirm 参数：\n'
                    'python manage.py delete_all_ransomware_data --confirm'
                )
            )
            return

        # 再次确认
        self.stdout.write(
            self.style.ERROR(
                '⚠️  最后确认：您即将删除所有勒索组织相关数据，此操作不可撤销！'
            )
        )
        
        try:
            # 使用数据库事务确保数据一致性
            with transaction.atomic():
                self.stdout.write('\n开始删除数据...\n')

                # 1. 删除应急工具（有外键关联到勒索组织）
                if tools_count > 0:
                    self.stdout.write('正在删除应急工具数据...')
                    Tools.objects.all().delete()
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ 已删除 {tools_count} 条应急工具记录')
                    )

                # 2. 删除谈判记录（有外键关联到勒索组织）
                if negotiation_records_count > 0:
                    self.stdout.write('正在删除谈判记录数据...')
                    NegotiationRecord.objects.all().delete()
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ 已删除 {negotiation_records_count} 条谈判记录')
                    )

                # 3. 删除勒索信（有外键关联到勒索组织）
                if ransom_notes_count > 0:
                    self.stdout.write('正在删除勒索信数据...')
                    RansomNote.objects.all().delete()
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ 已删除 {ransom_notes_count} 条勒索信记录')
                    )

                # 4. 最后删除勒索组织（主表）
                if groups_count > 0:
                    self.stdout.write('正在删除勒索组织数据...')
                    RansomwareGroup.objects.all().delete()
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ 已删除 {groups_count} 条勒索组织记录')
                    )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'\n🎉 删除操作完成！共删除了 {groups_count + tools_count + negotiation_records_count + ransom_notes_count} 条记录。'
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 删除操作失败：{str(e)}')
            )
            raise

        # 验证删除结果
        remaining_groups = RansomwareGroup.objects.count()
        remaining_tools = Tools.objects.count()
        remaining_negotiations = NegotiationRecord.objects.count()
        remaining_notes = RansomNote.objects.count()

        if remaining_groups == 0 and remaining_tools == 0 and remaining_negotiations == 0 and remaining_notes == 0:
            self.stdout.write(
                self.style.SUCCESS('\n✅ 验证完成：所有勒索组织相关数据已成功删除。')
            )
        else:
            self.stdout.write(
                self.style.WARNING(
                    f'\n⚠️  警告：仍有部分数据未删除：\n'
                    f'勒索组织: {remaining_groups}, 工具: {remaining_tools}, '
                    f'谈判记录: {remaining_negotiations}, 勒索信: {remaining_notes}'
                )
            )
