from django.core.management.base import BaseCommand
from apps.group.models import RansomwareGroup
import json


class Command(BaseCommand):
    help = '显示勒索软件组织的详细信息'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            help='组织名称',
            default='LockBit'
        )

    def handle(self, *args, **options):
        name = options['name']
        
        try:
            group = RansomwareGroup.objects.get(name=name)
        except RansomwareGroup.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'未找到名为 "{name}" 的勒索软件组织')
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f'\n=== {group.name} 勒索软件组织详细信息 ===\n')
        )

        # 基本信息
        self.stdout.write(self.style.WARNING('【基本信息】'))
        self.stdout.write(f'组织名称: {group.name}')
        self.stdout.write(f'组织ID: {group.id}')
        self.stdout.write(f'别名: {", ".join(group.aliases) if group.aliases else "无"}')
        self.stdout.write(f'活跃状态: {group.get_status_display()}')
        self.stdout.write(f'威胁等级: {group.get_threat_level_display()}')
        self.stdout.write(f'首次发现: {group.first_seen}')
        self.stdout.write(f'最后活动: {group.last_activity}')
        self.stdout.write(f'已知受害者数量: {group.victim_count}')
        
        # 技术特征
        self.stdout.write(f'\n{self.style.WARNING("【技术特征】")}')
        self.stdout.write(f'勒索软件家族: {", ".join(group.ransomware_families)}')
        self.stdout.write(f'加密算法: {", ".join(group.encryption_algorithms)}')
        self.stdout.write(f'攻击手段: {", ".join(group.attack_vectors)}')
        self.stdout.write(f'目标行业: {", ".join(group.target_industries)}')
        self.stdout.write(f'目标地区: {", ".join(group.target_regions)}')
        self.stdout.write(f'目标操作系统: {", ".join(group.operating_systems)}')

        # 运营信息
        self.stdout.write(f'\n{self.style.WARNING("【运营信息】")}')
        self.stdout.write(f'赎金范围: {group.get_ransom_range_display()}')
        self.stdout.write(f'支付方式: {", ".join(group.payment_methods)}')
        self.stdout.write(f'联系方式: {", ".join(group.contact_methods)}')


        # 关联信息
        self.stdout.write(f'\n{self.style.WARNING("【关联信息】")}')
        self.stdout.write(f'已知受害者数量: {group.victim_count}')
        self.stdout.write(f'恶意软件样本数量: {len(group.malware_samples)}')
        self.stdout.write(f'数据来源: {", ".join(group.data_sources)}')
        self.stdout.write(f'通信渠道: {", ".join(group.communication_channel)}')

        # IOC指标
        if group.ioc_indicators:
            self.stdout.write(f'\n{self.style.WARNING("【IOC指标】")}')
            for key, value in group.ioc_indicators.items():
                if isinstance(value, list):
                    self.stdout.write(f'{key}: {", ".join(value)}')
                else:
                    self.stdout.write(f'{key}: {value}')

        # 描述信息
        if group.description:
            self.stdout.write(f'\n{self.style.WARNING("【组织描述】")}')
            self.stdout.write(group.description)

        # 谈判策略
        if group.negotiation_tactics:
            self.stdout.write(f'\n{self.style.WARNING("【谈判策略】")}')
            self.stdout.write(group.negotiation_tactics)

        # 位置信息
        if group.locations:
            self.stdout.write(f'\n{self.style.WARNING("【位置信息】")}')
            for location in group.locations:
                self.stdout.write(f'  - {location}')

        # 受害者信息
        if group.victim:
            self.stdout.write(f'\n{self.style.WARNING("【受害者信息】")}')
            for victim in group.victim[:3]:  # 只显示前3个
                self.stdout.write(f'  - {victim.get("name", "未知")} ({victim.get("industry", "未知行业")}) - {victim.get("status", "未知状态")}')
            if len(group.victim) > 3:
                self.stdout.write(f'  ... 还有 {len(group.victim) - 3} 个受害者')

        # 勒索信样本
        if group.note:
            self.stdout.write(f'\n{self.style.WARNING("【勒索信样本】")}')
            for note in group.note[:2]:  # 只显示前2个
                self.stdout.write(f'文件名: {note.get("filename", "未知")}')
                content = note.get("content", "")
                if len(content) > 200:
                    content = content[:200] + "..."
                self.stdout.write(f'内容预览: {content}')
                self.stdout.write("")

        # 统计信息
        self.stdout.write(f'\n{self.style.SUCCESS("【统计信息】")}')
        self.stdout.write(f'创建时间: {group.created_at}')
        self.stdout.write(f'更新时间: {group.updated_at}')
        if group.get_activity_duration_days():
            self.stdout.write(f'活动持续天数: {group.get_activity_duration_days()} 天')
        self.stdout.write(f'当前是否活跃: {"是" if group.is_currently_active() else "否"}')
