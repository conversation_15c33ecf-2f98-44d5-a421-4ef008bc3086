from django.core.management.base import BaseCommand
from apps.group.models import Tools


class Command(BaseCommand):
    help = '更新现有工具的描述字段'

    def handle(self, *args, **options):
        # 工具描述数据
        tools_descriptions = {
            'LockBit解密工具': '专门针对LockBit勒索软件的解密工具，可以帮助受害者恢复被加密的文件。支持多种文件格式，操作简单，安全可靠。',
            'LockBit检测脚本': '自动检测系统是否感染LockBit勒索软件的Python脚本，提供全面的系统扫描和威胁评估功能。',
            'LockBit清除工具': '专业的LockBit勒索软件清除工具，能够彻底清理恶意文件、注册表项和系统配置，恢复系统正常状态。',
            'LockBit防护配置': '全面的LockBit勒索软件防护配置指南，包含系统加固、访问控制、数据备份和监控告警的最佳实践。',
            'LockBit IOC提取器': '自动提取LockBit勒索软件威胁指标的工具，支持多种输出格式，可与SIEM平台和威胁情报系统集成。'
        }

        updated_count = 0
        for tool_name, description in tools_descriptions.items():
            try:
                tool = Tools.objects.get(name=tool_name)
                tool.description = description
                tool.save()
                updated_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'成功更新工具描述: {tool.name}')
                )
            except Tools.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'工具不存在: {tool_name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'完成！共更新了 {updated_count} 个工具的描述')
        )
