# Generated by Django 5.2.3 on 2025-07-03 15:26

import mdeditor.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="RansomwareGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="勒索软件组织的主要名称",
                        max_length=200,
                        unique=True,
                        verbose_name="组织名称",
                    ),
                ),
                (
                    "aliases",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="组织的其他已知名称，存储为JSON数组",
                        verbose_name="别名列表",
                    ),
                ),
                (
                    "description",
                    mdeditor.fields.MDTextField(
                        blank=True,
                        help_text="组织的详细描述和背景信息",
                        verbose_name="组织描述",
                    ),
                ),
                (
                    "external_information_source",
                    mdeditor.fields.MDTextField(
                        blank=True,
                        help_text="外部信息来源链接",
                        verbose_name="外部信息来源",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "活跃"),
                            ("inactive", "不活跃"),
                            ("disbanded", "已解散"),
                            ("unknown", "未知"),
                        ],
                        default="unknown",
                        max_length=20,
                        verbose_name="活跃状态",
                    ),
                ),
                (
                    "first_seen",
                    models.DateTimeField(
                        blank=True,
                        help_text="该组织首次被发现的时间",
                        null=True,
                        verbose_name="首次发现时间",
                    ),
                ),
                (
                    "last_activity",
                    models.DateTimeField(
                        blank=True,
                        help_text="该组织最后一次已知活动的时间",
                        null=True,
                        verbose_name="最后活动时间",
                    ),
                ),
                (
                    "ransomware_families",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="该组织使用的勒索软件家族名称列表",
                        verbose_name="勒索软件家族",
                    ),
                ),
                (
                    "encryption_algorithms",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="使用的加密算法列表，如AES、RSA等",
                        verbose_name="加密算法",
                    ),
                ),
                (
                    "attack_vectors",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="常用的攻击手段和技术，如钓鱼邮件、RDP暴力破解等",
                        verbose_name="攻击手段",
                    ),
                ),
                (
                    "known_sites",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="已知站点",
                        verbose_name="已知站点",
                    ),
                ),
                (
                    "victim",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="受害者列表",
                        verbose_name="受害者",
                    ),
                ),
                (
                    "negotiation_record",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="谈判记录",
                        verbose_name="谈判记录",
                    ),
                ),
                (
                    "note",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="勒索信",
                        verbose_name="勒索信",
                    ),
                ),
                (
                    "target_industries",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="主要攻击的行业类型",
                        verbose_name="目标行业",
                    ),
                ),
                (
                    "target_regions",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="主要攻击的地理区域",
                        verbose_name="目标地区",
                    ),
                ),
                (
                    "operating_systems",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="主要攻击的操作系统类型",
                        verbose_name="目标操作系统",
                    ),
                ),
                (
                    "ransom_amount_min",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="要求的最低赎金金额，以美元计算",
                        max_digits=15,
                        null=True,
                        verbose_name="最低赎金金额(USD)",
                    ),
                ),
                (
                    "ransom_amount_max",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="要求的最高赎金金额，以美元计算",
                        max_digits=15,
                        null=True,
                        verbose_name="最高赎金金额(USD)",
                    ),
                ),
                (
                    "payment_methods",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="接受的支付方式列表",
                        verbose_name="支付方式",
                    ),
                ),
                (
                    "contact_methods",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="与受害者联系的方式，如邮箱、暗网链接等",
                        verbose_name="联系方式",
                    ),
                ),
                (
                    "leak_sites",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="用于发布受害者数据的网站列表",
                        verbose_name="泄露站点",
                    ),
                ),
                (
                    "negotiation_tactics",
                    mdeditor.fields.MDTextField(
                        blank=True,
                        help_text="该组织在勒索谈判中的常用策略和话术",
                        verbose_name="谈判策略",
                    ),
                ),
                (
                    "ioc_indicators",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="相关的威胁指标，包括IP、域名、文件哈希等",
                        verbose_name="IOC指标",
                    ),
                ),
                (
                    "malware_samples",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="相关恶意软件样本的哈希值列表",
                        verbose_name="恶意软件样本",
                    ),
                ),
                (
                    "threat_level",
                    models.CharField(
                        choices=[
                            ("low", "低"),
                            ("medium", "中"),
                            ("high", "高"),
                            ("critical", "严重"),
                        ],
                        default="medium",
                        max_length=20,
                        verbose_name="威胁等级",
                    ),
                ),
                (
                    "victim_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="已确认的受害者数量",
                        verbose_name="已知受害者数量",
                    ),
                ),
                (
                    "data_sources",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="情报数据的来源列表，如暗网、Telegram、安全厂商等",
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "related_groups",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="与该组织有关联的其他勒索组织ID列表",
                        verbose_name="关联组织",
                    ),
                ),
            ],
            options={
                "verbose_name": "勒索组织",
                "verbose_name_plural": "勒索组织",
                "ordering": ["-last_activity", "-created_at"],
                "indexes": [
                    models.Index(fields=["name"], name="group_name_idx"),
                    models.Index(fields=["status"], name="group_status_idx"),
                    models.Index(
                        fields=["threat_level"], name="group_threat_level_idx"
                    ),
                    models.Index(fields=["first_seen"], name="group_first_seen_idx"),
                    models.Index(
                        fields=["last_activity"], name="group_last_activity_idx"
                    ),
                    models.Index(fields=["created_at"], name="group_created_at_idx"),
                    models.Index(
                        fields=["status", "threat_level"],
                        name="group_status_threat_idx",
                    ),
                ],
            },
        ),
    ]
