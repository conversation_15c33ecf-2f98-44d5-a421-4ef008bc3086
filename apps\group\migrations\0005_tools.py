# Generated by Django 5.2.3 on 2025-07-14 10:23

import django.db.models.deletion
import mdeditor.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0004_ransomwaregroup_avg_delay_days_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Tools',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(help_text='勒索软件组织使用的工具名称', max_length=200, unique=True, verbose_name='工具名称')),
                ('content', mdeditor.fields.MDTextField(blank=True, help_text='工具的详细描述和背景信息', verbose_name='工具描述')),
                ('file', models.FileField(blank=True, help_text='工具的可执行文件', null=True, upload_to='tools/', verbose_name='工具文件')),
                ('ransomware_group', models.ForeignKey(help_text='该工具被勒索组织使用', on_delete=django.db.models.deletion.CASCADE, related_name='tools', to='group.ransomwaregroup', verbose_name='勒索组织')),
            ],
            options={
                'verbose_name': '应急工具',
                'verbose_name_plural': '应急工具',
            },
        ),
    ]
