# Generated by Django 5.2.3 on 2025-07-14 10:42

import mdeditor.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0005_tools'),
    ]

    operations = [
        migrations.AddField(
            model_name='tools',
            name='description',
            field=models.TextField(blank=True, help_text='工具的详细描述和背景信息', verbose_name='工具描述'),
        ),
        migrations.AlterField(
            model_name='tools',
            name='content',
            field=mdeditor.fields.MDTextField(blank=True, help_text='工具的详细内容，支持Markdown格式', verbose_name='工具内容'),
        ),
        migrations.AlterField(
            model_name='tools',
            name='name',
            field=models.CharField(help_text='勒索软件组织使用的工具名称', max_length=200, verbose_name='工具名称'),
        ),
    ]
