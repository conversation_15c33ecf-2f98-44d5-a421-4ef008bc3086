# Generated by Django 5.2.3 on 2025-07-16 10:36

from django.db import migrations
from django.utils.text import slugify


def populate_slug_values(apps, schema_editor):
    """为现有的勒索组织记录生成slug值"""
    RansomwareGroup = apps.get_model('group', 'RansomwareGroup')

    for group in RansomwareGroup.objects.all():
        if not group.slug:
            # 基于name生成slug
            base_slug = slugify(group.name)
            slug = base_slug
            counter = 1

            # 确保slug唯一性
            while RansomwareGroup.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1

            group.slug = slug
            group.save(update_fields=['slug'])
            print(f"为组织 '{group.name}' 生成slug: '{slug}'")


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0008_add_slug_field'),
    ]

    operations = [
        migrations.RunPython(populate_slug_values, reverse_code=migrations.RunPython.noop),
    ]
