# Generated by Django 5.1.3 on 2025-07-16 21:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0013_add_logo_field'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='negotiation_record',
        ),
        migrations.AlterField(
            model_name='tools',
            name='name',
            field=models.CharField(help_text='解密工具、检测脚本、清除工具、防护配置、IOC提取器等', max_length=200, verbose_name='应急工具名称'),
        ),
        migrations.CreateModel(
            name='NegotiationRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('initial_ransom', models.Char<PERSON>ield(blank=True, help_text='勒索组织最初要求的赎金金额，如 $600,000', max_length=100, null=True, verbose_name='初始赎金要求')),
                ('negotiated_ransom', models.CharField(blank=True, help_text='经过谈判后的最终赎金金额，如 $200,000', max_length=100, null=True, verbose_name='谈判后赎金')),
                ('paid', models.BooleanField(default=False, help_text='受害者是否已支付赎金', verbose_name='是否已支付')),
                ('payment_status', models.CharField(choices=[('unpaid', '未支付'), ('partial', '部分支付'), ('paid', '已支付'), ('unknown', '未知')], default='unknown', help_text='详细的支付状态', max_length=20, verbose_name='支付状态')),
                ('message_count', models.PositiveIntegerField(default=0, help_text='谈判过程中的消息总数', verbose_name='消息数量')),
                ('status', models.CharField(choices=[('ongoing', '进行中'), ('completed', '已完成'), ('failed', '失败'), ('abandoned', '已放弃')], default='ongoing', help_text='当前谈判的状态', max_length=20, verbose_name='谈判状态')),
                ('messages', models.JSONField(blank=True, default=list, help_text='完整的谈判对话记录，包含party、content和timestamp字段', verbose_name='谈判消息')),
                ('negotiation_start_time', models.DateTimeField(blank=True, help_text='谈判开始的时间', null=True, verbose_name='谈判开始时间')),
                ('negotiation_end_time', models.DateTimeField(blank=True, help_text='谈判结束的时间', null=True, verbose_name='谈判结束时间')),
                ('notes', models.TextField(blank=True, help_text='关于此次谈判的额外备注信息', verbose_name='备注')),
                ('data_sources', models.JSONField(blank=True, default=list, help_text='此谈判记录的数据来源', verbose_name='数据来源')),
                ('ransomware_group', models.ForeignKey(help_text='关联的勒索组织', on_delete=django.db.models.deletion.CASCADE, related_name='negotiation_records', to='group.ransomwaregroup', verbose_name='勒索组织')),
            ],
            options={
                'verbose_name': '谈判记录',
                'verbose_name_plural': '谈判记录',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['ransomware_group'], name='negotiation_group_idx'), models.Index(fields=['status'], name='negotiation_status_idx'), models.Index(fields=['paid'], name='negotiation_paid_idx'), models.Index(fields=['created_at'], name='negotiation_created_at_idx'), models.Index(fields=['ransomware_group', 'status'], name='negotiation_group_status_idx'), models.Index(fields=['ransomware_group', 'paid'], name='negotiation_group_paid_idx')],
            },
        ),
    ]
