# Generated by Django 5.1.3 on 2025-07-16 22:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0014_add_negotiation_record_model'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='note',
        ),
        migrations.CreateModel(
            name='RansomNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('note_name', models.CharField(help_text='勒索信的文件名或标识名称，如 README、HOW_TO_DECRYPT', max_length=200, verbose_name='勒索信名称')),
                ('extension', models.CharField(default='.txt', help_text='勒索信文件的扩展名，如 .txt、.html、.hta', max_length=20, verbose_name='文件扩展名')),
                ('content', models.TextField(help_text='勒索信的完整文本内容', verbose_name='勒索信内容')),
                ('group', models.ForeignKey(help_text='关联的勒索组织', on_delete=django.db.models.deletion.CASCADE, related_name='ransom_notes', to='group.ransomwaregroup', verbose_name='勒索组织')),
            ],
            options={
                'verbose_name': '勒索信',
                'verbose_name_plural': '勒索信',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['group'], name='ransom_note_group_idx'), models.Index(fields=['note_name'], name='ransom_note_name_idx'), models.Index(fields=['created_at'], name='ransom_note_created_at_idx'), models.Index(fields=['group', 'note_name'], name='ransom_note_group_name_idx')],
                'constraints': [models.UniqueConstraint(fields=('group', 'note_name', 'extension'), name='unique_ransom_note_per_group')],
            },
        ),
    ]
