# Generated by Django 5.1.2 on 2025-07-17 09:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0017_remove_ransom_note_indexes'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='negotiationrecord',
            name='negotiation_group_idx',
        ),
        migrations.RemoveIndex(
            model_name='negotiationrecord',
            name='negotiation_status_idx',
        ),
        migrations.RemoveIndex(
            model_name='negotiationrecord',
            name='negotiation_group_status_idx',
        ),
        migrations.RemoveIndex(
            model_name='negotiationrecord',
            name='negotiation_group_paid_idx',
        ),
        migrations.RenameField(
            model_name='negotiationrecord',
            old_name='ransomware_group',
            new_name='group',
        ),
        migrations.RenameField(
            model_name='negotiationrecord',
            old_name='initial_ransom',
            new_name='initialransom',
        ),
        migrations.RenameField(
            model_name='negotiationrecord',
            old_name='negotiated_ransom',
            new_name='negotiatedransom',
        ),
        migrations.RemoveField(
            model_name='negotiationrecord',
            name='data_sources',
        ),
        migrations.RemoveField(
            model_name='negotiationrecord',
            name='negotiation_end_time',
        ),
        migrations.RemoveField(
            model_name='negotiationrecord',
            name='negotiation_start_time',
        ),
        migrations.RemoveField(
            model_name='negotiationrecord',
            name='notes',
        ),
        migrations.RemoveField(
            model_name='negotiationrecord',
            name='payment_status',
        ),
        migrations.RemoveField(
            model_name='negotiationrecord',
            name='status',
        ),
        migrations.AddIndex(
            model_name='negotiationrecord',
            index=models.Index(fields=['group'], name='negotiation_group_idx'),
        ),
        migrations.AddIndex(
            model_name='negotiationrecord',
            index=models.Index(fields=['group', 'paid'], name='negotiation_group_paid_idx'),
        ),
    ]
