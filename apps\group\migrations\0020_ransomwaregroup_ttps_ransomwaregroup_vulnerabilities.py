# Generated by Django 5.1.2 on 2025-07-21 09:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0019_remove_negotiation_indexes'),
    ]

    operations = [
        migrations.AddField(
            model_name='ransomwaregroup',
            name='ttps',
            field=models.JSONField(blank=True, default=list, help_text='基于MITRE ATT&CK框架的战术、技术和程序信息，包含tactic_id、tactic_name、techniques等', verbose_name='TTPs (战术技术程序)'),
        ),
        migrations.AddField(
            model_name='ransomwaregroup',
            name='vulnerabilities',
            field=models.JSONField(blank=True, default=list, help_text='该组织利用的CVE漏洞信息，包含Vendor、Product、CVE、CVSS、severity等字段', verbose_name='利用的漏洞'),
        ),
    ]
