# Generated by Django 5.1.2 on 2025-07-21 09:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0020_ransomwaregroup_ttps_ransomwaregroup_vulnerabilities'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='aliases',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='attack_vectors',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='avg_delay_days',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='communication_channel',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='contact_methods',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='data_sources',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='encryption_algorithms',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='info_stealer_percentage',
        ),
        migrations.<PERSON>mo<PERSON><PERSON><PERSON>(
            model_name='ransomwaregroup',
            name='ioc_indicators',
        ),
        migrations.Remove<PERSON>ield(
            model_name='ransomwaregroup',
            name='malware_samples',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='negotiation_tactics',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='operating_systems',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='payment_methods',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='ransom_amount_max',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='ransom_amount_min',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='ransomware_families',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='related_groups',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='target_industries',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='target_regions',
        ),
        migrations.RemoveField(
            model_name='ransomwaregroup',
            name='victim',
        ),
        migrations.AlterField(
            model_name='ransomwaregroup',
            name='locations',
            field=models.JSONField(blank=True, default=list, help_text='该组织的暗网站点信息，包含fqdn、title、slug、available、type等字段', verbose_name='已知站点'),
        ),
        migrations.AlterField(
            model_name='ransomwaregroup',
            name='tools_used',
            field=models.JSONField(blank=True, default=dict, help_text='该组织使用的工具分类，如Exfiltration、RMM-Tools、DiscoveryEnum等', verbose_name='使用的工具'),
        ),
    ]
