# Generated by Django 5.1.2 on 2025-07-21 10:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0021_remove_ransomwaregroup_aliases_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='ransomwaregroup',
            name='aliases',
            field=models.JSONField(blank=True, default=list, help_text='组织的其他已知名称，存储为JSON数组', verbose_name='别名列表'),
        ),
        migrations.AddField(
            model_name='ransomwaregroup',
            name='avg_delay_days',
            field=models.CharField(blank=True, help_text='平均延迟', max_length=200, null=True, verbose_name='平均延迟'),
        ),
        migrations.AddField(
            model_name='ransomwaregroup',
            name='data_sources',
            field=models.JSONField(blank=True, default=list, help_text='情报数据的来源列表，如暗网、Telegram、安全厂商等', verbose_name='数据来源'),
        ),
        migrations.AddField(
            model_name='ransomwaregroup',
            name='info_stealer_percentage',
            field=models.CharField(blank=True, help_text='信息延迟比例', max_length=200, null=True, verbose_name='信息延迟比例'),
        ),
    ]
