# Generated by Django 5.2.3 on 2025-07-21 10:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0022_ransomwaregroup_aliases_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='IOCIndicator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('ioc_types', models.JSONField(blank=True, default=list, help_text='IOC的类型，如IP、域名、哈希等', verbose_name='IOC类型')),
                ('iocs', models.JSONField(blank=True, default=dict, help_text='具体的IOC值列表', verbose_name='IOC值')),
                ('group', models.ForeignKey(help_text='关联的勒索组织', on_delete=django.db.models.deletion.CASCADE, related_name='ioc_indicators', to='group.ransomwaregroup', verbose_name='勒索组织')),
            ],
            options={
                'verbose_name': 'IOC指标',
                'verbose_name_plural': 'IOC指标',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Victim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('post_title', models.CharField(help_text='受害者的帖子标题', max_length=200, verbose_name='帖子标题')),
                ('discovered', models.DateTimeField(help_text='受害者被发现的时间', verbose_name='发现时间')),
                ('description', models.TextField(blank=True, help_text='受害者的详细描述', verbose_name='受害者描述')),
                ('website', models.URLField(blank=True, help_text='受害者的官方网站', verbose_name='受害者网站')),
                ('published', models.DateTimeField(help_text='受害者帖子的发布时间', verbose_name='发布时间')),
                ('post_url', models.URLField(help_text='受害者的帖子链接', verbose_name='帖子链接')),
                ('country', models.CharField(help_text='受害者的所在国家', max_length=100, verbose_name='受害国家')),
                ('activity', models.CharField(help_text='受害者的活动状态', max_length=100, verbose_name='活动状态')),
                ('duplicates', models.JSONField(blank=True, default=list, help_text='重复的受害者记录', verbose_name='重复记录')),
                ('extrainfos', models.JSONField(blank=True, default=dict, help_text='受害者的额外信息', verbose_name='额外信息')),
                ('screenshot', models.URLField(blank=True, help_text='受害者的截图链接', verbose_name='截图链接')),
                ('infostealer', models.JSONField(blank=True, default=dict, help_text='信息窃取者的信息', verbose_name='信息窃取者')),
                ('press', models.JSONField(blank=True, default=dict, help_text='相关的媒体报道信息', verbose_name='媒体报道')),
                ('permalink', models.URLField(blank=True, help_text='受害者信息的信息来源', verbose_name='信息来源')),
                ('group', models.ManyToManyField(help_text='关联的勒索组织', related_name='victims', to='group.ransomwaregroup', verbose_name='勒索组织')),
            ],
            options={
                'verbose_name': '受害者',
                'verbose_name_plural': '受害者',
                'ordering': ['-created_at'],
            },
        ),
    ]
