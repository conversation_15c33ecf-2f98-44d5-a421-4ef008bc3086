# Generated by Django 5.2.3 on 2025-07-22 16:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('group', '0024_alter_victim_activity_alter_victim_country_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='tools',
            name='seo_description',
            field=models.TextField(blank=True, help_text='用于搜索引擎优化的页面描述，建议150-300字符', max_length=300, verbose_name='SEO描述'),
        ),
        migrations.AddField(
            model_name='tools',
            name='seo_keywords',
            field=models.CharField(blank=True, help_text='用于搜索引擎优化的关键词，多个关键词用逗号分隔', max_length=500, verbose_name='SEO关键词'),
        ),
        migrations.AlterField(
            model_name='tools',
            name='ransomware_group',
            field=models.ForeignKey(blank=True, help_text='该工具被勒索组织使用', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tools', to='group.ransomwaregroup', verbose_name='勒索组织'),
        ),
    ]
