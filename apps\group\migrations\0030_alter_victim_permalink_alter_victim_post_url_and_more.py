# Generated by Django 5.2.3 on 2025-07-23 17:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("group", "0029_alter_victim_post_url"),
    ]

    operations = [
        migrations.AlterField(
            model_name="victim",
            name="permalink",
            field=models.URLField(
                blank=True,
                help_text="受害者信息的信息来源",
                max_length=600,
                verbose_name="信息来源",
            ),
        ),
        migrations.AlterField(
            model_name="victim",
            name="post_url",
            field=models.URLField(
                blank=True,
                help_text="受害者的帖子链接",
                max_length=600,
                null=True,
                verbose_name="帖子链接",
            ),
        ),
        migrations.AlterField(
            model_name="victim",
            name="screenshot",
            field=models.URLField(
                blank=True,
                help_text="受害者的截图链接",
                max_length=600,
                verbose_name="截图链接",
            ),
        ),
        migrations.AlterField(
            model_name="victim",
            name="website",
            field=models.URLField(
                blank=True,
                help_text="受害者的官方网站",
                max_length=600,
                verbose_name="受害者网站",
            ),
        ),
    ]
