from django.db import models
from config.models import BaseModel
from mdeditor.fields import MDTextField


class RansomwareGroup(BaseModel):
    """
    勒索软件组织模型
    """

    # 活跃状态选择
    STATUS_CHOICES = [
        ("active", "活跃"),
        ("inactive", "不活跃"),
        ("disbanded", "已解散"),
        ("unknown", "未知"),
    ]

    # 威胁等级选择
    THREAT_LEVEL_CHOICES = [
        ("low", "低"),
        ("medium", "中"),
        ("high", "高"),
        ("critical", "严重"),
    ]

    # 支付方式选择
    PAYMENT_METHOD_CHOICES = [
        ("bitcoin", "比特币"),
        ("monero", "门罗币"),
        ("ethereum", "以太坊"),
        ("other_crypto", "其他加密货币"),
        ("mixed", "混合支付"),
        ("unknown", "未知"),
    ]

    # ===== 基本信息字段 =====
    name = models.CharField(
        max_length=200,
        unique=True,
        verbose_name="组织名称",
        help_text="勒索软件组织的主要名称",
    )

    logo = models.ImageField(
        upload_to="group_logos/",
        null=True,
        blank=True,
        verbose_name="组织标志",
        help_text="勒索软件组织的标志图片",
    )

    slug = models.SlugField(
        max_length=200,
        unique=True,
        verbose_name="URL标识",
        help_text="用于URL的唯一标识符",
    )

    avg_delay_days = models.CharField(
        max_length=200,
        verbose_name="平均延迟",
        help_text="平均延迟",
        null=True,
        blank=True,
    )

    info_stealer_percentage = models.CharField(
        max_length=200,
        verbose_name="信息延迟比例",
        help_text="信息延迟比例",
        null=True,
        blank=True,
    )

    aliases = models.JSONField(
        default=list,
        blank=True,
        verbose_name="别名列表",
        help_text="组织的其他已知名称，存储为JSON数组",
    )

    description = MDTextField(
        blank=True, verbose_name="组织描述", help_text="组织的详细描述和背景信息"
    )

    external_information_source = MDTextField(
        blank=True, verbose_name="外部信息来源", help_text="外部信息来源链接"
    )

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="unknown",
        verbose_name="活跃状态",
    )

    first_seen = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="首次发现时间",
        help_text="该组织首次被发现的时间",
    )

    last_activity = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="最后活动时间",
        help_text="该组织最后一次已知活动的时间",
    )

    # ===== 技术特征字段 =====
    tools_used = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="使用的工具",
        help_text="该组织使用的工具分类，如Exfiltration、RMM-Tools、DiscoveryEnum等",
    )

    locations = models.JSONField(
        default=list,
        blank=True,
        verbose_name="已知站点",
        help_text="该组织的暗网站点信息，包含fqdn、title、slug、available、type等字段",
    )



    # ===== 关联信息字段 =====
    threat_level = models.CharField(
        max_length=20,
        choices=THREAT_LEVEL_CHOICES,
        default="medium",
        verbose_name="威胁等级",
    )

    victim_count = models.PositiveIntegerField(
        default=0, verbose_name="已知受害者数量", help_text="已确认的受害者数量"
    )

    data_sources = models.JSONField(
        default=list,
        blank=True,
        verbose_name="数据来源",
        help_text="情报数据的来源列表，如暗网、Telegram、安全厂商等",
    )

    # ===== TTPs 和漏洞信息字段 =====
    ttps = models.JSONField(
        default=list,
        blank=True,
        verbose_name="TTPs (战术技术程序)",
        help_text="基于MITRE ATT&CK框架的战术、技术和程序信息，包含tactic_id、tactic_name、techniques等",
    )

    vulnerabilities = models.JSONField(
        default=list,
        blank=True,
        verbose_name="利用的漏洞",
        help_text="该组织利用的CVE漏洞信息，包含Vendor、Product、CVE、CVSS、severity等字段",
    )

    class Meta:
        verbose_name = "勒索组织"
        verbose_name_plural = "勒索组织"
        ordering = ["-last_activity", "-created_at"]

        # 数据库索引优化
        indexes = [
            models.Index(fields=["name"], name="group_name_idx"),
            models.Index(fields=["slug"], name="group_slug_idx"),
            models.Index(fields=["status"], name="group_status_idx"),
            models.Index(fields=["threat_level"], name="group_threat_level_idx"),
            models.Index(fields=["first_seen"], name="group_first_seen_idx"),
            models.Index(fields=["last_activity"], name="group_last_activity_idx"),
            models.Index(fields=["created_at"], name="group_created_at_idx"),
            # 复合索引用于常见查询
            models.Index(
                fields=["status", "threat_level"], name="group_status_threat_idx"
            ),
        ]

    def __str__(self):
        return self.name

    def get_aliases_display(self):
        """获取别名的显示字符串"""
        if self.aliases:
            return ", ".join(self.aliases)
        return "无别名"

    def is_currently_active(self):
        """判断组织是否当前活跃"""
        return self.status == "active"

    def get_activity_duration_days(self):
        """获取活动持续天数"""
        if self.first_seen and self.last_activity:
            return (self.last_activity - self.first_seen).days
        return None

    def clean(self):
        """模型验证"""
        from django.core.exceptions import ValidationError

        # 验证时间逻辑
        if (
            self.first_seen
            and self.last_activity
            and self.first_seen > self.last_activity
        ):
            raise ValidationError({"last_activity": "最后活动时间不能早于首次发现时间"})

    def save(self, *args, **kwargs):
        """重写保存方法，添加自动验证和slug生成"""
        # 如果没有slug，则自动生成
        if not self.slug:
            self.slug = self._generate_unique_slug()

        self.clean()
        super().save(*args, **kwargs)

    def _generate_unique_slug(self):
        """生成唯一的slug"""
        from django.utils.text import slugify

        base_slug = slugify(self.name)
        slug = base_slug
        counter = 1

        # 确保slug唯一性
        while RansomwareGroup.objects.filter(slug=slug).exclude(pk=self.pk).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1

        return slug


# 应急工具
class Tools(BaseModel):
    name = models.CharField(
        max_length=200,
        verbose_name="应急工具名称",
        help_text="解密工具、检测脚本、清除工具、防护配置、IOC提取器等",
    )

    description = models.TextField(
        blank=True, verbose_name="工具描述", help_text="工具的详细描述和背景信息"
    )

    content = MDTextField(
        blank=True,
        verbose_name="工具内容",
        help_text="工具的详细内容，支持Markdown格式",
    )

    file = models.FileField(
        upload_to="tools/",
        blank=True,
        null=True,
        verbose_name="工具文件",
        help_text="工具的可执行文件",
    )

    ransomware_group = models.ForeignKey(
        RansomwareGroup,
        on_delete=models.CASCADE,
        related_name="tools",
        verbose_name="勒索组织",
        help_text="该工具被勒索组织使用",
        null=True,
        blank=True,
    )

    view_count = models.PositiveIntegerField(
        default=0,
        verbose_name="浏览量",
        help_text="工具的浏览次数",
    )

    # ===== SEO字段 =====
    seo_keywords = models.CharField(
        max_length=500,
        blank=True,
        verbose_name="SEO关键词",
        help_text="用于搜索引擎优化的关键词，多个关键词用逗号分隔",
    )

    seo_description = models.TextField(
        max_length=300,
        blank=True,
        verbose_name="SEO描述",
        help_text="用于搜索引擎优化的页面描述，建议150-300字符",
    )

    class Meta:
        verbose_name = "应急工具"
        verbose_name_plural = "应急工具"

    def __str__(self):
        return self.name

    def increment_view_count(self):
        """增加浏览量"""
        self.view_count += 1
        self.save(update_fields=["view_count"])




class NegotiationRecord(BaseModel):
    """
    谈判记录模型
    """

    # ===== 基本信息字段 =====
    group = models.ForeignKey(
        RansomwareGroup,
        on_delete=models.CASCADE,
        related_name="negotiation_records",
        verbose_name="勒索组织",
        help_text="关联的勒索组织",
    )

    # ===== 赎金信息字段 =====
    initialransom = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="初始赎金要求",
        help_text="勒索组织最初要求的赎金金额，如 $600,000",
    )

    negotiatedransom = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="谈判后赎金",
        help_text="经过谈判后的最终赎金金额，如 $200,000",
    )

    paid = models.BooleanField(
        default=False,
        verbose_name="是否已支付",
        help_text="受害者是否已支付赎金",
    )

    # ===== 谈判过程信息 =====
    message_count = models.PositiveIntegerField(
        default=0,
        verbose_name="消息数量",
        help_text="谈判过程中的消息总数",
    )

    messages = models.JSONField(
        default=list,
        blank=True,
        verbose_name="谈判消息",
        help_text="完整的谈判对话记录，包含party、content和timestamp字段",
    )

    class Meta:
        verbose_name = "谈判记录"
        verbose_name_plural = "谈判记录"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.group.name} - 谈判记录 #{self.pk or 'New'}"

    def get_ransom_reduction_percentage(self):
        """计算赎金减少百分比"""
        if not self.initialransom or not self.negotiatedransom:
            return None

        try:
            # 提取数字部分（去除货币符号和逗号）
            import re

            initial_amount = float(re.sub(r"[^\d.]", "", self.initialransom))
            negotiated_amount = float(re.sub(r"[^\d.]", "", self.negotiatedransom))

            if initial_amount > 0:
                reduction = (
                    (initial_amount - negotiated_amount) / initial_amount
                ) * 100
                return round(reduction, 2)
        except (ValueError, TypeError):
            pass
        return None

    def get_victim_messages_count(self):
        """获取受害者发送的消息数量"""
        if not self.messages:
            return 0
        return len([msg for msg in self.messages if msg.get("party") == "Victim"])

    def get_attacker_messages_count(self):
        """获取攻击者发送的消息数量"""
        if not self.messages:
            return 0
        return len([msg for msg in self.messages if msg.get("party") != "Victim"])

    def clean(self):
        """模型验证"""
        from django.core.exceptions import ValidationError

        # 验证消息数量与实际消息列表的一致性
        if self.messages and self.message_count != len(self.messages):
            self.message_count = len(self.messages)

    def save(self, *args, **kwargs):
        """重写保存方法，添加自动验证"""
        self.clean()
        super().save(*args, **kwargs)


class RansomNote(BaseModel):
    """
    勒索信模型
    与勒索组织建立一对多关系
    """

    group = models.ForeignKey(
        RansomwareGroup,
        on_delete=models.CASCADE,
        related_name="ransom_notes",
        verbose_name="勒索组织",
        help_text="关联的勒索组织",
    )

    note_name = models.CharField(
        max_length=200,
        verbose_name="勒索信名称",
        help_text="勒索信的文件名或标识名称，如 README、HOW_TO_DECRYPT",
    )

    extension = models.CharField(
        max_length=20,
        default=".txt",
        verbose_name="文件扩展名",
        help_text="勒索信文件的扩展名，如 .txt、.html、.hta",
    )

    content = models.TextField(
        null=True,
        blank=True,
        verbose_name="勒索信内容",
        help_text="勒索信的完整文本内容",
    )

    class Meta:
        verbose_name = "勒索信"
        verbose_name_plural = "勒索信"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.group.name} - {self.note_name}{self.extension}"

    def get_content_preview(self, max_length=200):
        """获取内容预览"""
        if not self.content:
            return ""
        if len(self.content) <= max_length:
            return self.content
        return self.content[:max_length] + "..."

    def clean(self):
        """模型验证"""
        from django.core.exceptions import ValidationError

        # 验证扩展名格式
        if self.extension and not self.extension.startswith("."):
            self.extension = "." + self.extension

        # 验证内容不能为空
        if not self.content or not self.content.strip():
            raise ValidationError({"content": "勒索信内容不能为空"})

    def save(self, *args, **kwargs):
        """重写保存方法，添加自动验证"""
        self.clean()
        super().save(*args, **kwargs)


class IOCIndicator(BaseModel):
    """
    IOC指标模型
    与勒索组织建立一对多关系
    """

    group = models.ForeignKey(
        RansomwareGroup,
        on_delete=models.CASCADE,
        related_name="ioc_indicators",
        verbose_name="勒索组织",
        help_text="关联的勒索组织",
    )

    ioc_types = models.JSONField(
        default=list,
        blank=True,
        verbose_name="IOC类型",
        help_text="IOC的类型，如IP、域名、哈希等",
    )

    iocs = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="IOC值",
        help_text="具体的IOC值列表",
    )

    class Meta:
        verbose_name = "IOC指标"
        verbose_name_plural = "IOC指标"
        ordering = ["-created_at"]


# 受害者
class Victim(BaseModel):
    """
    受害者模型
    与勒索组织建立多对多关系
    """

    group = models.ForeignKey(
        RansomwareGroup,
        on_delete=models.CASCADE,
        related_name="victims",
        blank=True,
        null=True,
        verbose_name="勒索组织",
        help_text="关联的勒索组织",
    )

    post_title = models.CharField(
        max_length=500,
        verbose_name="帖子标题",
        help_text="受害者的帖子标题",
    )

    discovered = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="发现时间",
        help_text="受害者被发现的时间",
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name="受害者描述",
        help_text="受害者的详细描述",
    )

    website = models.URLField(
        max_length=600,
        blank=True,
        verbose_name="受害者网站",
        help_text="受害者的官方网站",
    )

    attack_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="攻击时间",
        help_text="攻击时间",
    )

    post_url = models.URLField(
        max_length=600,
        blank=True,
        null=True,
        verbose_name="帖子链接",
        help_text="受害者的帖子链接",
    )

    country = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="受害国家",
        help_text="受害者的所在国家",
    )

    activity = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="活动状态",
        help_text="受害者的活动状态",
    )

    duplicates = models.JSONField(
        default=list,
        blank=True,
        verbose_name="重复记录",
        help_text="重复的受害者记录",
    )

    extrainfos = models.JSONField(
        default=list,
        blank=True,
        verbose_name="额外信息",
        help_text="受害者的额外信息",
    )

    screenshot = models.URLField(
        max_length=600,
        blank=True,
        verbose_name="截图链接",
        help_text="受害者的截图链接",
    )

    infostealer = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="信息窃取者",
        help_text="信息窃取者的信息",
    )

    press = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="媒体报道",
        help_text="相关的媒体报道信息",
    )

    permalink = models.URLField(
        max_length=600,
        blank=True,
        verbose_name="信息来源",
        help_text="受害者信息的信息来源",
    )

    class Meta:
        verbose_name = "受害者"
        verbose_name_plural = "受害者"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.post_title} - {self.country}"
