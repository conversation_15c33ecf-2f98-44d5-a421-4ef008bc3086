"""
勒索组织相关的序列化器
"""

from rest_framework import serializers
from .models import RansomwareGroup, Tools, NegotiationRecord, RansomNote, IOCIndicator, Victim


class RansomwareGroupListSerializer(serializers.ModelSerializer):
    """勒索组织列表序列化器"""

    class Meta:
        model = RansomwareGroup
        fields = [
            "id",
            "name",
            "slug",
            "avg_delay_days",
            "info_stealer_percentage",
            "aliases",
            "status",
            "description",
            "threat_level",
            "first_seen",
            "last_activity",
            "victim_count",
            "created_at",
        ]


class ToolsSerializer(serializers.ModelSerializer):
    """应急工具序列化器"""

    ransomware_group = RansomwareGroupListSerializer(read_only=True)

    class Meta:
        model = Tools
        fields = [
            "id",
            "name",
            "description",
            "content",
            "file",
            "created_at",
            "updated_at",
            "ransomware_group",
            "view_count",
        ]


class NegotiationRecordListSerializer(serializers.ModelSerializer):
    """谈判记录列表序列化器"""

    group_name = serializers.CharField(source='group.name', read_only=True)
    ransom_reduction_percentage = serializers.SerializerMethodField()

    class Meta:
        model = NegotiationRecord
        fields = [
            'id',
            'group',
            'group_name',
            'paid',
            'initialransom',
            'negotiatedransom',
            'ransom_reduction_percentage',
            'message_count',
            'created_at',
        ]

    def get_ransom_reduction_percentage(self, obj):
        """获取赎金减少百分比"""
        return obj.get_ransom_reduction_percentage()


class NegotiationRecordDetailSerializer(serializers.ModelSerializer):
    """谈判记录详情序列化器"""

    group_name = serializers.CharField(source='group.name', read_only=True)
    ransom_reduction_percentage = serializers.SerializerMethodField()
    victim_messages_count = serializers.SerializerMethodField()
    attacker_messages_count = serializers.SerializerMethodField()

    class Meta:
        model = NegotiationRecord
        fields = '__all__'

    def get_ransom_reduction_percentage(self, obj):
        """获取赎金减少百分比"""
        return obj.get_ransom_reduction_percentage()

    def get_victim_messages_count(self, obj):
        """获取受害者消息数量"""
        return obj.get_victim_messages_count()

    def get_attacker_messages_count(self, obj):
        """获取攻击者消息数量"""
        return obj.get_attacker_messages_count()


class NegotiationRecordCreateUpdateSerializer(serializers.ModelSerializer):
    """谈判记录创建和更新序列化器"""

    class Meta:
        model = NegotiationRecord
        fields = [
            'group',
            'paid',
            'initialransom',
            'negotiatedransom',
            'message_count',
            'messages',
        ]

    def validate(self, data):
        """整体数据验证"""
        # 验证时间逻辑
        start_time = data.get('negotiation_start_time')
        end_time = data.get('negotiation_end_time')

        if start_time and end_time and start_time > end_time:
            raise serializers.ValidationError(
                {"negotiation_end_time": "谈判结束时间不能早于开始时间"}
            )

        # 验证消息数量与实际消息列表的一致性
        messages = data.get('messages', [])
        message_count = data.get('message_count', 0)

        if messages and message_count != len(messages):
            data['message_count'] = len(messages)

        return data


class RansomNoteListSerializer(serializers.ModelSerializer):
    """勒索信列表序列化器"""

    group_name = serializers.CharField(source='group.name', read_only=True)
    content_preview = serializers.SerializerMethodField()

    class Meta:
        model = RansomNote
        fields = [
            'id',
            'group',
            'group_name',
            'note_name',
            'extension',
            'content_preview',
            'created_at',
        ]

    def get_content_preview(self, obj):
        """获取内容预览"""
        return obj.get_content_preview()


class RansomNoteDetailSerializer(serializers.ModelSerializer):
    """勒索信详情序列化器"""

    group_name = serializers.CharField(source='group.name', read_only=True)
    content_preview = serializers.SerializerMethodField()

    class Meta:
        model = RansomNote
        fields = '__all__'

    def get_content_preview(self, obj):
        """获取内容预览"""
        return obj.get_content_preview()


class RansomNoteCreateUpdateSerializer(serializers.ModelSerializer):
    """勒索信创建和更新序列化器"""

    class Meta:
        model = RansomNote
        fields = [
            'group',
            'note_name',
            'extension',
            'content',
        ]

    def validate_note_name(self, value):
        """验证勒索信名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("勒索信名称不能为空")
        return value.strip()

    def validate_content(self, value):
        """验证勒索信内容"""
        if not value or not value.strip():
            raise serializers.ValidationError("勒索信内容不能为空")
        return value.strip()

    def validate_extension(self, value):
        """验证文件扩展名格式"""
        if value and not value.startswith('.'):
            value = '.' + value
        return value

    def validate(self, data):
        """整体数据验证"""
        # 基本数据验证，不再检查唯一性约束
        return data


class IOCIndicatorSerializer(serializers.ModelSerializer):
    """IOC指标序列化器"""

    class Meta:
        model = IOCIndicator
        fields = [
            'id',
            'ioc_types',
            'iocs',
            'created_at',
            'updated_at',
        ]


class VictimSerializer(serializers.ModelSerializer):
    """受害者序列化器"""

    # 为了向后兼容，将 attack_date 字段映射为 published
    published = serializers.DateTimeField(source='attack_date', read_only=True)

    class Meta:
        model = Victim
        fields = [
            'id',
            'post_title',
            'discovered',
            'description',
            'website',
            'attack_date',
            'published',  # 向后兼容字段
            'post_url',
            'country',
            'activity',
            'duplicates',
            'extrainfos',
            'screenshot',
            'infostealer',
            'press',
            'permalink',
            'created_at',
            'updated_at',
        ]


class RansomwareGroupBasicSerializer(serializers.ModelSerializer):
    """勒索组织基础信息序列化器 - 只包含基本信息，不包含关联数据"""

    class Meta:
        model = RansomwareGroup
        fields = [
            "id",
            "name",
            "slug",
            "logo",
            "avg_delay_days",
            "info_stealer_percentage",
            "aliases",
            "description",
            "external_information_source",
            "status",
            "first_seen",
            "last_activity",
            "created_at",
            "updated_at",
            "tools_used",
            "locations",
            "threat_level",
            "victim_count",
            "data_sources",
            "ttps",
            "vulnerabilities",
        ]


class RansomwareGroupDetailSerializer(serializers.ModelSerializer):
    """勒索组织详情序列化器 - 包含所有关联数据（保持向后兼容）"""

    tools = ToolsSerializer(many=True, read_only=True)
    negotiation_records = NegotiationRecordDetailSerializer(many=True, read_only=True)
    ransom_notes = RansomNoteListSerializer(many=True, read_only=True)
    ioc_indicators = IOCIndicatorSerializer(many=True, read_only=True)
    victims = VictimSerializer(many=True, read_only=True)

    class Meta:
        model = RansomwareGroup
        fields = "__all__"


class RansomwareGroupToolsSerializer(serializers.ModelSerializer):
    """勒索组织工具序列化器 - 只返回工具相关数据"""

    tools = ToolsSerializer(many=True, read_only=True)

    class Meta:
        model = RansomwareGroup
        fields = ["id", "name", "slug", "tools"]


class RansomwareGroupNegotiationsSerializer(serializers.ModelSerializer):
    """勒索组织谈判记录序列化器 - 只返回谈判记录相关数据"""

    negotiation_records = NegotiationRecordDetailSerializer(many=True, read_only=True)

    class Meta:
        model = RansomwareGroup
        fields = ["id", "name", "slug", "negotiation_records"]


class RansomwareGroupRansomNotesSerializer(serializers.ModelSerializer):
    """勒索组织勒索信序列化器 - 只返回勒索信相关数据"""

    ransom_notes = RansomNoteListSerializer(many=True, read_only=True)

    class Meta:
        model = RansomwareGroup
        fields = ["id", "name", "slug", "ransom_notes"]


class RansomwareGroupIOCIndicatorsSerializer(serializers.ModelSerializer):
    """勒索组织IOC指标序列化器 - 只返回IOC指标相关数据"""

    ioc_indicators = IOCIndicatorSerializer(many=True, read_only=True)

    class Meta:
        model = RansomwareGroup
        fields = ["id", "name", "slug", "ioc_indicators"]


class RansomwareGroupVictimsSerializer(serializers.ModelSerializer):
    """勒索组织受害者序列化器 - 只返回受害者相关数据"""

    victims = VictimSerializer(many=True, read_only=True)

    class Meta:
        model = RansomwareGroup
        fields = ["id", "name", "slug", "victims"]


class RansomwareGroupCreateUpdateSerializer(serializers.ModelSerializer):
    """勒索组织创建和更新序列化器"""

    class Meta:
        model = RansomwareGroup
        fields = [
            "name",
            "logo",
            "slug",
            "avg_delay_days",
            "info_stealer_percentage",
            "aliases",
            "description",
            "external_information_source",
            "status",
            "first_seen",
            "last_activity",
            "tools_used",
            "locations",
            "threat_level",
            "victim_count",
            "data_sources",
            "ttps",
            "vulnerabilities",
        ]

    def validate_name(self, value):
        """验证组织名称的唯一性"""
        # 在更新时排除当前实例
        queryset = RansomwareGroup.objects.filter(name=value)
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)

        if queryset.exists():
            raise serializers.ValidationError("该组织名称已存在")
        return value

    def validate(self, data):
        """整体数据验证"""
        # 验证赎金金额范围
        ransom_min = data.get("ransom_amount_min")
        ransom_max = data.get("ransom_amount_max")

        if ransom_min and ransom_max and ransom_min > ransom_max:
            raise serializers.ValidationError(
                {"ransom_amount_max": "最高赎金金额不能小于最低赎金金额"}
            )

        # 验证时间逻辑
        first_seen = data.get("first_seen")
        last_activity = data.get("last_activity")

        if first_seen and last_activity and first_seen > last_activity:
            raise serializers.ValidationError(
                {"last_activity": "最后活动时间不能早于首次发现时间"}
            )

        return data
