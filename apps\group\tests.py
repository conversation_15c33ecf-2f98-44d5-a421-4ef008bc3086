from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from .models import RansomwareGroup


class RansomwareGroupViewSetTestCase(APITestCase):
    """测试勒索组织ViewSet的slug查找功能"""

    def setUp(self):
        """设置测试数据"""
        self.group = RansomwareGroup.objects.create(
            name="Test Ransomware Group",
            slug="test-ransomware-group",
            description="Test description",
            status="active",
            threat_level="high"
        )

    def test_retrieve_by_slug(self):
        """测试通过slug获取勒索组织详情"""
        url = reverse('ransomware-group-detail', kwargs={'slug': self.group.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data.get('success'))
        self.assertEqual(response.data['data']['slug'], self.group.slug)
        self.assertEqual(response.data['data']['name'], self.group.name)

    def test_retrieve_by_slug_not_found(self):
        """测试通过不存在的slug获取勒索组织详情"""
        url = reverse('ransomware-group-detail', kwargs={'slug': 'non-existent-slug'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_tools_action_with_slug(self):
        """测试通过slug获取勒索组织相关工具"""
        url = reverse('ransomware-group-tools', kwargs={'slug': self.group.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data.get('success'))
        self.assertIn('data', response.data)

    def test_delete_not_allowed(self):
        """测试DELETE操作被禁用"""
        url = reverse('ransomware-group-detail', kwargs={'slug': self.group.slug})
        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        self.assertFalse(response.data.get('success'))
        self.assertEqual(response.data.get('error_code'), 'METHOD_NOT_ALLOWED')

        # 验证记录仍然存在
        self.assertTrue(RansomwareGroup.objects.filter(slug=self.group.slug).exists())
