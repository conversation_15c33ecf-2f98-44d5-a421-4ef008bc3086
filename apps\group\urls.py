"""
勒索组织应用的URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import RansomwareGroupViewSet, ToolsViewSet, NegotiationRecordViewSet, RansomNoteViewSet, VictimViewSet

# 创建DRF路由器
router = DefaultRouter()
router.register(r'groups', RansomwareGroupViewSet, basename='ransomware-group')
router.register(r'tools', ToolsViewSet, basename='tools')
router.register(r'negotiation-records', NegotiationRecordViewSet, basename='negotiation-record')
router.register(r'ransom-notes', RansomNoteViewSet, basename='ransom-note')
router.register(r'victims', VictimViewSet, basename='victim')

# URL模式
urlpatterns = [
    # DRF路由
    path('', include(router.urls)),
]

# 可用的API端点：
# 勒索组织管理（增改查，不支持删除）：
# GET /api/v1/groups/ - 勒索组织列表
# GET /api/v1/groups/{slug}/ - 勒索组织详情
# POST /api/v1/groups/ - 创建勒索组织
# PUT /api/v1/groups/{slug}/ - 更新勒索组织
# PATCH /api/v1/groups/{slug}/ - 部分更新勒索组织
# GET /api/v1/groups/{slug}/tools/ - 获取勒索组织相关工具
#
# 应急工具管理（只读）：
# GET /api/v1/tools/ - 应急工具列表
# GET /api/v1/tools/{id}/ - 应急工具详情
# GET /api/v1/tools/stats/ - 应急工具统计数据
#
# 谈判记录管理（完整CRUD）：
# GET /api/v1/negotiation-records/ - 谈判记录列表
# GET /api/v1/negotiation-records/{id}/ - 谈判记录详情
# POST /api/v1/negotiation-records/ - 创建谈判记录
# PUT /api/v1/negotiation-records/{id}/ - 更新谈判记录
# PATCH /api/v1/negotiation-records/{id}/ - 部分更新谈判记录
# DELETE /api/v1/negotiation-records/{id}/ - 删除谈判记录
# GET /api/v1/negotiation-records/stats/ - 谈判记录统计数据
#
# 勒索信管理（完整CRUD）：
# GET /api/v1/ransom-notes/ - 勒索信列表
# GET /api/v1/ransom-notes/{id}/ - 勒索信详情
# POST /api/v1/ransom-notes/ - 创建勒索信
# PUT /api/v1/ransom-notes/{id}/ - 更新勒索信
# PATCH /api/v1/ransom-notes/{id}/ - 部分更新勒索信
# DELETE /api/v1/ransom-notes/{id}/ - 删除勒索信
# GET /api/v1/ransom-notes/stats/ - 勒索信统计数据
