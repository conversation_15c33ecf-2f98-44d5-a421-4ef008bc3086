"""
勒索组织相关的视图函数
"""

from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiResponse, OpenApiParameter
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework import filters
from rest_framework.permissions import IsAuthenticated, AllowAny
from .filters import Group<PERSON>ilter
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from utils.viewsets import StandardModelViewSet, StandardReadOnlyModelViewSet
from utils.response import StandardResponseSerializer
from utils.permissions import get_permissions_for_action
from .models import Ransom<PERSON>Group, Tools, NegotiationRecord, RansomNote, Victim
from .serializers import (
    RansomwareGroupListSerializer,
    RansomwareGroupBasicSerializer,
    RansomwareGroupDetailSerializer,
    RansomwareGroupCreateUpdateSerializer,
    RansomwareGroupToolsSerializer,
    RansomwareGroupNegotiationsSerializer,
    RansomwareGroupRansomNotesSerializer,
    RansomwareGroupIOCIndicatorsSerializer,
    RansomwareGroupVictimsSerializer,
    ToolsSerializer,
    NegotiationRecordListSerializer,
    NegotiationRecordDetailSerializer,
    NegotiationRecordCreateUpdateSerializer,
    RansomNoteListSerializer,
    RansomNoteDetailSerializer,
    RansomNoteCreateUpdateSerializer,
    VictimSerializer
)


@extend_schema_view(
    list=extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织列表",
        description="获取所有勒索组织列表，支持分页查询、搜索和筛选",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取勒索组织列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织详情",
        description="根据slug获取特定勒索组织的详细信息，包括组织基本信息、技术特征、运营信息等",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取勒索组织详情"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="勒索组织未找到"
            )
        }
    ),
    create=extend_schema(
        tags=['勒索组织管理'],
        summary="创建勒索组织",
        description="创建新的勒索组织记录",
        responses={
            201: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功创建勒索组织"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            )
        }
    ),
    update=extend_schema(
        tags=['勒索组织管理'],
        summary="更新勒索组织",
        description="更新指定的勒索组织信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新勒索组织"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="勒索组织未找到"
            )
        }
    ),
    partial_update=extend_schema(
        tags=['勒索组织管理'],
        summary="部分更新勒索组织",
        description="部分更新指定的勒索组织信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新勒索组织"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="勒索组织未找到"
            )
        }
    )
)
class RansomwareGroupViewSet(StandardModelViewSet):
    """
    勒索组织视图集 - 提供增改查功能（不支持删除）

    支持的操作：
    - GET /groups/ - 获取勒索组织列表
    - GET /groups/{slug}/ - 获取勒索组织详情
    - POST /groups/ - 创建勒索组织
    - PUT /groups/{slug}/ - 更新勒索组织
    - PATCH /groups/{slug}/ - 部分更新勒索组织
    - GET /groups/{slug}/tools/ - 获取勒索组织相关工具
    """

    queryset = RansomwareGroup.objects.all()
    lookup_field = 'slug'  # 使用slug作为查找字段
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'threat_level']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'updated_at', 'first_seen', 'last_activity', 'victim_count']
    ordering = ['-last_activity', '-created_at']
    filterset_class = GroupFilter

    def get_permissions(self):
        """
        根据动作设置权限
        - retrieve 操作需要用户登录
        - 其他操作允许匿名访问
        """
        return get_permissions_for_action(self.action)

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return RansomwareGroupListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return RansomwareGroupCreateUpdateSerializer
        elif self.action == 'basic':
            return RansomwareGroupBasicSerializer
        elif self.action == 'tools':
            return RansomwareGroupToolsSerializer
        elif self.action == 'negotiations':
            return RansomwareGroupNegotiationsSerializer
        elif self.action == 'ransom_notes':
            return RansomwareGroupRansomNotesSerializer
        elif self.action == 'ioc_indicators':
            return RansomwareGroupIOCIndicatorsSerializer
        elif self.action == 'victims':
            return RansomwareGroupVictimsSerializer
        return RansomwareGroupDetailSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        keyword = self.request.query_params.get('keyword')

        if keyword:
            queryset = queryset.filter(
                Q(name__icontains=keyword) |
                Q(description__icontains=keyword) |
                Q(aliases__icontains=keyword) |
                Q(ransomware_families__icontains=keyword) |
                Q(tools_used__icontains=keyword) |
                Q(target_regions__icontains=keyword) |
                Q(target_industries__icontains=keyword) |
                Q(contact_methods__icontains=keyword) |
                Q(payment_methods__icontains=keyword)
            )

        return queryset

    def retrieve(self, request, *args, **kwargs):
        """获取勒索组织详情并添加前后记录信息"""
        # 调用父类的retrieve方法获取标准响应
        response = super().retrieve(request, *args, **kwargs)

        # 如果响应成功，添加前后记录信息
        if response.status_code == 200 and hasattr(response, 'data') and isinstance(response.data, dict):
            if response.data.get('success') and 'data' in response.data:
                instance = self.get_object()
                current_id = instance.id

                # 获取前后记录（仍然使用id进行排序，但返回slug）
                prev_instance = RansomwareGroup.objects.filter(id__lt=current_id).order_by('-id').first()
                next_instance = RansomwareGroup.objects.filter(id__gt=current_id).order_by('id').first()

                # 添加前后记录信息到响应数据（使用slug作为标识）
                response.data['data']['prev_record'] = {
                    "slug": prev_instance.slug,
                    "name": prev_instance.name,
                    "status": prev_instance.status,
                    "threat_level": prev_instance.threat_level
                } if prev_instance else None

                response.data['data']['next_record'] = {
                    "slug": next_instance.slug,
                    "name": next_instance.name,
                    "status": next_instance.status,
                    "threat_level": next_instance.threat_level
                } if next_instance else None

        return response

    @extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织基础信息",
        description="获取勒索组织的基础信息，不包含关联数据",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取基础信息"
            )
        }
    )
    @action(detail=True, methods=['get'])
    def basic(self, request, slug=None):
        """获取勒索组织基础信息"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        # 添加前后记录信息
        current_id = instance.id
        prev_instance = RansomwareGroup.objects.filter(id__lt=current_id).order_by('-id').first()
        next_instance = RansomwareGroup.objects.filter(id__gt=current_id).order_by('id').first()

        data = serializer.data
        data['prev_id'] = {
            "slug": prev_instance.slug,
            "name": prev_instance.name,
            "status": prev_instance.status,
            "threat_level": prev_instance.threat_level
        } if prev_instance else None

        data['next_id'] = {
            "slug": next_instance.slug,
            "name": next_instance.name,
            "status": next_instance.status,
            "threat_level": next_instance.threat_level
        } if next_instance else None

        return Response({
            "success": True,
            "message": "获取基础信息成功",
            "data": data
        })

    @extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织相关工具",
        description="获取特定勒索组织的相关应急工具，支持分页",
        parameters=[
            OpenApiParameter(
                name='page',
                type=int,
                location=OpenApiParameter.QUERY,
                description='页码，默认为1',
                default=1
            ),
            OpenApiParameter(
                name='page_size',
                type=int,
                location=OpenApiParameter.QUERY,
                description='每页数量，默认为12，最大30',
                default=12
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取相关工具列表"
            )
        }
    )
    @action(detail=True, methods=['get'])
    def tools(self, request, slug=None):
        """获取勒索组织相关的工具（支持分页）"""
        instance = self.get_object()

        # 获取分页参数
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 12)), 30)  # 限制最大页面大小

        # 获取所有工具
        tools_queryset = instance.tools.all().order_by('-created_at')

        # 计算分页
        total_count = tools_queryset.count()
        total_pages = (total_count + page_size - 1) // page_size

        # 计算偏移量
        offset = (page - 1) * page_size
        tools_page = tools_queryset[offset:offset + page_size]

        # 序列化数据
        from .serializers import ToolsSerializer
        tools_data = ToolsSerializer(tools_page, many=True).data

        return Response({
            "success": True,
            "message": "获取相关工具成功",
            "data": {
                "tools": tools_data,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            }
        })

    @extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织谈判记录",
        description="获取特定勒索组织的谈判记录，支持分页",
        parameters=[
            OpenApiParameter(
                name='page',
                type=int,
                location=OpenApiParameter.QUERY,
                description='页码，默认为1',
                default=1
            ),
            OpenApiParameter(
                name='page_size',
                type=int,
                location=OpenApiParameter.QUERY,
                description='每页数量，默认为10，最大50',
                default=10
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取谈判记录列表"
            )
        }
    )
    @action(detail=True, methods=['get'])
    def negotiations(self, request, slug=None):
        """获取勒索组织谈判记录（支持分页）"""
        instance = self.get_object()

        # 获取分页参数
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 10)), 50)  # 限制最大页面大小

        # 获取所有谈判记录
        negotiations_queryset = instance.negotiation_records.all().order_by('-created_at')

        # 计算分页
        total_count = negotiations_queryset.count()
        total_pages = (total_count + page_size - 1) // page_size

        # 计算偏移量
        offset = (page - 1) * page_size
        negotiations_page = negotiations_queryset[offset:offset + page_size]

        # 序列化数据
        from .serializers import NegotiationRecordDetailSerializer
        negotiations_data = NegotiationRecordDetailSerializer(negotiations_page, many=True).data

        return Response({
            "success": True,
            "message": "获取谈判记录成功",
            "data": {
                "negotiation_records": negotiations_data,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            }
        })

    @extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织勒索信",
        description="获取特定勒索组织的勒索信，支持分页",
        parameters=[
            OpenApiParameter(
                name='page',
                type=int,
                location=OpenApiParameter.QUERY,
                description='页码，默认为1',
                default=1
            ),
            OpenApiParameter(
                name='page_size',
                type=int,
                location=OpenApiParameter.QUERY,
                description='每页数量，默认为8，最大20',
                default=8
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取勒索信列表"
            )
        }
    )
    @action(detail=True, methods=['get'], url_path='ransom-notes')
    def ransom_notes(self, request, slug=None):
        """获取勒索组织勒索信（支持分页）"""
        instance = self.get_object()

        # 获取分页参数
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 8)), 20)  # 限制最大页面大小

        # 获取所有勒索信
        ransom_notes_queryset = instance.ransom_notes.all().order_by('-created_at')

        # 计算分页
        total_count = ransom_notes_queryset.count()
        total_pages = (total_count + page_size - 1) // page_size

        # 计算偏移量
        offset = (page - 1) * page_size
        ransom_notes_page = ransom_notes_queryset[offset:offset + page_size]

        # 序列化数据
        from .serializers import RansomNoteListSerializer
        ransom_notes_data = RansomNoteListSerializer(ransom_notes_page, many=True).data

        return Response({
            "success": True,
            "message": "获取勒索信成功",
            "data": {
                "ransom_notes": ransom_notes_data,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            }
        })

    @extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织IOC指标",
        description="获取特定勒索组织的所有IOC指标",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取IOC指标列表"
            )
        }
    )
    @action(detail=True, methods=['get'], url_path='ioc-indicators')
    def ioc_indicators(self, request, slug=None):
        """获取勒索组织IOC指标"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return Response({
            "success": True,
            "message": "获取IOC指标成功",
            "data": serializer.data
        })

    @extend_schema(
        tags=['勒索组织管理'],
        summary="获取勒索组织受害者",
        description="获取特定勒索组织的受害者信息，支持分页",
        parameters=[
            OpenApiParameter(
                name='page',
                type=int,
                location=OpenApiParameter.QUERY,
                description='页码，默认为1',
                default=1
            ),
            OpenApiParameter(
                name='page_size',
                type=int,
                location=OpenApiParameter.QUERY,
                description='每页数量，默认为12，最大100',
                default=12
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取受害者列表"
            )
        }
    )
    @action(detail=True, methods=['get'])
    def victims(self, request, slug=None):
        """获取勒索组织受害者（支持分页）"""
        instance = self.get_object()

        # 获取分页参数
        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 12)), 100)  # 限制最大页面大小

        # 获取所有受害者
        victims_queryset = instance.victims.all().order_by('-discovered')

        # 计算分页
        total_count = victims_queryset.count()
        total_pages = (total_count + page_size - 1) // page_size

        # 计算偏移量
        offset = (page - 1) * page_size
        victims_page = victims_queryset[offset:offset + page_size]

        # 序列化数据
        from .serializers import VictimSerializer
        victims_data = VictimSerializer(victims_page, many=True).data

        return Response({
            "success": True,
            "message": "获取受害者成功",
            "data": {
                "victims": victims_data,
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            }
        })

    @extend_schema(exclude=True)
    def destroy(self, request, *args, **kwargs):
        """禁用删除功能"""
        from rest_framework import status
        return Response({
            "success": False,
            "message": "不支持删除勒索组织记录",
            "error_code": "METHOD_NOT_ALLOWED"
        }, status=status.HTTP_405_METHOD_NOT_ALLOWED)


@extend_schema_view(
    list=extend_schema(
        tags=['应急工具管理'],
        summary="获取应急工具列表",
        description="获取所有应急工具列表，支持分页查询、搜索和筛选",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取应急工具列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['应急工具管理'],
        summary="获取应急工具详情",
        description="根据ID获取特定应急工具的详细信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取应急工具详情"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="应急工具未找到"
            )
        }
    )
)
class ToolsViewSet(StandardReadOnlyModelViewSet):
    """
    应急工具视图集 - 只提供读取功能（列表和详情）
    """

    queryset = Tools.objects.all()
    serializer_class = ToolsSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['ransomware_group']
    search_fields = ['name', 'description', 'content']
    ordering_fields = ['created_at', 'updated_at', 'name']
    ordering = ['-updated_at']

    def get_permissions(self):
        """
        根据动作设置权限
        - retrieve 操作需要用户登录
        - 其他操作允许匿名访问
        """
        return get_permissions_for_action(self.action)

    def get_queryset(self):
        """自定义查询集，支持额外的筛选"""
        queryset = super().get_queryset()

        # 筛选是否有文件
        has_file = self.request.query_params.get('has_file')
        if has_file is not None:
            if has_file.lower() == 'true':
                queryset = queryset.exclude(file='')
            elif has_file.lower() == 'false':
                queryset = queryset.filter(file='')

        return queryset

    def retrieve(self, request, *args, **kwargs):
        """获取工具详情并增加浏览量"""
        instance = self.get_object()
        # 增加浏览量
        instance.increment_view_count()
        serializer = self.get_serializer(instance)
        return Response({
            "success": True,
            "message": "获取工具详情成功",
            "data": serializer.data
        })

    @extend_schema(
        tags=['应急工具管理'],
        summary="获取应急工具统计数据",
        description="获取应急工具的统计信息，包括总数、有文件的工具数等",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取统计数据"
            )
        }
    )
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取工具统计数据"""
        from datetime import datetime, timedelta

        # 基础统计
        total = Tools.objects.count()
        with_files = Tools.objects.exclude(file='').count()
        with_docs = Tools.objects.exclude(content='').count()

        # 最近30天更新的工具数
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recently_updated = Tools.objects.filter(updated_at__gte=thirty_days_ago).count()

        stats_data = {
            'total': total,
            'with_files': with_files,
            'with_docs': with_docs,
            'recently_updated': recently_updated
        }

        return Response({
            "success": True,
            "message": "获取统计数据成功",
            "data": stats_data
        })


@extend_schema_view(
    list=extend_schema(
        tags=['谈判记录管理'],
        summary="获取谈判记录列表",
        description="获取所有谈判记录列表，支持分页查询、搜索和筛选",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取谈判记录列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['谈判记录管理'],
        summary="获取谈判记录详情",
        description="根据ID获取特定谈判记录的详细信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取谈判记录详情"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="谈判记录未找到"
            )
        }
    ),
    create=extend_schema(
        tags=['谈判记录管理'],
        summary="创建谈判记录",
        description="创建新的谈判记录",
        responses={
            201: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功创建谈判记录"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            )
        }
    ),
    update=extend_schema(
        tags=['谈判记录管理'],
        summary="更新谈判记录",
        description="更新指定的谈判记录信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新谈判记录"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="谈判记录未找到"
            )
        }
    ),
    partial_update=extend_schema(
        tags=['谈判记录管理'],
        summary="部分更新谈判记录",
        description="部分更新指定的谈判记录信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新谈判记录"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="谈判记录未找到"
            )
        }
    )
)
class NegotiationRecordViewSet(StandardModelViewSet):
    """
    谈判记录视图集 - 提供完整的CRUD功能

    支持的操作：
    - GET /negotiation-records/ - 获取谈判记录列表
    - GET /negotiation-records/{id}/ - 获取谈判记录详情
    - POST /negotiation-records/ - 创建谈判记录
    - PUT /negotiation-records/{id}/ - 更新谈判记录
    - PATCH /negotiation-records/{id}/ - 部分更新谈判记录
    - DELETE /negotiation-records/{id}/ - 删除谈判记录
    - GET /negotiation-records/stats/ - 获取谈判记录统计数据
    """

    queryset = NegotiationRecord.objects.all().select_related('group')
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['group', 'paid']
    search_fields = ['group__name', 'initialransom', 'negotiatedransom']
    ordering_fields = ['created_at', 'updated_at', 'message_count']
    ordering = ['-created_at']

    def get_permissions(self):
        """
        根据动作设置权限
        - retrieve 操作需要用户登录
        - 其他操作允许匿名访问
        """
        return get_permissions_for_action(self.action)

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return NegotiationRecordListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return NegotiationRecordCreateUpdateSerializer
        return NegotiationRecordDetailSerializer

    @extend_schema(
        tags=['谈判记录管理'],
        summary="获取谈判记录统计数据",
        description="获取谈判记录的统计信息，包括总数、支付情况、状态分布等",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取统计数据"
            )
        }
    )
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取谈判记录统计数据"""
        from django.db.models import Count, Avg
        from datetime import datetime, timedelta

        # 基础统计
        total = NegotiationRecord.objects.count()
        paid_count = NegotiationRecord.objects.filter(paid=True).count()
        unpaid_count = total - paid_count

        # 状态分布
        status_stats = NegotiationRecord.objects.values('status').annotate(
            count=Count('id')
        ).order_by('status')

        # 支付状态分布
        payment_status_stats = NegotiationRecord.objects.values('payment_status').annotate(
            count=Count('id')
        ).order_by('payment_status')

        # 最近30天的记录数
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_count = NegotiationRecord.objects.filter(
            created_at__gte=thirty_days_ago
        ).count()

        # 平均消息数量
        avg_messages = NegotiationRecord.objects.aggregate(
            avg_messages=Avg('message_count')
        )['avg_messages'] or 0

        stats_data = {
            'total': total,
            'paid_count': paid_count,
            'unpaid_count': unpaid_count,
            'payment_rate': round((paid_count / total * 100) if total > 0 else 0, 2),
            'status_distribution': list(status_stats),
            'payment_status_distribution': list(payment_status_stats),
            'recent_count': recent_count,
            'avg_messages': round(avg_messages, 2)
        }

        return Response({
            "success": True,
            "message": "获取统计数据成功",
            "data": stats_data
        })


@extend_schema_view(
    list=extend_schema(
        tags=['勒索信管理'],
        summary="获取勒索信列表",
        description="获取所有勒索信列表，支持分页查询、搜索和筛选",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取勒索信列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['勒索信管理'],
        summary="获取勒索信详情",
        description="根据ID获取特定勒索信的详细信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取勒索信详情"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="勒索信未找到"
            )
        }
    ),
    create=extend_schema(
        tags=['勒索信管理'],
        summary="创建勒索信",
        description="创建新的勒索信记录",
        responses={
            201: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功创建勒索信"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            )
        }
    ),
    update=extend_schema(
        tags=['勒索信管理'],
        summary="更新勒索信",
        description="更新指定的勒索信信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新勒索信"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="勒索信未找到"
            )
        }
    ),
    partial_update=extend_schema(
        tags=['勒索信管理'],
        summary="部分更新勒索信",
        description="部分更新指定的勒索信信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新勒索信"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="勒索信未找到"
            )
        }
    )
)
class RansomNoteViewSet(StandardModelViewSet):
    """
    勒索信视图集 - 提供完整的CRUD功能

    支持的操作：
    - GET /ransom-notes/ - 获取勒索信列表
    - GET /ransom-notes/{id}/ - 获取勒索信详情
    - POST /ransom-notes/ - 创建勒索信
    - PUT /ransom-notes/{id}/ - 更新勒索信
    - PATCH /ransom-notes/{id}/ - 部分更新勒索信
    - DELETE /ransom-notes/{id}/ - 删除勒索信
    - GET /ransom-notes/stats/ - 获取勒索信统计数据
    """

    queryset = RansomNote.objects.all().select_related('group')
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['group', 'extension']
    search_fields = ['note_name', 'content', 'group__name']
    ordering_fields = ['created_at', 'updated_at', 'note_name']
    ordering = ['-created_at']

    def get_permissions(self):
        """
        根据动作设置权限
        - retrieve 操作需要用户登录
        - 其他操作允许匿名访问
        """
        return get_permissions_for_action(self.action)

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return RansomNoteListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return RansomNoteCreateUpdateSerializer
        return RansomNoteDetailSerializer

    def get_queryset(self):
        """自定义查询集，支持额外的筛选"""
        queryset = super().get_queryset()

        # 按勒索组织筛选
        group_slug = self.request.query_params.get('group_slug')
        if group_slug:
            queryset = queryset.filter(group__slug=group_slug)

        # 按扩展名筛选
        extension = self.request.query_params.get('extension')
        if extension:
            queryset = queryset.filter(extension=extension)

        return queryset

    @extend_schema(
        tags=['勒索信管理'],
        summary="获取勒索信统计数据",
        description="获取勒索信的统计信息，包括总数、语言分布、特征分布等",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取统计数据"
            )
        }
    )
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取勒索信统计数据"""
        from django.db.models import Count
        from datetime import datetime, timedelta

        # 基础统计
        total = RansomNote.objects.count()

        # 扩展名分布
        extension_stats = RansomNote.objects.values('extension').annotate(
            count=Count('id')
        ).order_by('-count')

        # 最近30天新增的勒索信数
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_count = RansomNote.objects.filter(
            created_at__gte=thirty_days_ago
        ).count()

        # 按勒索组织分布（前10）
        group_stats = RansomNote.objects.values(
            'group__name'
        ).annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        stats_data = {
            'total': total,
            'extension_distribution': list(extension_stats),
            'group_distribution': list(group_stats),
            'recent_count': recent_count
        }

        return Response({
            "success": True,
            "message": "获取统计数据成功",
            "data": stats_data
        })


@extend_schema_view(
    list=extend_schema(
        tags=['受害者管理'],
        summary="获取受害者列表",
        description="获取所有受害者列表，支持分页查询、搜索和筛选",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取受害者列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['受害者管理'],
        summary="获取受害者详情",
        description="根据ID获取特定受害者的详细信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取受害者详情"
            ),
            404: OpenApiResponse(description="受害者不存在")
        }
    )
)
class VictimViewSet(StandardReadOnlyModelViewSet):
    """
    受害者视图集 - 提供只读功能

    支持的操作：
    - GET /victims/ - 获取受害者列表
    - GET /victims/{id}/ - 获取受害者详情
    """

    queryset = Victim.objects.all().select_related('group')
    serializer_class = VictimSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['group', 'country', 'activity']
    search_fields = ['post_title', 'description', 'website', 'group__name']
    ordering_fields = ['discovered', 'attack_date', 'created_at']
    ordering = ['-discovered', '-created_at']

    def get_permissions(self):
        """
        根据动作设置权限
        - retrieve 操作需要用户登录
        - 其他操作允许匿名访问
        """
        return get_permissions_for_action(self.action)

    def retrieve(self, request, *args, **kwargs):
        """获取受害者详情"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        # 添加勒索组织信息
        data = serializer.data
        if instance.group:
            data['group_name'] = instance.group.name
            data['group_slug'] = instance.group.slug

        return Response({
            "success": True,
            "message": "获取受害者详情成功",
            "data": data
        })
