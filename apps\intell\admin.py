from django.contrib import admin
from .models import IntelCategory, IntelTag, IntelPost, IntelPostTag


@admin.register(IntelCategory)
class IntelCategoryAdmin(admin.ModelAdmin):
    list_display = ["name", "description", "created_at", "updated_at"]
    readonly_fields = ["created_at", "updated_at"]
    ordering = ["name"]


@admin.register(IntelTag)
class IntelTagAdmin(admin.ModelAdmin):
    list_display = ["name", "description", "created_at", "updated_at"]
    readonly_fields = ["created_at", "updated_at"]
    ordering = ["name"]


@admin.register(IntelPost)
class IntelPostAdmin(admin.ModelAdmin):
    list_display = [
        "title",
        "keywords",
        "category",
        "source",
        "created_at",
        "updated_at",
    ]
    list_filter = ["category", "source", "created_at"]
    search_fields = ["title", "keywords", "description"]
    readonly_fields = ["created_at", "updated_at"]
    ordering = ["-created_at"]
