# Generated by Django 5.2.3 on 2025-07-15 09:42

import django.db.models.deletion
import mdeditor.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='IntelCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='分类名称')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
            ],
            options={
                'verbose_name': '情报分类',
                'verbose_name_plural': '情报分类',
            },
        ),
        migrations.CreateModel(
            name='IntelTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='标签名称')),
                ('description', models.TextField(blank=True, verbose_name='标签描述')),
            ],
            options={
                'verbose_name': '情报标签',
                'verbose_name_plural': '情报标签',
            },
        ),
        migrations.CreateModel(
            name='IntelPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('title', models.CharField(max_length=200, verbose_name='文章标题')),
                ('description', models.TextField(blank=True, verbose_name='威胁描述')),
                ('content', mdeditor.fields.MDTextField(verbose_name='文章内容')),
                ('confidence', models.IntegerField(default=0, verbose_name='置信度')),
                ('threat_type', models.CharField(choices=[('恶意软件', '恶意软件'), ('网络钓鱼', '网络钓鱼'), ('DDoS攻击', 'DDoS攻击'), ('数据泄露', '数据泄露'), ('勒索软件', '勒索软件'), ('APT攻击', 'APT攻击')], default='恶意软件', max_length=20, verbose_name='威胁类型')),
                ('severity', models.CharField(choices=[('严重', '严重'), ('高', '高'), ('中', '中'), ('低', '低')], default='中', max_length=20, verbose_name='严重程度')),
                ('source', models.CharField(choices=[('内部监测', '内部监测'), ('第三方情报', '第三方情报'), ('开源情报', '开源情报'), ('合作伙伴', '合作伙伴')], default='内部监测', max_length=20, verbose_name='情报来源')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='intell.intelcategory', verbose_name='文章分类')),
            ],
            options={
                'verbose_name': '情报文章',
                'verbose_name_plural': '情报文章',
            },
        ),
        migrations.CreateModel(
            name='IntelPostTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='intell.intelpost', verbose_name='情报文章')),
                ('tag', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='intell.inteltag', verbose_name='情报标签')),
            ],
            options={
                'verbose_name': '情报标签',
                'verbose_name_plural': '情报标签',
            },
        ),
    ]
