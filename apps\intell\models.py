from django.db import models
from config.models import BaseModel
from mdeditor.fields import MDTextField


class IntelCategory(BaseModel):
    name = models.CharField(max_length=100, unique=True, verbose_name="分类名称")
    description = models.TextField(blank=True, verbose_name="分类描述")

    class Meta:
        verbose_name = "情报分类"
        verbose_name_plural = "情报分类"

    def __str__(self):
        return self.name


# 情报标签
class IntelTag(BaseModel):
    name = models.CharField(max_length=50, unique=True, verbose_name="标签名称")
    description = models.TextField(blank=True, verbose_name="标签描述")

    class Meta:
        verbose_name = "情报标签"
        verbose_name_plural = "情报标签"

    def __str__(self):
        return self.name


# 情报文章
class IntelPost(BaseModel):
    # 情报来源
    class Source(models.TextChoices):
        INTERNAL = "内部监测", "内部监测"
        THIRD_PARTY = "第三方情报", "第三方情报"
        OPEN_SOURCE = "开源情报", "开源情报"
        PARTNER = "合作伙伴", "合作伙伴"

    title = models.CharField(max_length=200, verbose_name="文章标题")
    keywords = models.CharField(max_length=200, blank=True, verbose_name="文章关键词")
    description = models.TextField(blank=True, verbose_name="威胁描述")
    content = MDTextField(verbose_name="文章内容")
    category = models.ForeignKey(
        IntelCategory, on_delete=models.CASCADE, verbose_name="文章分类"
    )

    # 情报来源
    source = models.CharField(
        max_length=20,
        choices=Source.choices,
        default=Source.INTERNAL,
        verbose_name="情报来源",
    )

    # 标签关系
    tags = models.ManyToManyField(
        IntelTag,
        through='IntelPostTag',
        blank=True,
        related_name='posts',
        verbose_name="情报标签",
        help_text="情报文章的标签"
    )

    class Meta:
        verbose_name = "情报文章"
        verbose_name_plural = "情报文章"

    def __str__(self):
        return self.title


# 情报标签与文章的多对多关系
class IntelPostTag(BaseModel):
    post = models.ForeignKey(
        IntelPost, on_delete=models.CASCADE, verbose_name="情报文章"
    )
    tag = models.ForeignKey(IntelTag, on_delete=models.CASCADE, verbose_name="情报标签")

    class Meta:
        verbose_name = "情报标签"
        verbose_name_plural = "情报标签"

    def __str__(self):
        return f"{self.post.title} - {self.tag.name}"
