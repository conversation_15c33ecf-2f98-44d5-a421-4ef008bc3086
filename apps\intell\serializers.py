"""
威胁情报相关的序列化器
"""
from rest_framework import serializers
from .models import IntelPost, IntelCategory, IntelTag, IntelPostTag


class IntelCategorySerializer(serializers.ModelSerializer):
    """情报分类序列化器"""
    
    class Meta:
        model = IntelCategory
        fields = ['id', 'name', 'description']


class IntelTagSerializer(serializers.ModelSerializer):
    """情报标签序列化器"""
    
    class Meta:
        model = IntelTag
        fields = ['id', 'name', 'description']


class IntelPostListSerializer(serializers.ModelSerializer):
    """威胁情报列表序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    tags = serializers.SerializerMethodField()

    class Meta:
        model = IntelPost
        fields = [
            'id',
            'title',
            'keywords',
            'description',
            'source',
            'category',
            'category_name',
            'tags',
            'created_at',
            'updated_at',
        ]
    
    def get_tags(self, obj):
        """获取标签列表"""
        return [tag.name for tag in obj.tags.all()]


class IntelPostCreateUpdateSerializer(serializers.ModelSerializer):
    """威胁情报创建和更新序列化器"""
    tags = serializers.PrimaryKeyRelatedField(
        queryset=IntelTag.objects.all(),
        many=True,
        required=False,
        help_text="标签ID列表"
    )

    class Meta:
        model = IntelPost
        fields = [
            'title',
            'keywords',
            'description',
            'content',
            'source',
            'category',
            'tags',
        ]

    def create(self, validated_data):
        """创建威胁情报"""
        tags_data = validated_data.pop('tags', [])
        intel_post = IntelPost.objects.create(**validated_data)

        # 设置标签关系
        if tags_data:
            intel_post.tags.set(tags_data)

        return intel_post

    def update(self, instance, validated_data):
        """更新威胁情报"""
        tags_data = validated_data.pop('tags', None)

        # 更新基本字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 更新标签关系
        if tags_data is not None:
            instance.tags.set(tags_data)

        return instance


class IntelPostDetailSerializer(serializers.ModelSerializer):
    """威胁情报详情序列化器"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_info = IntelCategorySerializer(source='category', read_only=True)
    tags = IntelTagSerializer(many=True, read_only=True)

    class Meta:
        model = IntelPost
        fields = [
            'id',
            'title',
            'keywords',
            'description',
            'content',
            'source',
            'category',
            'category_name',
            'category_info',
            'tags',
            'created_at',
            'updated_at',
        ]
