"""
威胁情报应用的URL配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import IntelPostViewSet, IntelCategoryViewSet, IntelTagViewSet

# 创建DRF路由器
router = DefaultRouter()
router.register(r'posts', IntelPostViewSet, basename='intel-post')
router.register(r'categories', IntelCategoryViewSet, basename='intel-category')
router.register(r'tags', IntelTagViewSet, basename='intel-tag')

# URL模式
urlpatterns = [
    # DRF路由
    path('', include(router.urls)),
]

# 可用的API端点：
# GET /api/v1/intell/posts/ - 威胁情报列表
# GET /api/v1/intell/posts/{id}/ - 威胁情报详情
# GET /api/v1/intell/posts/stats/ - 威胁情报统计数据
# GET /api/v1/intell/categories/ - 情报分类列表
# GET /api/v1/intell/tags/ - 情报标签列表