"""
威胁情报相关的视图函数
"""

from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiResponse
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework import filters
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from utils.viewsets import StandardModelViewSet, StandardReadOnlyModelViewSet
from utils.response import StandardResponseSerializer
from utils.permissions import get_permissions_for_action
from .models import IntelPost, IntelCategory, IntelTag
from .serializers import (
    IntelPostListSerializer,
    IntelPostDetailSerializer,
    IntelPostCreateUpdateSerializer,
    IntelCategorySerializer,
    IntelTagSerializer
)


@extend_schema_view(
    list=extend_schema(
        tags=['威胁情报管理'],
        summary="获取威胁情报列表",
        description="获取所有威胁情报列表，支持分页查询、搜索和筛选",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取威胁情报列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['威胁情报管理'],
        summary="获取威胁情报详情",
        description="根据ID获取特定威胁情报的详细信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取威胁情报详情"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="威胁情报未找到"
            )
        }
    ),
    create=extend_schema(
        tags=['威胁情报管理'],
        summary="创建威胁情报",
        description="创建新的威胁情报",
        responses={
            201: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功创建威胁情报"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            )
        }
    ),
    update=extend_schema(
        tags=['威胁情报管理'],
        summary="更新威胁情报",
        description="更新指定的威胁情报",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新威胁情报"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="威胁情报未找到"
            )
        }
    ),
    partial_update=extend_schema(
        tags=['威胁情报管理'],
        summary="部分更新威胁情报",
        description="部分更新指定的威胁情报",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功更新威胁情报"
            ),
            400: OpenApiResponse(
                response=StandardResponseSerializer,
                description="数据验证失败"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="威胁情报未找到"
            )
        }
    ),
    destroy=extend_schema(
        tags=['威胁情报管理'],
        summary="删除威胁情报",
        description="删除指定的威胁情报",
        responses={
            204: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功删除威胁情报"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="威胁情报未找到"
            )
        }
    )
)
class IntelPostViewSet(StandardModelViewSet):
    """
    威胁情报视图集 - 提供完整的增删改查功能

    支持的操作：
    - GET /posts/ - 获取威胁情报列表
    - GET /posts/{id}/ - 获取威胁情报详情
    - POST /posts/ - 创建威胁情报
    - PUT /posts/{id}/ - 更新威胁情报
    - PATCH /posts/{id}/ - 部分更新威胁情报
    - DELETE /posts/{id}/ - 删除威胁情报
    - GET /posts/stats/ - 获取统计数据
    """

    queryset = IntelPost.objects.select_related('category').prefetch_related('tags').all()
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['source', 'category']
    search_fields = ['title', 'keywords', 'description', 'content']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']

    def get_permissions(self):
        """
        根据动作设置权限
        - retrieve 操作需要用户登录
        - 其他操作允许匿名访问
        """
        return get_permissions_for_action(self.action)

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return IntelPostListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return IntelPostCreateUpdateSerializer
        return IntelPostDetailSerializer

    def get_queryset(self):
        """自定义查询集，支持额外的筛选"""
        queryset = super().get_queryset()

        # 时间范围筛选
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        return queryset

    def retrieve(self, request, *args, **kwargs):
        """获取威胁情报详情"""
        return super().retrieve(request, *args, **kwargs)

    @extend_schema(
        tags=['威胁情报管理'],
        summary="获取威胁情报统计数据",
        description="获取威胁情报的统计信息，包括总数、各类型分布等",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取统计数据"
            )
        }
    )
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取威胁情报统计数据"""
        from datetime import datetime, timedelta

        # 基础统计
        total = IntelPost.objects.count()

        # 按来源统计
        source_stats = IntelPost.objects.values('source').annotate(
            count=Count('id')
        ).order_by('-count')

        # 按分类统计
        category_stats = IntelPost.objects.values('category__name').annotate(
            count=Count('id')
        ).order_by('-count')

        # 最近30天新增的情报数
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_count = IntelPost.objects.filter(created_at__gte=thirty_days_ago).count()

        stats_data = {
            'total': total,
            'recent_count': recent_count,
            'source_distribution': list(source_stats),
            'category_distribution': list(category_stats)
        }

        return Response({
            "success": True,
            "message": "获取统计数据成功",
            "data": stats_data
        })


@extend_schema_view(
    list=extend_schema(
        tags=['威胁情报管理'],
        summary="获取情报分类列表",
        description="获取所有情报分类列表",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取分类列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['威胁情报管理'],
        summary="获取情报分类详情",
        description="根据ID获取特定情报分类的详细信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取分类详情"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="分类未找到"
            )
        }
    )
)
class IntelCategoryViewSet(StandardReadOnlyModelViewSet):
    """
    情报分类视图集 - 只提供读取功能
    """
    queryset = IntelCategory.objects.all()
    serializer_class = IntelCategorySerializer
    ordering = ['name']


@extend_schema_view(
    list=extend_schema(
        tags=['威胁情报管理'],
        summary="获取情报标签列表",
        description="获取所有情报标签列表",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取标签列表"
            )
        }
    ),
    retrieve=extend_schema(
        tags=['威胁情报管理'],
        summary="获取情报标签详情",
        description="根据ID获取特定情报标签的详细信息",
        responses={
            200: OpenApiResponse(
                response=StandardResponseSerializer,
                description="成功获取标签详情"
            ),
            404: OpenApiResponse(
                response=StandardResponseSerializer,
                description="标签未找到"
            )
        }
    )
)
class IntelTagViewSet(StandardReadOnlyModelViewSet):
    """
    情报标签视图集 - 只提供读取功能
    """
    queryset = IntelTag.objects.all()
    serializer_class = IntelTagSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering = ['name']
