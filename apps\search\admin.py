"""
搜索应用管理界面
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import SearchResult, SearchStatistics


@admin.register(SearchResult)
class SearchResultAdmin(admin.ModelAdmin):
    """
    搜索结果管理
    """
    list_display = [
        'query', 'content_type', 'object_id', 'rank',
        'search_count', 'created_at'
    ]
    list_filter = ['content_type', 'created_at']
    search_fields = ['query']
    ordering = ['-rank', '-search_count']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('搜索信息', {
            'fields': ('query', 'content_type', 'object_id')
        }),
        ('相关性信息', {
            'fields': ('rank', 'search_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related()


@admin.register(SearchStatistics)
class SearchStatisticsAdmin(admin.ModelAdmin):
    """
    搜索统计管理
    """
    list_display = [
        'query', 'search_count', 'result_count',
        'last_searched', 'popularity_indicator'
    ]
    list_filter = ['last_searched']
    search_fields = ['query']
    ordering = ['-search_count', '-last_searched']
    readonly_fields = ['created_at', 'updated_at', 'last_searched']

    fieldsets = (
        ('搜索信息', {
            'fields': ('query',)
        }),
        ('统计信息', {
            'fields': ('search_count', 'result_count')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'last_searched')
        }),
    )

    def popularity_indicator(self, obj):
        """
        热门程度指示器
        """
        if obj.search_count >= 100:
            color = 'red'
            level = '非常热门'
        elif obj.search_count >= 50:
            color = 'orange'
            level = '热门'
        elif obj.search_count >= 10:
            color = 'blue'
            level = '一般'
        else:
            color = 'gray'
            level = '冷门'

        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            level
        )
    popularity_indicator.short_description = '热门程度'

    def get_queryset(self, request):
        """优化查询性能"""
        return super().get_queryset(request).select_related()

    actions = ['clear_old_statistics']

    def clear_old_statistics(self, request, queryset):
        """
        清除旧的搜索统计
        """
        # 删除30天前的统计数据
        cutoff_date = timezone.now() - timezone.timedelta(days=30)
        old_stats = SearchStatistics.objects.filter(last_searched__lt=cutoff_date)
        count = old_stats.count()
        old_stats.delete()

        self.message_user(
            request,
            f'已清除 {count} 条30天前的搜索统计记录'
        )
    clear_old_statistics.short_description = '清除30天前的统计数据'


# 自定义管理界面标题
admin.site.site_header = '威胁情报数据中心管理后台'
admin.site.site_title = '威胁情报数据中心'
admin.site.index_title = '搜索功能管理'
