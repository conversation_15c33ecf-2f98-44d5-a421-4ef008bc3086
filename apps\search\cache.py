"""
搜索缓存管理
"""
from django.core.cache import cache
from django.conf import settings
import hashlib
import json
from typing import Any, Optional, Dict, List


class SearchCacheManager:
    """
    搜索缓存管理器
    """
    
    # 缓存键前缀
    CACHE_PREFIX = 'search'
    
    # 缓存超时时间（秒）
    DEFAULT_TIMEOUT = 300  # 5分钟
    POPULAR_SEARCHES_TIMEOUT = 3600  # 1小时
    SUGGESTIONS_TIMEOUT = 1800  # 30分钟
    
    def __init__(self):
        self.enabled = getattr(settings, 'SEARCH_CACHE_ENABLED', True)
    
    def get_search_results(
        self, 
        query: str, 
        content_types: Optional[List[str]] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Optional[Dict[str, Any]]:
        """
        获取缓存的搜索结果
        
        Args:
            query: 搜索关键词
            content_types: 内容类型列表
            page: 页码
            page_size: 每页大小
            
        Returns:
            缓存的搜索结果，如果不存在则返回None
        """
        if not self.enabled:
            return None
            
        cache_key = self._generate_search_cache_key(query, content_types, page, page_size)
        return cache.get(cache_key)
    
    def set_search_results(
        self,
        query: str,
        content_types: Optional[List[str]],
        page: int,
        page_size: int,
        results: Dict[str, Any],
        timeout: Optional[int] = None
    ):
        """
        缓存搜索结果
        
        Args:
            query: 搜索关键词
            content_types: 内容类型列表
            page: 页码
            page_size: 每页大小
            results: 搜索结果
            timeout: 缓存超时时间
        """
        if not self.enabled:
            return
            
        cache_key = self._generate_search_cache_key(query, content_types, page, page_size)
        timeout = timeout or self.DEFAULT_TIMEOUT
        cache.set(cache_key, results, timeout)
    
    def get_popular_searches(self) -> Optional[List[Dict[str, Any]]]:
        """
        获取缓存的热门搜索
        
        Returns:
            缓存的热门搜索列表
        """
        if not self.enabled:
            return None
            
        cache_key = f"{self.CACHE_PREFIX}:popular_searches"
        return cache.get(cache_key)
    
    def set_popular_searches(self, popular_searches: List[Dict[str, Any]]):
        """
        缓存热门搜索
        
        Args:
            popular_searches: 热门搜索列表
        """
        if not self.enabled:
            return
            
        cache_key = f"{self.CACHE_PREFIX}:popular_searches"
        cache.set(cache_key, popular_searches, self.POPULAR_SEARCHES_TIMEOUT)
    
    def get_search_suggestions(self, query: str) -> Optional[List[Dict[str, Any]]]:
        """
        获取缓存的搜索建议
        
        Args:
            query: 搜索关键词前缀
            
        Returns:
            缓存的搜索建议列表
        """
        if not self.enabled:
            return None
            
        cache_key = f"{self.CACHE_PREFIX}:suggestions:{hashlib.md5(query.encode()).hexdigest()}"
        return cache.get(cache_key)
    
    def set_search_suggestions(self, query: str, suggestions: List[Dict[str, Any]]):
        """
        缓存搜索建议
        
        Args:
            query: 搜索关键词前缀
            suggestions: 搜索建议列表
        """
        if not self.enabled:
            return
            
        cache_key = f"{self.CACHE_PREFIX}:suggestions:{hashlib.md5(query.encode()).hexdigest()}"
        cache.set(cache_key, suggestions, self.SUGGESTIONS_TIMEOUT)
    
    def invalidate_search_cache(self, pattern: Optional[str] = None):
        """
        清除搜索缓存
        
        Args:
            pattern: 缓存键模式，如果为None则清除所有搜索缓存
        """
        if not self.enabled:
            return
            
        if pattern:
            # 这里可以实现更精确的缓存清除逻辑
            # 由于Django的缓存后端限制，这里简化处理
            pass
        else:
            # 清除所有缓存（简化处理）
            cache.clear()
    
    def _generate_search_cache_key(
        self,
        query: str,
        content_types: Optional[List[str]],
        page: int,
        page_size: int
    ) -> str:
        """
        生成搜索缓存键
        
        Args:
            query: 搜索关键词
            content_types: 内容类型列表
            page: 页码
            page_size: 每页大小
            
        Returns:
            缓存键
        """
        content_types_str = ','.join(sorted(content_types or []))
        cache_data = {
            'query': query,
            'content_types': content_types_str,
            'page': page,
            'page_size': page_size
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        cache_hash = hashlib.md5(cache_str.encode()).hexdigest()
        return f"{self.CACHE_PREFIX}:results:{cache_hash}"


class SearchPerformanceOptimizer:
    """
    搜索性能优化器
    """
    
    @staticmethod
    def optimize_queryset(queryset, content_type: str):
        """
        优化查询集性能
        
        Args:
            queryset: Django查询集
            content_type: 内容类型
            
        Returns:
            优化后的查询集
        """
        # 根据不同的内容类型进行查询优化
        optimization_map = {
            'ransomware_group': lambda qs: qs.select_related(),
            'negotiation_record': lambda qs: qs.select_related('group'),
            'intel_post': lambda qs: qs.select_related('category'),
            'tools': lambda qs: qs.select_related('ransomware_group'),
            'ransom_note': lambda qs: qs.select_related('group'),
            'ioc_indicator': lambda qs: qs.select_related('group'),
            'victim': lambda qs: qs.prefetch_related('group'),  # 多对多关系使用prefetch_related
            'blog_post': lambda qs: qs.select_related('category').prefetch_related('tags'),
        }
        
        optimizer = optimization_map.get(content_type)
        if optimizer:
            return optimizer(queryset)
        return queryset
    
    @staticmethod
    def get_search_fields_for_model(content_type: str) -> List[str]:
        """
        获取模型的搜索字段
        
        Args:
            content_type: 内容类型
            
        Returns:
            搜索字段列表
        """
        search_fields_map = {
            'ransomware_group': ['name', 'aliases', 'description'],
            'negotiation_record': ['group__name', 'initialransom', 'negotiatedransom'],
            'intel_post': ['title', 'description', 'content'],
            'tools': ['name', 'description', 'content'],
            'ransom_note': ['note_name', 'content'],
            'ioc_indicator': ['group__name'],
            'victim': ['post_title', 'website', 'country', 'description'],
            'blog_post': ['title', 'content', 'excerpt', 'meta_keywords'],
        }
        
        return search_fields_map.get(content_type, [])
    
    @staticmethod
    def should_use_full_text_search(query: str) -> bool:
        """
        判断是否应该使用全文搜索
        
        Args:
            query: 搜索关键词
            
        Returns:
            是否使用全文搜索
        """
        # 如果查询词太短，使用简单的LIKE查询可能更快
        if len(query.strip()) < 3:
            return False
        
        # 如果查询词包含特殊字符，可能需要特殊处理
        special_chars = ['*', '?', '[', ']', '(', ')', '{', '}']
        if any(char in query for char in special_chars):
            return False
        
        return True
    
    @staticmethod
    def get_optimal_page_size(total_count: int, requested_page_size: int) -> int:
        """
        获取最优的分页大小
        
        Args:
            total_count: 总记录数
            requested_page_size: 请求的分页大小
            
        Returns:
            最优的分页大小
        """
        # 如果总记录数很少，不需要分页
        if total_count <= 20:
            return min(total_count, requested_page_size)
        
        # 限制最大分页大小以避免性能问题
        max_page_size = 100
        return min(requested_page_size, max_page_size)


# 全局缓存管理器实例
search_cache_manager = SearchCacheManager()
