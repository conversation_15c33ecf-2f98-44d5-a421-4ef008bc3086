"""
重建搜索索引的管理命令
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.postgres.search import SearchVector
from apps.group.models import RansomwareGroup, NegotiationRecord, Tools, RansomNote, IOCIndicator, Victim
from apps.intell.models import IntelPost


class Command(BaseCommand):
    help = '重建全文搜索索引'

    def add_arguments(self, parser):
        parser.add_argument(
            '--model',
            type=str,
            help='指定要重建索引的模型 (ransomware_group, negotiation_record, intel_post, tools, ransom_note, ioc_indicator, victim)',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='批处理大小 (默认: 100)',
        )

    def handle(self, *args, **options):
        model_name = options.get('model')
        batch_size = options['batch_size']

        if model_name:
            self.rebuild_model_index(model_name, batch_size)
        else:
            self.rebuild_all_indexes(batch_size)

    def rebuild_all_indexes(self, batch_size):
        """重建所有模型的搜索索引"""
        models = [
            'ransomware_group',
            'negotiation_record', 
            'intel_post',
            'tools',
            'ransom_note',
            'ioc_indicator',
            'victim'
        ]
        
        for model_name in models:
            self.rebuild_model_index(model_name, batch_size)

    def rebuild_model_index(self, model_name, batch_size):
        """重建指定模型的搜索索引"""
        model_map = {
            'ransomware_group': (RansomwareGroup, self.update_ransomware_group_search_vector),
            'negotiation_record': (NegotiationRecord, self.update_negotiation_record_search_vector),
            'intel_post': (IntelPost, self.update_intel_post_search_vector),
            'tools': (Tools, self.update_tools_search_vector),
            'ransom_note': (RansomNote, self.update_ransom_note_search_vector),
            'ioc_indicator': (IOCIndicator, self.update_ioc_indicator_search_vector),
            'victim': (Victim, self.update_victim_search_vector),
        }

        if model_name not in model_map:
            self.stdout.write(
                self.style.ERROR(f'未知的模型: {model_name}')
            )
            return

        model_class, update_func = model_map[model_name]
        
        self.stdout.write(f'开始重建 {model_class.__name__} 的搜索索引...')
        
        total_count = model_class.objects.count()
        updated_count = 0
        
        # 分批处理
        for offset in range(0, total_count, batch_size):
            with transaction.atomic():
                objects = model_class.objects.all()[offset:offset + batch_size]
                for obj in objects:
                    update_func(obj)
                    obj.save(update_fields=['search_vector'])
                    updated_count += 1
                
                self.stdout.write(f'已处理 {updated_count}/{total_count} 条记录')
        
        self.stdout.write(
            self.style.SUCCESS(f'成功重建 {model_class.__name__} 的搜索索引，共处理 {updated_count} 条记录')
        )

    def update_ransomware_group_search_vector(self, obj):
        """更新勒索组织的搜索向量"""
        aliases_text = ' '.join(obj.aliases) if obj.aliases else ''
        
        search_vector = (
            SearchVector('name', weight='A', config='simple') +
            SearchVector(aliases_text, weight='B', config='simple') +
            SearchVector('description', weight='C', config='simple') +
            SearchVector('external_information_source', weight='D', config='simple')
        )
        
        # 直接设置搜索向量的文本表示
        obj.search_vector = (
            f"setweight(to_tsvector('simple', '{obj.name or ''}'), 'A') || "
            f"setweight(to_tsvector('simple', '{aliases_text}'), 'B') || "
            f"setweight(to_tsvector('simple', '{obj.description or ''}'), 'C') || "
            f"setweight(to_tsvector('simple', '{obj.external_information_source or ''}'), 'D')"
        )

    def update_negotiation_record_search_vector(self, obj):
        """更新谈判记录的搜索向量"""
        group_name = obj.group.name if obj.group else ''
        messages_text = ''
        
        if obj.messages:
            messages_text = ' '.join([
                msg.get('content', '') for msg in obj.messages 
                if isinstance(msg, dict) and 'content' in msg
            ])
        
        obj.search_vector = (
            f"setweight(to_tsvector('simple', '{group_name}'), 'A') || "
            f"setweight(to_tsvector('simple', '{obj.initialransom or ''}'), 'B') || "
            f"setweight(to_tsvector('simple', '{obj.negotiatedransom or ''}'), 'B') || "
            f"setweight(to_tsvector('simple', '{messages_text}'), 'C')"
        )

    def update_intel_post_search_vector(self, obj):
        """更新威胁情报的搜索向量"""
        obj.search_vector = (
            f"setweight(to_tsvector('simple', '{obj.title or ''}'), 'A') || "
            f"setweight(to_tsvector('simple', '{obj.description or ''}'), 'B') || "
            f"setweight(to_tsvector('simple', '{obj.content or ''}'), 'C') || "
            f"setweight(to_tsvector('simple', '{obj.keywords or ''}'), 'D') || "
            f"setweight(to_tsvector('simple', '{obj.source or ''}'), 'D')"
        )

    def update_tools_search_vector(self, obj):
        """更新应急工具的搜索向量"""
        group_name = obj.ransomware_group.name if obj.ransomware_group else ''
        
        obj.search_vector = (
            f"setweight(to_tsvector('simple', '{obj.name or ''}'), 'A') || "
            f"setweight(to_tsvector('simple', '{obj.description or ''}'), 'B') || "
            f"setweight(to_tsvector('simple', '{group_name}'), 'B') || "
            f"setweight(to_tsvector('simple', '{obj.content or ''}'), 'C')"
        )

    def update_ransom_note_search_vector(self, obj):
        """更新勒索信的搜索向量"""
        group_name = obj.group.name if obj.group else ''
        
        obj.search_vector = (
            f"setweight(to_tsvector('simple', '{obj.note_name or ''}'), 'A') || "
            f"setweight(to_tsvector('simple', '{obj.content or ''}'), 'B') || "
            f"setweight(to_tsvector('simple', '{group_name}'), 'B') || "
            f"setweight(to_tsvector('simple', '{obj.extension or ''}'), 'D')"
        )

    def update_ioc_indicator_search_vector(self, obj):
        """更新IOC指标的搜索向量"""
        group_name = obj.group.name if obj.group else ''
        iocs_text = ''
        types_text = ''
        
        if obj.iocs:
            iocs_text = ' '.join([
                str(value) for value in obj.iocs.values() 
                if isinstance(value, (str, list))
            ])
        
        if obj.ioc_types:
            types_text = ' '.join(obj.ioc_types)
        
        obj.search_vector = (
            f"setweight(to_tsvector('simple', '{group_name}'), 'A') || "
            f"setweight(to_tsvector('simple', '{iocs_text}'), 'B') || "
            f"setweight(to_tsvector('simple', '{types_text}'), 'C')"
        )

    def update_victim_search_vector(self, obj):
        """更新受害者的搜索向量"""
        # 由于是多对多关系，获取所有关联组织的名称
        group_names = ' '.join([group.name for group in obj.group.all()])

        obj.search_vector = (
            f"setweight(to_tsvector('simple', '{obj.post_title or ''}'), 'A') || "
            f"setweight(to_tsvector('simple', '{obj.website or ''}'), 'B') || "
            f"setweight(to_tsvector('simple', '{obj.country or ''}'), 'B') || "
            f"setweight(to_tsvector('simple', '{group_names}'), 'B') || "
            f"setweight(to_tsvector('simple', '{obj.description or ''}'), 'C') || "
            f"setweight(to_tsvector('simple', '{obj.activity or ''}'), 'C')"
        )
