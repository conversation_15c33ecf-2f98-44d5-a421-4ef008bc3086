"""
测试搜索功能的管理命令
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.search.services import UnifiedSearchService
from apps.group.models import RansomwareGroup
from apps.intell.models import IntelPost


class Command(BaseCommand):
    help = '测试搜索功能'

    def add_arguments(self, parser):
        parser.add_argument(
            '--query',
            type=str,
            default='test',
            help='搜索关键词 (默认: test)',
        )
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='创建测试数据',
        )

    def handle(self, *args, **options):
        query = options['query']
        create_test_data = options['create_test_data']

        if create_test_data:
            self.create_test_data()

        self.test_search_functionality(query)

    def create_test_data(self):
        """创建测试数据"""
        self.stdout.write('创建测试数据...')
        
        # 创建测试勒索组织
        group, created = RansomwareGroup.objects.get_or_create(
            name="SearchTestGroup",
            defaults={
                'slug': 'search-test-group',
                'description': '这是一个用于测试搜索功能的勒索组织',
                'status': 'active',
                'threat_level': 'high',
                'aliases': ['TestGroup', 'SearchGroup'],
            }
        )
        
        if created:
            self.stdout.write(f'创建勒索组织: {group.name}')
        else:
            self.stdout.write(f'勒索组织已存在: {group.name}')

        # 创建测试威胁情报
        from apps.intell.models import IntelCategory
        
        category, _ = IntelCategory.objects.get_or_create(
            name="测试分类",
            defaults={'description': '测试分类描述'}
        )
        
        post, created = IntelPost.objects.get_or_create(
            title="搜索测试威胁情报",
            defaults={
                'description': '这是一个用于测试搜索功能的威胁情报文章',
                'content': '这里是威胁情报的详细内容，包含了各种关键词用于测试搜索功能。',
                'category': category,
                'threat_type': '恶意软件',
                'severity': '高',
                'source': '内部监测',
                'confidence': 90,
            }
        )
        
        if created:
            self.stdout.write(f'创建威胁情报: {post.title}')
        else:
            self.stdout.write(f'威胁情报已存在: {post.title}')

        self.stdout.write(self.style.SUCCESS('测试数据创建完成'))

    def test_search_functionality(self, query):
        """测试搜索功能"""
        self.stdout.write(f'开始测试搜索功能，查询词: "{query}"')
        
        search_service = UnifiedSearchService()
        
        # 测试基本搜索
        self.stdout.write('\n1. 测试基本搜索...')
        start_time = timezone.now()
        results = search_service.search(query)
        end_time = timezone.now()
        
        search_time = (end_time - start_time).total_seconds() * 1000
        
        self.stdout.write(f'搜索耗时: {search_time:.2f}ms')
        self.stdout.write(f'总结果数: {results["pagination"]["total_count"]}')
        self.stdout.write(f'当前页结果数: {len(results["results"])}')
        
        # 显示搜索结果
        if results['results']:
            self.stdout.write('\n搜索结果:')
            for i, result in enumerate(results['results'][:5], 1):
                self.stdout.write(
                    f'  {i}. [{result["content_type"]}] {result["title"]} '
                    f'(相关性: {result["rank"]:.3f})'
                )
        
        # 测试按内容类型过滤
        self.stdout.write('\n2. 测试按内容类型过滤...')
        for content_type in ['ransomware_group', 'intel_post']:
            filtered_results = search_service.search(
                query, 
                content_types=[content_type]
            )
            count = filtered_results['pagination']['total_count']
            self.stdout.write(f'  {content_type}: {count} 个结果')
        
        # 测试分页
        self.stdout.write('\n3. 测试分页功能...')
        page1_results = search_service.search(query, page=1, page_size=2)
        self.stdout.write(f'第1页 (每页2条): {len(page1_results["results"])} 个结果')
        
        if page1_results['pagination']['has_next']:
            page2_results = search_service.search(query, page=2, page_size=2)
            self.stdout.write(f'第2页 (每页2条): {len(page2_results["results"])} 个结果')
        
        # 测试热门搜索
        self.stdout.write('\n4. 测试热门搜索...')
        popular_searches = search_service.get_popular_searches(limit=5)
        self.stdout.write(f'热门搜索词数量: {len(popular_searches)}')
        
        if popular_searches:
            self.stdout.write('热门搜索词:')
            for search in popular_searches:
                self.stdout.write(
                    f'  "{search["query"]}" (搜索{search["search_count"]}次)'
                )
        
        # 测试缓存
        self.stdout.write('\n5. 测试缓存功能...')
        
        # 第一次搜索（无缓存）
        start_time = timezone.now()
        search_service.search(query, use_cache=False)
        no_cache_time = (timezone.now() - start_time).total_seconds() * 1000
        
        # 第二次搜索（有缓存）
        start_time = timezone.now()
        search_service.search(query, use_cache=True)
        cache_time = (timezone.now() - start_time).total_seconds() * 1000
        
        self.stdout.write(f'无缓存搜索耗时: {no_cache_time:.2f}ms')
        self.stdout.write(f'有缓存搜索耗时: {cache_time:.2f}ms')
        
        if cache_time < no_cache_time:
            improvement = ((no_cache_time - cache_time) / no_cache_time) * 100
            self.stdout.write(f'缓存性能提升: {improvement:.1f}%')
        
        # 测试空查询
        self.stdout.write('\n6. 测试空查询处理...')
        empty_results = search_service.search('')
        self.stdout.write(f'空查询结果数: {len(empty_results["results"])}')
        
        # 测试特殊字符查询
        self.stdout.write('\n7. 测试特殊字符查询...')
        special_results = search_service.search('test*')
        self.stdout.write(f'特殊字符查询结果数: {len(special_results["results"])}')
        
        self.stdout.write(self.style.SUCCESS('\n搜索功能测试完成！'))
        
        # 性能建议
        if search_time > 1000:  # 超过1秒
            self.stdout.write(
                self.style.WARNING(
                    f'警告: 搜索耗时较长 ({search_time:.2f}ms)，建议检查数据库索引和查询优化'
                )
            )
        elif search_time > 500:  # 超过500ms
            self.stdout.write(
                self.style.WARNING(
                    f'注意: 搜索耗时 {search_time:.2f}ms，可以考虑进一步优化'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'搜索性能良好: {search_time:.2f}ms'
                )
            )
