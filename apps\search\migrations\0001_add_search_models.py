# Generated by Django 5.2.3 on 2025-07-22 17:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SearchResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('query', models.CharField(help_text='用户输入的搜索关键词', max_length=500, verbose_name='搜索查询')),
                ('content_type', models.CharField(choices=[('ransomware_group', '勒索组织'), ('negotiation_record', '谈判记录'), ('intel_post', '威胁情报'), ('tools', '应急工具'), ('ransom_note', '勒索信'), ('ioc_indicator', 'IOC指标'), ('victim', '受害者')], help_text='搜索结果的数据类型', max_length=50, verbose_name='内容类型')),
                ('object_id', models.PositiveIntegerField(help_text='搜索结果对象的主键ID', verbose_name='对象ID')),
                ('rank', models.FloatField(help_text='PostgreSQL 全文搜索的相关性评分', verbose_name='相关性评分')),
                ('search_count', models.PositiveIntegerField(default=1, help_text='此结果被搜索到的次数', verbose_name='搜索次数')),
            ],
            options={
                'verbose_name': '搜索结果',
                'verbose_name_plural': '搜索结果',
                'ordering': ['-rank', '-search_count'],
                'indexes': [models.Index(fields=['query', 'content_type'], name='search_sear_query_b8ee7c_idx'), models.Index(fields=['rank'], name='search_sear_rank_ed26f1_idx'), models.Index(fields=['created_at'], name='search_sear_created_f0be95_idx')],
            },
        ),
        migrations.CreateModel(
            name='SearchStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('query', models.CharField(help_text='搜索关键词', max_length=500, unique=True, verbose_name='搜索查询')),
                ('search_count', models.PositiveIntegerField(default=1, help_text='此关键词被搜索的总次数', verbose_name='搜索次数')),
                ('result_count', models.PositiveIntegerField(default=0, help_text='最近一次搜索返回的结果数量', verbose_name='结果数量')),
                ('last_searched', models.DateTimeField(auto_now=True, help_text='最后一次搜索的时间', verbose_name='最后搜索时间')),
            ],
            options={
                'verbose_name': '搜索统计',
                'verbose_name_plural': '搜索统计',
                'ordering': ['-search_count', '-last_searched'],
                'indexes': [models.Index(fields=['search_count'], name='search_sear_search__b45055_idx'), models.Index(fields=['last_searched'], name='search_sear_last_se_0e03e1_idx')],
            },
        ),
    ]
