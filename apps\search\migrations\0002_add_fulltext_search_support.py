# Generated manually for full-text search functionality

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('search', '0001_add_search_models'),
        ('group', '0014_add_negotiation_record_model'),
        ('intell', '0001_initial'),
    ]

    operations = [
        # 启用 PostgreSQL 扩展和添加搜索字段
        migrations.RunSQL(
            sql=[
                # 启用必要的PostgreSQL扩展
                "CREATE EXTENSION IF NOT EXISTS pg_trgm;",
                
                # 为各个表添加搜索向量字段
                "ALTER TABLE group_ransomwaregroup ADD COLUMN IF NOT EXISTS search_vector text;",
                "ALTER TABLE group_negotiationrecord ADD COLUMN IF NOT EXISTS search_vector text;", 
                "ALTER TABLE intell_intelpost ADD COLUMN IF NOT EXISTS search_vector text;",
                "ALTER TABLE group_tools ADD COLUMN IF NOT EXISTS search_vector text;",
                "ALTER TABLE group_ransomnote ADD COLUMN IF NOT EXISTS search_vector text;",
                "ALTER TABLE group_iocindicator ADD COLUMN IF NOT EXISTS search_vector text;",
                "ALTER TABLE group_victim ADD COLUMN IF NOT EXISTS search_vector text;",
                
                # 先更新现有数据的搜索向量（简单版本）
                "UPDATE group_ransomwaregroup SET search_vector = name WHERE search_vector IS NULL;",
                "UPDATE group_negotiationrecord SET search_vector = COALESCE(initialransom, '') WHERE search_vector IS NULL;",
                "UPDATE intell_intelpost SET search_vector = title WHERE search_vector IS NULL;",
                "UPDATE group_tools SET search_vector = name WHERE search_vector IS NULL;",
                "UPDATE group_ransomnote SET search_vector = note_name WHERE search_vector IS NULL;",
                "UPDATE group_iocindicator SET search_vector = '' WHERE search_vector IS NULL;",
                "UPDATE group_victim SET search_vector = COALESCE(post_title, '') WHERE search_vector IS NULL;",
            ],
            reverse_sql=[
                # 删除索引
                "DROP INDEX IF EXISTS group_ransomwaregroup_search_gin_idx;",
                "DROP INDEX IF EXISTS group_negotiationrecord_search_gin_idx;",
                "DROP INDEX IF EXISTS intell_intelpost_search_gin_idx;",
                "DROP INDEX IF EXISTS group_tools_search_gin_idx;",
                "DROP INDEX IF EXISTS group_ransomnote_search_gin_idx;",
                "DROP INDEX IF EXISTS group_iocindicator_search_gin_idx;",
                "DROP INDEX IF EXISTS group_victim_search_gin_idx;",
                
                # 删除字段
                "ALTER TABLE group_ransomwaregroup DROP COLUMN IF EXISTS search_vector;",
                "ALTER TABLE group_negotiationrecord DROP COLUMN IF EXISTS search_vector;",
                "ALTER TABLE intell_intelpost DROP COLUMN IF EXISTS search_vector;",
                "ALTER TABLE group_tools DROP COLUMN IF EXISTS search_vector;",
                "ALTER TABLE group_ransomnote DROP COLUMN IF EXISTS search_vector;",
                "ALTER TABLE group_iocindicator DROP COLUMN IF EXISTS search_vector;",
                "ALTER TABLE group_victim DROP COLUMN IF EXISTS search_vector;",
            ],
        ),
    ]
