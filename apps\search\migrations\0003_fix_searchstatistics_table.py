# Generated manually to fix SearchStatistics table structure

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('search', '0002_add_fulltext_search_support'),
    ]

    operations = [
        # 删除旧的 SearchStatistics 表
        migrations.RunSQL(
            sql="DROP TABLE IF EXISTS search_searchstatistics CASCADE;",
            reverse_sql=""
        ),
        
        # 重新创建 SearchStatistics 表，使用正确的结构
        migrations.CreateModel(
            name='SearchStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('query', models.CharField(help_text='搜索关键词', max_length=500, unique=True, verbose_name='搜索查询')),
                ('search_count', models.PositiveIntegerField(default=1, help_text='此关键词被搜索的总次数', verbose_name='搜索次数')),
                ('result_count', models.PositiveIntegerField(default=0, help_text='最近一次搜索返回的结果数量', verbose_name='结果数量')),
                ('last_searched', models.DateTimeField(auto_now=True, help_text='最后一次搜索的时间', verbose_name='最后搜索时间')),
            ],
            options={
                'verbose_name': '搜索统计',
                'verbose_name_plural': '搜索统计',
                'ordering': ['-search_count', '-last_searched'],
            },
        ),
        
        # 添加索引
        migrations.RunSQL(
            sql=[
                "CREATE INDEX search_searchstatistics_search_count_idx ON search_searchstatistics (search_count);",
                "CREATE INDEX search_searchstatistics_last_searched_idx ON search_searchstatistics (last_searched);",
            ],
            reverse_sql=[
                "DROP INDEX IF EXISTS search_searchstatistics_search_count_idx;",
                "DROP INDEX IF EXISTS search_searchstatistics_last_searched_idx;",
            ]
        ),
    ]
