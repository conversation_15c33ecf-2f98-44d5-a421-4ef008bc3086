# Generated by Django 5.2.3 on 2025-07-23 14:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('search', '0003_fix_searchstatistics_table'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='searchresult',
            name='content_type',
            field=models.CharField(choices=[('ransomware_group', '勒索组织'), ('negotiation_record', '谈判记录'), ('intel_post', '威胁情报'), ('tools', '应急工具'), ('ransom_note', '勒索信'), ('ioc_indicator', 'IOC指标'), ('victim', '受害者'), ('blog_post', '安全博客')], help_text='搜索结果的数据类型', max_length=50, verbose_name='内容类型'),
        ),
        migrations.AddIndex(
            model_name='searchstatistics',
            index=models.Index(fields=['search_count'], name='search_sear_search__b45055_idx'),
        ),
        migrations.AddIndex(
            model_name='searchstatistics',
            index=models.Index(fields=['last_searched'], name='search_sear_last_se_0e03e1_idx'),
        ),
    ]
