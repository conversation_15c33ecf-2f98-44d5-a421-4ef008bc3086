"""
全文搜索相关模型和配置
"""
from django.db import models
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
from django.contrib.postgres.indexes import GinIndex
from config.models import BaseModel


class SearchableModel(models.Model):
    """
    可搜索模型的抽象基类
    为模型添加全文搜索功能
    """
    # PostgreSQL 全文搜索向量字段
    search_vector = models.TextField(
        null=True,
        blank=True,
        verbose_name="搜索向量",
        help_text="PostgreSQL tsvector 字段，用于全文搜索"
    )

    class Meta:
        abstract = True
        indexes = [
            # 为搜索向量字段创建 GIN 索引
            GinIndex(fields=['search_vector'], name='%(class)s_search_gin_idx'),
        ]

    def update_search_vector(self):
        """
        更新搜索向量
        子类需要重写此方法来定义搜索内容
        """
        raise NotImplementedError("子类必须实现 update_search_vector 方法")


class SearchConfiguration:
    """
    搜索配置类
    定义不同模型的搜索字段和权重
    """

    # 搜索权重配置 (A=1.0, B=0.4, C=0.2, D=0.1)
    WEIGHT_A = 'A'  # 最高权重 - 标题、名称等主要字段
    WEIGHT_B = 'B'  # 高权重 - 描述、摘要等重要字段
    WEIGHT_C = 'C'  # 中权重 - 内容、详情等一般字段
    WEIGHT_D = 'D'  # 低权重 - 标签、别名等辅助字段

    # 勒索组织搜索配置
    RANSOMWARE_GROUP_CONFIG = {
        'name': WEIGHT_A,           # 组织名称 - 最高权重
        'aliases': WEIGHT_B,        # 别名 - 高权重
        'description': WEIGHT_C,    # 描述 - 中权重
        'external_information_source': WEIGHT_D,  # 外部信息源 - 低权重
    }

    # 谈判记录搜索配置
    NEGOTIATION_RECORD_CONFIG = {
        'group__name': WEIGHT_A,    # 关联组织名称 - 最高权重
        'initialransom': WEIGHT_B,  # 初始赎金 - 高权重
        'negotiatedransom': WEIGHT_B,  # 谈判后赎金 - 高权重
        'messages': WEIGHT_C,       # 消息内容 - 中权重
        'notes': WEIGHT_C,          # 备注 - 中权重
    }

    # 威胁情报搜索配置
    INTEL_POST_CONFIG = {
        'title': WEIGHT_A,          # 标题 - 最高权重
        'description': WEIGHT_B,    # 描述 - 高权重
        'content': WEIGHT_C,        # 内容 - 中权重
        'keywords': WEIGHT_D,       # 关键词 - 低权重
        'source': WEIGHT_D,         # 来源 - 低权重
    }

    # 应急工具搜索配置
    TOOLS_CONFIG = {
        'name': WEIGHT_A,           # 工具名称 - 最高权重
        'description': WEIGHT_B,    # 描述 - 高权重
        'content': WEIGHT_C,        # 内容 - 中权重
        'ransomware_group__name': WEIGHT_B,  # 关联组织名称 - 高权重
    }

    # 勒索信搜索配置
    RANSOM_NOTE_CONFIG = {
        'note_name': WEIGHT_A,      # 勒索信名称 - 最高权重
        'content': WEIGHT_B,        # 内容 - 高权重
        'group__name': WEIGHT_B,    # 关联组织名称 - 高权重
        'extension': WEIGHT_D,      # 扩展名 - 低权重
    }

    # IOC指标搜索配置
    IOC_INDICATOR_CONFIG = {
        'group__name': WEIGHT_A,    # 关联组织名称 - 最高权重
        'iocs': WEIGHT_B,           # IOC数据 - 高权重
        'ioc_types': WEIGHT_C,      # IOC类型 - 中权重
    }

    # 受害者搜索配置
    VICTIM_CONFIG = {
        'company': WEIGHT_A,        # 公司名称 - 最高权重
        'website': WEIGHT_B,        # 网站 - 高权重
        'country': WEIGHT_B,        # 国家 - 高权重
        'activity': WEIGHT_C,       # 活动状态 - 中权重
        'group__name': WEIGHT_B,    # 关联组织名称 - 高权重
    }

    # 博客文章搜索配置
    BLOG_POST_CONFIG = {
        'title': WEIGHT_A,          # 文章标题 - 最高权重
        'excerpt': WEIGHT_B,        # 文章摘要 - 高权重
        'content': WEIGHT_C,        # 文章内容 - 中权重
        'meta_keywords': WEIGHT_D,  # SEO关键词 - 低权重
        'meta_description': WEIGHT_D,  # SEO描述 - 低权重
    }


class SearchResult(BaseModel):
    """
    搜索结果模型
    用于缓存搜索结果和统计搜索行为
    """

    # 搜索内容类型
    CONTENT_TYPE_CHOICES = [
        ('ransomware_group', '勒索组织'),
        ('negotiation_record', '谈判记录'),
        ('intel_post', '威胁情报'),
        ('tools', '应急工具'),
        ('ransom_note', '勒索信'),
        ('ioc_indicator', 'IOC指标'),
        ('victim', '受害者'),
        ('blog_post', '安全博客'),
    ]

    query = models.CharField(
        max_length=500,
        verbose_name="搜索查询",
        help_text="用户输入的搜索关键词"
    )

    content_type = models.CharField(
        max_length=50,
        choices=CONTENT_TYPE_CHOICES,
        verbose_name="内容类型",
        help_text="搜索结果的数据类型"
    )

    object_id = models.PositiveIntegerField(
        verbose_name="对象ID",
        help_text="搜索结果对象的主键ID"
    )

    rank = models.FloatField(
        verbose_name="相关性评分",
        help_text="PostgreSQL 全文搜索的相关性评分"
    )

    search_count = models.PositiveIntegerField(
        default=1,
        verbose_name="搜索次数",
        help_text="此结果被搜索到的次数"
    )

    class Meta:
        verbose_name = "搜索结果"
        verbose_name_plural = "搜索结果"
        ordering = ['-rank', '-search_count']
        indexes = [
            models.Index(fields=['query', 'content_type']),
            models.Index(fields=['rank']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.query} -> {self.content_type}#{self.object_id} (rank: {self.rank})"


class SearchStatistics(BaseModel):
    """
    搜索统计模型
    记录搜索行为和热门搜索词
    """

    query = models.CharField(
        max_length=500,
        unique=True,
        verbose_name="搜索查询",
        help_text="搜索关键词"
    )

    search_count = models.PositiveIntegerField(
        default=1,
        verbose_name="搜索次数",
        help_text="此关键词被搜索的总次数"
    )

    result_count = models.PositiveIntegerField(
        default=0,
        verbose_name="结果数量",
        help_text="最近一次搜索返回的结果数量"
    )

    last_searched = models.DateTimeField(
        auto_now=True,
        verbose_name="最后搜索时间",
        help_text="最后一次搜索的时间"
    )

    class Meta:
        verbose_name = "搜索统计"
        verbose_name_plural = "搜索统计"
        ordering = ['-search_count', '-last_searched']
        indexes = [
            models.Index(fields=['search_count']),
            models.Index(fields=['last_searched']),
        ]

    def __str__(self):
        return f"{self.query} (搜索{self.search_count}次)"
