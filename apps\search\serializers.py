"""
搜索相关序列化器
"""
from rest_framework import serializers


class SearchRequestSerializer(serializers.Serializer):
    """
    搜索请求序列化器
    """
    q = serializers.CharField(
        max_length=500,
        required=True,
        help_text="搜索关键词"
    )
    
    content_types = serializers.CharField(
        max_length=500,
        required=False,
        help_text="要搜索的内容类型，多个类型用逗号分隔"
    )
    
    page = serializers.IntegerField(
        min_value=1,
        default=1,
        required=False,
        help_text="页码"
    )
    
    page_size = serializers.IntegerField(
        min_value=1,
        max_value=100,
        default=20,
        required=False,
        help_text="每页大小"
    )
    
    def validate_content_types(self, value):
        """
        验证内容类型
        """
        if not value:
            return None
            
        valid_types = {
            'ransomware_group', 'negotiation_record', 'intel_post',
            'tools', 'ransom_note', 'ioc_indicator', 'victim', 'blog_post'
        }
        
        types = [t.strip() for t in value.split(',') if t.strip()]
        invalid_types = [t for t in types if t not in valid_types]
        
        if invalid_types:
            raise serializers.ValidationError(
                f"无效的内容类型: {', '.join(invalid_types)}. "
                f"有效类型: {', '.join(valid_types)}"
            )
        
        return types


class SearchResultItemSerializer(serializers.Serializer):
    """
    搜索结果项序列化器
    """
    content_type = serializers.CharField(help_text="内容类型")
    object_id = serializers.IntegerField(help_text="对象ID")
    rank = serializers.FloatField(help_text="相关性评分")
    title = serializers.CharField(help_text="标题")
    description = serializers.CharField(help_text="描述", allow_null=True)
    url = serializers.CharField(help_text="对象URL")
    data = serializers.DictField(help_text="对象数据")
    created_at = serializers.CharField(help_text="创建时间", allow_null=True)


class PaginationSerializer(serializers.Serializer):
    """
    分页信息序列化器
    """
    page = serializers.IntegerField(help_text="当前页码")
    page_size = serializers.IntegerField(help_text="每页大小")
    total_count = serializers.IntegerField(help_text="总记录数")
    total_pages = serializers.IntegerField(help_text="总页数")
    has_next = serializers.BooleanField(help_text="是否有下一页")
    has_previous = serializers.BooleanField(help_text="是否有上一页")


class SearchResponseSerializer(serializers.Serializer):
    """
    搜索响应序列化器
    """
    query = serializers.CharField(help_text="搜索关键词")
    results = SearchResultItemSerializer(many=True, help_text="搜索结果列表")
    pagination = PaginationSerializer(help_text="分页信息")
    content_types = serializers.DictField(help_text="各内容类型的结果数量")


class PopularSearchSerializer(serializers.Serializer):
    """
    热门搜索序列化器
    """
    query = serializers.CharField(help_text="搜索关键词")
    search_count = serializers.IntegerField(help_text="搜索次数")
    result_count = serializers.IntegerField(help_text="结果数量")
    last_searched = serializers.DateTimeField(help_text="最后搜索时间")


class SearchSuggestionSerializer(serializers.Serializer):
    """
    搜索建议序列化器
    """
    suggestion = serializers.CharField(help_text="建议的搜索词")
    type = serializers.CharField(help_text="建议类型")
    count = serializers.IntegerField(help_text="相关结果数量")
