"""
搜索服务类
"""
from django.db import models
from django.contrib.postgres.search import SearchQuery, SearchRank, SearchVector
from django.core.paginator import Paginator
from django.db.models import Q
from typing import Dict, List, Any, Optional, Tuple

from apps.group.models import RansomwareGroup, NegotiationRecord, Tools, RansomNote, IOCIndicator, Victim
from apps.intell.models import IntelPost
from apps.blog.models import BlogPost
from .models import SearchStatistics
from .cache import search_cache_manager, SearchPerformanceOptimizer


class UnifiedSearchService:
    """
    统一搜索服务类
    提供跨模型的全文搜索功能
    """
    
    # 搜索模型配置
    SEARCH_MODELS = {
        'ransomware_group': {
            'model': RansomwareGroup,
            'fields': ['name', 'aliases', 'description', 'external_information_source'],
            'display_fields': ['id', 'name', 'slug', 'status', 'threat_level', 'description'],
            'title_field': 'name',
            'description_field': 'description',
        },
        'negotiation_record': {
            'model': NegotiationRecord,
            'fields': ['group__name', 'initialransom', 'negotiatedransom', 'messages'],
            'display_fields': ['id', 'group', 'initialransom', 'negotiatedransom', 'paid', 'message_count'],
            'title_field': 'group__name',
            'description_field': 'initialransom',
        },
        'intel_post': {
            'model': IntelPost,
            'fields': ['title', 'description', 'content', 'keywords', 'source'],
            'display_fields': ['id', 'title', 'description', 'keywords', 'source', 'category'],
            'title_field': 'title',
            'description_field': 'description',
        },
        'tools': {
            'model': Tools,
            'fields': ['name', 'description', 'content', 'ransomware_group__name'],
            'display_fields': ['id', 'name', 'description', 'ransomware_group', 'view_count'],
            'title_field': 'name',
            'description_field': 'description',
        },
        'ransom_note': {
            'model': RansomNote,
            'fields': ['note_name', 'content', 'group__name', 'extension'],
            'display_fields': ['id', 'note_name', 'content', 'group', 'extension'],
            'title_field': 'note_name',
            'description_field': 'content',
        },
        'ioc_indicator': {
            'model': IOCIndicator,
            'fields': ['group__name', 'iocs', 'ioc_types'],
            'display_fields': ['id', 'group', 'ioc_types', 'iocs'],
            'title_field': 'group__name',
            'description_field': 'ioc_types',
        },
        'victim': {
            'model': Victim,
            'fields': ['post_title', 'website', 'country', 'description', 'activity'],
            'display_fields': ['id', 'post_title', 'website', 'country', 'activity', 'description'],
            'title_field': 'post_title',
            'description_field': 'description',
        },
        'blog_post': {
            'model': BlogPost,
            'fields': ['title', 'content', 'excerpt', 'meta_keywords', 'meta_description'],
            'display_fields': ['id', 'title', 'excerpt', 'category', 'view_count', 'created_at', 'cover_image'],
            'title_field': 'title',
            'description_field': 'excerpt',
        },
    }

    def __init__(self):
        self.cache_manager = search_cache_manager
        self.performance_optimizer = SearchPerformanceOptimizer()

    def search(
        self, 
        query: str, 
        content_types: Optional[List[str]] = None,
        page: int = 1,
        page_size: int = 20,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        执行统一搜索
        
        Args:
            query: 搜索关键词
            content_types: 要搜索的内容类型列表，None表示搜索所有类型
            page: 页码
            page_size: 每页大小
            use_cache: 是否使用缓存
            
        Returns:
            搜索结果字典
        """
        if not query.strip():
            return self._empty_result()

        # 检查缓存
        if use_cache:
            cached_result = self.cache_manager.get_search_results(
                query, content_types, page, page_size
            )
            if cached_result:
                return cached_result

        # 执行搜索
        search_results = []
        total_count = 0
        
        # 确定要搜索的模型
        models_to_search = content_types or list(self.SEARCH_MODELS.keys())
        
        for content_type in models_to_search:
            if content_type in self.SEARCH_MODELS:
                results, count = self._search_model(content_type, query)
                search_results.extend(results)
                total_count += count

        # 按相关性排序
        search_results.sort(key=lambda x: x['rank'], reverse=True)

        # 优化分页大小
        optimal_page_size = self.performance_optimizer.get_optimal_page_size(
            total_count, page_size
        )

        # 确保分页大小至少为1
        if optimal_page_size <= 0:
            optimal_page_size = 1

        # 分页
        paginator = Paginator(search_results, optimal_page_size)
        page_obj = paginator.get_page(page)
        
        result = {
            'query': query,
            'results': list(page_obj),
            'pagination': {
                'page': page,
                'page_size': optimal_page_size,
                'total_count': total_count,
                'total_pages': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            },
            'content_types': {
                content_type: len([r for r in search_results if r['content_type'] == content_type])
                for content_type in models_to_search
            }
        }

        # 缓存结果
        if use_cache:
            self.cache_manager.set_search_results(
                query, content_types, page, optimal_page_size, result
            )
        
        # 更新搜索统计
        self._update_search_statistics(query, total_count)
        
        return result

    def _search_model(self, content_type: str, query: str) -> Tuple[List[Dict], int]:
        """
        搜索指定模型
        
        Args:
            content_type: 内容类型
            query: 搜索关键词
            
        Returns:
            (搜索结果列表, 总数)
        """
        config = self.SEARCH_MODELS[content_type]
        model = config['model']
        
        # 判断是否使用全文搜索
        use_full_text = self.performance_optimizer.should_use_full_text_search(query)

        # 获取基础查询集，对博客文章添加已发布过滤
        base_queryset = model.objects.all()
        if content_type == 'blog_post':
            base_queryset = base_queryset.filter(is_published=True)

        if use_full_text:
            # 使用PostgreSQL全文搜索
            search_query = SearchQuery(query, config='simple')
            queryset = base_queryset.annotate(
                rank=SearchRank(
                    SearchVector(*config['fields'], config='simple'),
                    search_query
                )
            ).filter(
                rank__gt=0
            ).order_by('-rank')
        else:
            # 使用简单的LIKE查询（对于短查询词可能更快）
            search_fields = self.performance_optimizer.get_search_fields_for_model(content_type)
            q_objects = Q()
            for field in search_fields:
                q_objects |= Q(**{f"{field}__icontains": query})

            queryset = base_queryset.filter(q_objects).annotate(
                rank=models.Value(1.0, output_field=models.FloatField())
            )

        # 应用性能优化
        try:
            queryset = self.performance_optimizer.optimize_queryset(queryset, content_type)
        except Exception:
            # 如果优化失败，使用原始查询集
            pass

        results = []
        for obj in queryset:
            result_data = {
                'content_type': content_type,
                'object_id': obj.id,
                'rank': float(obj.rank),
                'title': self._get_field_value(obj, config['title_field']),
                'description': self._get_field_value(obj, config['description_field']),
                'url': self._generate_object_url(content_type, obj),
                'data': {
                    field: self._get_field_value(obj, field)
                    for field in config['display_fields']
                },
                'created_at': obj.created_at.isoformat() if hasattr(obj, 'created_at') else None,
            }
            results.append(result_data)

        return results, queryset.count()

    def _get_field_value(self, obj: models.Model, field_path: str) -> Any:
        """
        获取对象字段值，支持关联字段，确保返回可序列化的值

        Args:
            obj: 模型对象
            field_path: 字段路径，如 'group__name'

        Returns:
            字段值（可序列化）
        """
        try:
            if '__' in field_path:
                # 处理关联字段
                parts = field_path.split('__')
                value = obj
                for part in parts:
                    value = getattr(value, part, None)
                    if value is None:
                        break
                return self._serialize_value(value)
            else:
                value = getattr(obj, field_path, None)
                return self._serialize_value(value)
        except (AttributeError, TypeError):
            return None

    def _serialize_value(self, value):
        """
        序列化值，确保可以转换为JSON

        Args:
            value: 要序列化的值

        Returns:
            可序列化的值
        """
        if value is None:
            return None

        # 如果是Django ImageField，返回URL
        if hasattr(value, 'url'):
            try:
                return value.url
            except (ValueError, AttributeError):
                return None

        # 如果是Django模型对象，返回其字符串表示或ID
        if hasattr(value, '_meta') and hasattr(value._meta, 'model'):
            # 对于博客分类，返回名称
            if hasattr(value, 'name'):
                return value.name
            # 对于其他模型，返回字符串表示
            return str(value)

        # 如果是日期时间对象，转换为ISO格式字符串
        if hasattr(value, 'isoformat'):
            return value.isoformat()

        # 其他情况直接返回
        return value

    def _generate_object_url(self, content_type: str, obj: models.Model) -> str:
        """
        生成对象的URL

        Args:
            content_type: 内容类型
            obj: 模型对象

        Returns:
            对象URL
        """
        # 安全地获取对象ID
        obj_id = getattr(obj, 'id', None)
        if obj_id is None:
            return '#'

        url_patterns = {
            'ransomware_group': f'/api/v1/groups/{obj_id}/',
            'negotiation_record': f'/api/v1/negotiation-records/{obj_id}/',
            'intel_post': f'/api/v1/intell/posts/{obj_id}/',
            'tools': f'/api/v1/tools/{obj_id}/',
            'ransom_note': f'/api/v1/ransom-notes/{obj_id}/',
            'ioc_indicator': f'/api/v1/ioc-indicators/{obj_id}/',
            'victim': f'/api/v1/victims/{obj_id}/',
            'blog_post': f'/api/v1/blog/posts/{obj_id}/',
        }
        return url_patterns.get(content_type, '#')



    def _empty_result(self) -> Dict[str, Any]:
        """
        返回空搜索结果
        
        Returns:
            空结果字典
        """
        return {
            'query': '',
            'results': [],
            'pagination': {
                'page': 1,
                'page_size': 20,
                'total_count': 0,
                'total_pages': 0,
                'has_next': False,
                'has_previous': False,
            },
            'content_types': {}
        }

    def _update_search_statistics(self, query: str, result_count: int):
        """
        更新搜索统计
        
        Args:
            query: 搜索关键词
            result_count: 结果数量
        """
        try:
            stats, created = SearchStatistics.objects.get_or_create(
                query=query,
                defaults={'result_count': result_count}
            )
            if not created:
                stats.search_count += 1
                stats.result_count = result_count
                stats.save(update_fields=['search_count', 'result_count', 'last_searched'])
        except Exception:
            # 忽略统计更新错误，不影响搜索功能
            pass

    def get_popular_searches(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取热门搜索词

        Args:
            limit: 返回数量限制

        Returns:
            热门搜索词列表
        """
        # 尝试从缓存获取
        cached_result = self.cache_manager.get_popular_searches()
        if cached_result:
            return cached_result[:limit]

        # 从数据库获取
        popular_searches = list(
            SearchStatistics.objects.order_by('-search_count', '-last_searched')[:50]
            .values('query', 'search_count', 'result_count', 'last_searched')
        )

        # 缓存结果
        self.cache_manager.set_popular_searches(popular_searches)

        return popular_searches[:limit]

    def clear_search_cache(self):
        """
        清除搜索缓存
        """
        self.cache_manager.invalidate_search_cache()
