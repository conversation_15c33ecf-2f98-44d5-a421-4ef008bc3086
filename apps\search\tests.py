"""
搜索功能测试
"""
from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock

from apps.group.models import RansomwareGroup, NegotiationRecord
from apps.intell.models import IntelPost, IntelCategory
from .services import UnifiedSearchService
from .models import SearchStatistics
from .cache import SearchCacheManager


class UnifiedSearchServiceTest(TestCase):
    """
    统一搜索服务测试
    """

    def setUp(self):
        """
        设置测试数据
        """
        self.search_service = UnifiedSearchService()

        # 创建测试数据
        self.ransomware_group = RansomwareGroup.objects.create(
            name="TestGroup",
            slug="test-group",
            description="Test ransomware group for testing",
            status="active",
            threat_level="high"
        )

        self.category = IntelCategory.objects.create(
            name="Test Category",
            description="Test category"
        )

        self.intel_post = IntelPost.objects.create(
            title="Test Intel Post",
            keywords="test, intelligence, malware",
            description="Test intelligence post for testing",
            content="This is a test content for intelligence post",
            category=self.category,
            source="内部监测"
        )

    def test_search_empty_query(self):
        """
        测试空查询
        """
        result = self.search_service.search("")
        self.assertEqual(result['query'], '')
        self.assertEqual(len(result['results']), 0)
        self.assertEqual(result['pagination']['total_count'], 0)

    def test_search_ransomware_group(self):
        """
        测试搜索勒索组织
        """
        result = self.search_service.search("TestGroup")
        self.assertGreater(len(result['results']), 0)

        # 检查结果中是否包含我们创建的组织
        found_group = False
        for item in result['results']:
            if item['content_type'] == 'ransomware_group' and item['object_id'] == self.ransomware_group.id:
                found_group = True
                break
        self.assertTrue(found_group)

    def test_search_intel_post(self):
        """
        测试搜索威胁情报
        """
        result = self.search_service.search("Test Intel")
        self.assertGreater(len(result['results']), 0)

        # 检查结果中是否包含我们创建的情报
        found_post = False
        for item in result['results']:
            if item['content_type'] == 'intel_post' and item['object_id'] == self.intel_post.id:
                found_post = True
                break
        self.assertTrue(found_post)

    def test_search_with_content_types_filter(self):
        """
        测试按内容类型过滤搜索
        """
        result = self.search_service.search("Test", content_types=['ransomware_group'])

        # 所有结果都应该是勒索组织类型
        for item in result['results']:
            self.assertEqual(item['content_type'], 'ransomware_group')

    def test_search_pagination(self):
        """
        测试搜索分页
        """
        result = self.search_service.search("Test", page=1, page_size=1)

        self.assertEqual(result['pagination']['page'], 1)
        self.assertEqual(result['pagination']['page_size'], 1)
        self.assertLessEqual(len(result['results']), 1)

    def test_get_popular_searches(self):
        """
        测试获取热门搜索
        """
        # 创建一些搜索统计数据
        SearchStatistics.objects.create(
            query="popular search",
            search_count=10,
            result_count=5
        )

        popular_searches = self.search_service.get_popular_searches(limit=5)
        self.assertLessEqual(len(popular_searches), 5)

        if popular_searches:
            self.assertIn('query', popular_searches[0])
            self.assertIn('search_count', popular_searches[0])


class SearchAPITest(APITestCase):
    """
    搜索API测试
    """

    def setUp(self):
        """
        设置测试数据
        """
        self.search_url = reverse('search:unified_search')
        self.popular_url = reverse('search:popular_searches')
        self.suggestions_url = reverse('search:search_suggestions')

        # 创建测试数据
        self.ransomware_group = RansomwareGroup.objects.create(
            name="APITestGroup",
            slug="api-test-group",
            description="API test ransomware group",
            status="active",
            threat_level="medium"
        )

    def test_search_api_success(self):
        """
        测试搜索API成功响应
        """
        response = self.client.get(self.search_url, {'q': 'APITestGroup'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('query', response.data)
        self.assertIn('results', response.data)
        self.assertIn('pagination', response.data)
        self.assertEqual(response.data['query'], 'APITestGroup')

    def test_search_api_missing_query(self):
        """
        测试搜索API缺少查询参数
        """
        response = self.client.get(self.search_url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_search_api_with_content_types(self):
        """
        测试搜索API带内容类型过滤
        """
        response = self.client.get(self.search_url, {
            'q': 'APITestGroup',
            'content_types': 'ransomware_group'
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)

    def test_search_api_with_pagination(self):
        """
        测试搜索API分页
        """
        response = self.client.get(self.search_url, {
            'q': 'APITestGroup',
            'page': 1,
            'page_size': 10
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['pagination']['page'], 1)
        self.assertEqual(response.data['pagination']['page_size'], 10)

    def test_popular_searches_api(self):
        """
        测试热门搜索API
        """
        response = self.client.get(self.popular_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)

    def test_search_suggestions_api(self):
        """
        测试搜索建议API
        """
        response = self.client.get(self.suggestions_url, {'q': 'test'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)


class SearchCacheTest(TestCase):
    """
    搜索缓存测试
    """

    def setUp(self):
        """
        设置测试
        """
        self.cache_manager = SearchCacheManager()

    def test_cache_search_results(self):
        """
        测试缓存搜索结果
        """
        test_results = {
            'query': 'test',
            'results': [],
            'pagination': {'total_count': 0}
        }

        # 设置缓存
        self.cache_manager.set_search_results(
            'test', None, 1, 20, test_results
        )

        # 获取缓存
        cached_results = self.cache_manager.get_search_results(
            'test', None, 1, 20
        )

        self.assertIsNotNone(cached_results)
        self.assertEqual(cached_results['query'], 'test')

    def test_cache_popular_searches(self):
        """
        测试缓存热门搜索
        """
        test_popular = [
            {'query': 'popular1', 'search_count': 10},
            {'query': 'popular2', 'search_count': 5}
        ]

        # 设置缓存
        self.cache_manager.set_popular_searches(test_popular)

        # 获取缓存
        cached_popular = self.cache_manager.get_popular_searches()

        self.assertIsNotNone(cached_popular)
        self.assertEqual(len(cached_popular), 2)

    @patch('apps.search.cache.cache')
    def test_cache_disabled(self, mock_cache):
        """
        测试缓存禁用时的行为
        """
        cache_manager = SearchCacheManager()
        cache_manager.enabled = False

        # 尝试设置缓存
        cache_manager.set_search_results('test', None, 1, 20, {})

        # 缓存不应该被调用
        mock_cache.set.assert_not_called()

        # 尝试获取缓存
        result = cache_manager.get_search_results('test', None, 1, 20)

        # 应该返回None
        self.assertIsNone(result)
