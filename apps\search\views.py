"""
搜索相关视图
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

from .services import UnifiedSearchService
from .serializers import SearchRequestSerializer, SearchResponseSerializer


@extend_schema(
    summary="统一全文搜索",
    description="在威胁情报数据中心的所有核心数据中进行全文搜索",
    parameters=[
        OpenApiParameter(
            name='q',
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            required=True,
            description='搜索关键词'
        ),
        OpenApiParameter(
            name='content_types',
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            required=False,
            description='要搜索的内容类型，多个类型用逗号分隔。可选值: ransomware_group, negotiation_record, intel_post, tools, ransom_note, ioc_indicator, victim, blog_post'
        ),
        OpenApiParameter(
            name='page',
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            required=False,
            description='页码，默认为1'
        ),
        OpenApiParameter(
            name='page_size',
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            required=False,
            description='每页大小，默认为20，最大100'
        ),
    ],
    responses={
        200: SearchResponseSerializer,
        400: "请求参数错误",
    },
    tags=['搜索']
)
@api_view(['GET'])
@permission_classes([AllowAny])
def unified_search(request):
    """
    统一全文搜索接口

    支持在以下数据类型中搜索：
    - ransomware_group: 勒索组织
    - negotiation_record: 谈判记录
    - intel_post: 威胁情报
    - tools: 应急工具
    - ransom_note: 勒索信
    - ioc_indicator: IOC指标
    - victim: 受害者
    - blog_post: 安全博客
    """
    # 验证请求参数
    serializer = SearchRequestSerializer(data=request.query_params)
    if not serializer.is_valid():
        return Response(
            {'error': '请求参数错误', 'details': serializer.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    validated_data = serializer.validated_data

    # 确保 validated_data 不为空且包含必需的字段
    if not validated_data or not isinstance(validated_data, dict):
        return Response(
            {'error': '请求参数错误', 'details': '验证数据格式错误'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # 获取搜索参数，使用安全的方式访问
    query = validated_data.get('q')
    if not query:
        return Response(
            {'error': '请求参数错误', 'details': '缺少必需的搜索关键词'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # 执行搜索
    search_service = UnifiedSearchService()
    try:
        results = search_service.search(
            query=query,
            content_types=validated_data.get('content_types'),
            page=validated_data.get('page', 1),
            page_size=validated_data.get('page_size', 20),
            use_cache=True
        )

        return Response(results, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': '搜索服务异常', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="获取热门搜索词",
    description="获取最近的热门搜索关键词列表",
    parameters=[
        OpenApiParameter(
            name='limit',
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            required=False,
            description='返回数量限制，默认为10，最大50'
        ),
    ],
    responses={
        200: {
            'type': 'array',
            'items': {
                'type': 'object',
                'properties': {
                    'query': {'type': 'string', 'description': '搜索关键词'},
                    'search_count': {'type': 'integer', 'description': '搜索次数'},
                    'result_count': {'type': 'integer', 'description': '结果数量'},
                    'last_searched': {'type': 'string', 'format': 'date-time', 'description': '最后搜索时间'},
                }
            }
        }
    },
    tags=['搜索']
)
@api_view(['GET'])
@permission_classes([AllowAny])
def popular_searches(request):
    """
    获取热门搜索词接口
    """
    limit = request.query_params.get('limit', 10)
    try:
        limit = int(limit)
        if limit > 50:
            limit = 50
        elif limit < 1:
            limit = 10
    except (ValueError, TypeError):
        limit = 10

    search_service = UnifiedSearchService()
    try:
        popular_searches = search_service.get_popular_searches(limit=limit)
        return Response(popular_searches, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': '获取热门搜索词失败', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="搜索建议",
    description="根据输入的关键词提供搜索建议",
    parameters=[
        OpenApiParameter(
            name='q',
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            required=True,
            description='搜索关键词前缀'
        ),
        OpenApiParameter(
            name='limit',
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            required=False,
            description='返回建议数量，默认为5，最大20'
        ),
    ],
    responses={
        200: {
            'type': 'array',
            'items': {
                'type': 'object',
                'properties': {
                    'suggestion': {'type': 'string', 'description': '建议的搜索词'},
                    'type': {'type': 'string', 'description': '建议类型'},
                    'count': {'type': 'integer', 'description': '相关结果数量'},
                }
            }
        }
    },
    tags=['搜索']
)
@api_view(['GET'])
@permission_classes([AllowAny])
def search_suggestions(request):
    """
    搜索建议接口
    """
    query = request.query_params.get('q', '').strip()
    if not query:
        return Response([], status=status.HTTP_200_OK)

    limit = request.query_params.get('limit', 5)
    try:
        limit = int(limit)
        if limit > 20:
            limit = 20
        elif limit < 1:
            limit = 5
    except (ValueError, TypeError):
        limit = 5

    # 这里可以实现更复杂的搜索建议逻辑
    # 目前返回基于历史搜索的简单建议
    from .models import SearchStatistics

    try:
        suggestions = []

        # 基于历史搜索的建议
        similar_searches = SearchStatistics.objects.filter(
            query__icontains=query
        ).order_by('-search_count')[:limit]

        for search_stat in similar_searches:
            suggestions.append({
                'suggestion': search_stat.query,
                'type': 'history',
                'count': search_stat.result_count
            })

        return Response(suggestions, status=status.HTTP_200_OK)

    except Exception as e:
        return Response(
            {'error': '获取搜索建议失败', 'details': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
