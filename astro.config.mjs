// @ts-check
import { defineConfig } from 'astro/config';
import node from '@astrojs/node';
import path from 'path';

import tailwindcss from '@tailwindcss/vite';
import { viteMockServe } from 'vite-plugin-mock';

import vue from '@astrojs/vue';
import react from '@astrojs/react';

// https://astro.build/config
export default defineConfig({
  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),
  vite: {
    resolve: {
      alias: {
        '@': path.resolve('./src')
      }
    },
    plugins: [
      tailwindcss(),
      viteMockServe({
        mockPath: 'mock',
        enable: true,
        logger: true,
        // @ts-ignore
        supportTs: true,
      })
    ]
  },

  integrations: [vue(), react()]
});
