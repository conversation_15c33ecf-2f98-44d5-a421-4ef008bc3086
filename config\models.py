"""
项目通用模型基类
"""
from django.db import models
import uuid


class BaseModel(models.Model):
    """
    抽象基础模型，提供通用字段
    所有业务模型都应该继承此基类
    """
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="创建时间"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="更新时间"
    )

    class Meta:
        abstract = True
        # 为所有继承的模型提供默认排序
        ordering = ['-created_at']
