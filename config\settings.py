"""
Django settings for config project.

通过 'django-admin startproject' 使用 Django 5.2.3 生成的配置文件。

有关此文件的更多信息，请参见
https://docs.djangoproject.com/en/5.2/topics/settings/

有关设置及其值的完整列表，请参见
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
from datetime import timedelta
from dotenv import load_dotenv

# 加载环境变量文件
load_dotenv()

# 构建项目路径：BASE_DIR / '子目录'
BASE_DIR = Path(__file__).resolve().parent.parent


# 快速开发配置 - 不适合生产环境
# 参见 https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# 安全警告：生产环境中请妥善保管密钥！
SECRET_KEY = os.getenv('SECRET_KEY', 'django-insecure-yk+%r34mk__m#^))_8qp!g!svu-d-2#&61x4@tr&4sx3wj8$oi')

# 安全警告：生产环境中不要开启调试模式！
DEBUG = os.getenv('DEBUG', 'True').lower() in ('true', '1', 'yes')

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1,testserver').split(',')

# 自定义用户模型
AUTH_USER_MODEL = 'authentication.User'


# 应用定义

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 第三方应用
    'rest_framework',  # Django REST Framework
    'rest_framework_simplejwt',  # JWT认证
    'drf_spectacular',  # API文档生成
    'django_filters',  # Django过滤器
    'jsoneditor',
    'mdeditor',  # Markdown编辑器
    'corsheaders',  # CORS跨域支持
    'django5_aliyun_oss',  # 阿里云OSS存储
    # 自定义应用
    'apps.authentication', # 用户认证应用
    'apps.group', # 勒索组织应用
    'apps.blog', # 博客应用
    'apps.intell', # 情报文章应用
    'apps.search', # 全文搜索应用
]

# 中间件配置
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # CORS中间件，必须放在最前面
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# 根URL配置
ROOT_URLCONF = 'config.urls'

# 模板配置
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# WSGI应用配置
WSGI_APPLICATION = 'config.wsgi.application'


# 数据库配置
# 参见 https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'threat_intelligence_db'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'password'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
    }
}


# 密码验证配置
# 参见 https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# 国际化配置
# 参见 https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

# 时区配置
TIME_ZONE = 'Asia/Shanghai'

#  是否使用国际化
USE_I18N = True

# 是否使用时区
USE_TZ = False


# 静态文件配置 (CSS, JavaScript, Images)
# 参见 https://docs.djangoproject.com/en/5.2/howto/static-files/
STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# 静态文件目录
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# 媒体文件配置
MEDIA_URL = 'media/'
MEDIA_ROOT = BASE_DIR / 'media'

# 默认主键字段类型
# 参见 https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Django REST Framework 配置
REST_FRAMEWORK = {
    # 默认权限类 - 开发阶段允许所有访问，生产环境需要修改
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',  # 允许任何人访问（开发时使用）
    ],

    # 默认认证类
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',  # JWT认证
        'rest_framework.authentication.SessionAuthentication',  # Session认证
    ],

    # 默认渲染器
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',         # JSON渲染器
        'rest_framework.renderers.BrowsableAPIRenderer', # 可浏览的API界面（调试用）
    ],

    # API文档生成配置
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',

    # 分页配置
    'DEFAULT_PAGINATION_CLASS': 'utils.pagination.StandardPageNumberPagination',
    'PAGE_SIZE': 20,  # 每页显示20条记录

    # 时间格式配置
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S',  # 日期时间格式
    'DATE_FORMAT': '%Y-%m-%d',               # 日期格式

    # 异常处理配置
    'EXCEPTION_HANDLER': 'utils.exceptions.custom_exception_handler',
}

# Simple JWT 配置
SIMPLE_JWT = {
    # Token 有效期（自定义配置）
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),  # 访问token有效期24小时
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),   # 刷新token有效期7天

    # Token 刷新配置
    'ROTATE_REFRESH_TOKENS': True,  # 刷新时生成新的refresh token
    'BLACKLIST_AFTER_ROTATION': True,  # 旧的refresh token加入黑名单
}

# drf-spectacular 配置
SPECTACULAR_SETTINGS = {
    'TITLE': '威胁情报数据中心 API',
    'DESCRIPTION': '威胁情报数据中心的 RESTful API 文档',
    'VERSION': '1.0.0',
    'SCHEMA_PATH_PREFIX': '/api/v1/',

    # API分组和标签配置
    'TAGS': [
        {
            'name': '用户认证',
            'description': '用户注册、登录、密码管理等认证相关的API接口'
        },
        {
            'name': '勒索组织管理',
            'description': '勒索软件组织相关的API接口，包括组织信息查询、详情获取等功能'
        },
        {
            'name': '博客管理',
            'description': '博客相关的API接口，包括文章、分类、标签的列表、详情、搜索等功能'
        },
    ],

    # 认证配置
    'COMPONENT_SPLIT_REQUEST': True,
    'SERVE_AUTHENTICATION': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
}

# 点击劫持保护
X_FRAME_OPTIONS = 'SAMEORIGIN'

# MDEditor 配置
MDEDITOR_CONFIGS = {
    'default':{
        'width': '90% ',  # Custom edit box width
        'height': 500,  # Custom edit box height
        'toolbar': ["undo", "redo", "|",
                    "bold", "del", "italic", "quote", "ucwords", "uppercase", "lowercase", "|",
                    "h1", "h2", "h3", "h5", "h6", "|",
                    "list-ul", "list-ol", "hr", "|",
                    "link", "reference-link", "image", "code", "preformatted-text", "code-block", "table", "datetime",
                    "emoji", "html-entities", "pagebreak", "goto-line", "|",
                    "help", "info",
                    "||", "preview", "watch", "fullscreen"],  # custom edit box toolbar
        'upload_image_formats': ["jpg", "jpeg", "gif", "png", "bmp", "webp"],  # image upload format type
        'image_folder': 'editor',  # image save the folder name
        'theme': 'default',  # edit box theme, dark / default
        'preview_theme': 'default',  # Preview area theme, dark / default
        'editor_theme': 'default',  # edit area theme, pastel-on-dark / default
        'toolbar_autofixed': True,  # Whether the toolbar capitals
        'search_replace': True,  # Whether to open the search for replacement
        'emoji': True,  # whether to open the expression function
        'tex': True,  # whether to open the tex chart function
        'flow_chart': True,  # whether to open the flow chart function
        'sequence': True, # Whether to open the sequence diagram function
        'watch': True,  # Live preview
        'lineWrapping': False,  # lineWrapping
        'lineNumbers': False,  # lineNumbers
        'language': 'zh',  # zh / en / es
        # 自定义上传URL（指向我们的自定义视图）
        'upload_image_url': '/mdeditor/upload/',  # 自定义图片上传URL
    }
}


# CORS 配置
CORS_ALLOWED_ORIGINS = os.getenv(
    'CORS_ALLOWED_ORIGINS',
    'http://localhost:4321,http://127.0.0.1:4321,http://localhost:3000,http://127.0.0.1:3000'
).split(',')

# 开发环境下允许所有来源（生产环境请删除）
if DEBUG:
    CORS_ALLOW_ALL_ORIGINS = True

# CORS 其他配置
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# CORS 允许的HTTP方法
CORS_ALLOWED_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# 预检请求的最大缓存时间
CORS_PREFLIGHT_MAX_AGE = 86400

# CSRF 配置
CSRF_TRUSTED_ORIGINS = os.getenv(
    'CSRF_TRUSTED_ORIGINS',
    'https://threat-admin.solarsecurity.cn,http://localhost:4321,http://127.0.0.1:4321,http://localhost:3000,http://127.0.0.1:3000'
).split(',')

# 微信登录配置
WECHAT_APP_ID = os.getenv('WECHAT_APP_ID', '')
WECHAT_APP_SECRET = os.getenv('WECHAT_APP_SECRET', '')

# 阿里云短信服务配置
ALIYUN_ACCESS_KEY_ID = os.getenv('ALIYUN_ACCESS_KEY_ID', '')
ALIYUN_ACCESS_KEY_SECRET = os.getenv('ALIYUN_ACCESS_KEY_SECRET', '')
ALIYUN_SMS_SIGN_NAME = os.getenv('ALIYUN_SMS_SIGN_NAME', '威胁情报数据中心')
ALIYUN_SMS_TEMPLATE_REGISTER = os.getenv('ALIYUN_SMS_TEMPLATE_REGISTER', 'SMS_123456789')
ALIYUN_SMS_TEMPLATE_LOGIN = os.getenv('ALIYUN_SMS_TEMPLATE_LOGIN', 'SMS_123456789')
ALIYUN_SMS_TEMPLATE_RESET = os.getenv('ALIYUN_SMS_TEMPLATE_RESET', 'SMS_123456789')

ALIYUN_OSS = {
    'ACCESS_KEY_ID': os.getenv('ALIYUN_OSS_ACCESS_KEY_ID', ''),
    'ACCESS_KEY_SECRET': os.getenv('ALIYUN_OSS_ACCESS_KEY_SECRET', ''),
    'ENDPOINT': os.getenv('ALIYUN_OSS_ENDPOINT', ''),
    'BUCKET_NAME': os.getenv('ALIYUN_OSS_BUCKET_NAME', ''),
    'URL_EXPIRE_SECONDS': 3600,  # 可选，默认为3600
}

# 设置为默认存储器
STORAGES = {
    'default': {
        'BACKEND': 'django5_aliyun_oss.storage.AliyunOSSStorage',
    },
    'staticfiles': {
        'BACKEND': 'django.contrib.staticfiles.storage.StaticFilesStorage',
    }
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
            'encoding': 'utf-8',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps.authentication': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
