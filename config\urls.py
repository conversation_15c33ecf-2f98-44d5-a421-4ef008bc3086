"""
URL 配置项用于 config 项目。

`urlpatterns` 列表将 URL 路径映射到视图函数。更多信息请参见:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
示例:
函数视图
    1. 添加导入:  from my_app import views
    2. 将 URL 添加到 urlpatterns:  path('', views.home, name='home')
基于类的视图
    1. 添加导入:  from other_app.views import Home
    2. 将 URL 添加到 urlpatterns:  path('', Home.as_view(), name='home')
包含其他 URL 配置
    1. 导入 include() 函数: from django.urls import include, path
    2. 将 URL 添加到 urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView

urlpatterns = [
    # Django管理后台
    path('admin/', admin.site.urls),

    # 自定义MDEditor 路由（优先级高于默认路由）
    path('mdeditor/', include('utils.mdeditor_urls')),
    # MDEditor 默认路由（作为备用）
    path('mdeditor/', include('mdeditor.urls')),

    # Django REST Framework 可浏览API界面（仅在调试模式下启用）
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),

    # API文档路由
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

    # API路由
    path('api/v1/', include([
        # 用户认证相关API
        path('auth/', include('apps.authentication.urls')),
        # 勒索组织相关API
        path('', include('apps.group.urls')),
        # 博客相关API
        path('blog/', include('apps.blog.urls')),
        # 威胁情报相关API
        path('intell/', include('apps.intell.urls')),
        # 全文搜索相关API
        path('search/', include('apps.search.urls')),
    ])),
]

# 开发环境下的静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)