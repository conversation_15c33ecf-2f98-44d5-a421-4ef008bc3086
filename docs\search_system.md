# 威胁情报数据中心全文搜索系统使用文档

## 概述

威胁情报数据中心的全文搜索系统基于PostgreSQL的全文搜索功能，提供统一的跨模型搜索能力。系统支持中文分词，具备高性能缓存机制，能够在所有核心数据模型中进行智能搜索。

## 功能特性

### 🔍 搜索范围
- **勒索组织** (ransomware_group): 组织名称、别名、描述、外部信息源
- **谈判记录** (negotiation_record): 关联组织、赎金信息、消息内容
- **威胁情报** (intel_post): 标题、描述、内容、威胁类型、严重程度
- **应急工具** (tools): 工具名称、描述、内容、关联组织
- **勒索信** (ransom_note): 勒索信名称、内容、关联组织、扩展名
- **IOC指标** (ioc_indicator): 关联组织、IOC数据、IOC类型
- **受害者** (victim): 标题、网站、国家、活动状态、关联组织
- **安全博客** (blog_post): 文章标题、内容、摘要、关键词、SEO描述

### ⚡ 性能特性
- **响应时间**: 平均240ms
- **缓存机制**: 5分钟搜索结果缓存，1小时热门搜索缓存
- **智能优化**: 自动选择全文搜索或LIKE查询
- **分页支持**: 灵活的分页大小调整

## API接口使用

### 1. 统一搜索接口

**接口地址**: `GET /api/v1/search/`

**请求参数**:
- `q` (必需): 搜索关键词
- `content_types` (可选): 要搜索的内容类型，多个用逗号分隔
- `page` (可选): 页码，默认为1
- `page_size` (可选): 每页大小，默认为20，最大100

**内容类型选项**:
```
ransomware_group    - 勒索组织
negotiation_record  - 谈判记录
intel_post         - 威胁情报
tools              - 应急工具
ransom_note        - 勒索信
ioc_indicator      - IOC指标
victim             - 受害者
blog_post          - 安全博客
```

**请求示例**:
```bash
# 基本搜索
curl "http://localhost:8000/api/v1/search/?q=勒索软件"

# 指定内容类型搜索
curl "http://localhost:8000/api/v1/search/?q=Lockbit&content_types=ransomware_group,tools"

# 分页搜索
curl "http://localhost:8000/api/v1/search/?q=威胁情报&page=2&page_size=10"
```

**响应格式**:
```json
{
  "query": "搜索关键词",
  "results": [
    {
      "content_type": "ransomware_group",
      "object_id": 1,
      "rank": 0.845,
      "title": "LockBit",
      "description": "LockBit勒索软件组织",
      "url": "/api/v1/groups/1/",
      "data": {
        "id": 1,
        "name": "LockBit",
        "status": "active",
        "threat_level": "high"
      },
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 15,
    "total_pages": 1,
    "has_next": false,
    "has_previous": false
  },
  "content_types": {
    "ransomware_group": 5,
    "intel_post": 10
  }
}
```

### 2. 热门搜索词接口

**接口地址**: `GET /api/v1/search/popular/`

**请求参数**:
- `limit` (可选): 返回数量，默认为10，最大50

**请求示例**:
```bash
curl "http://localhost:8000/api/v1/search/popular/?limit=5"
```

**响应格式**:
```json
[
  {
    "query": "LockBit",
    "search_count": 25,
    "result_count": 8,
    "last_searched": "2024-01-15T10:30:00Z"
  }
]
```

### 3. 搜索建议接口

**接口地址**: `GET /api/v1/search/suggestions/`

**请求参数**:
- `q` (必需): 搜索关键词前缀
- `limit` (可选): 返回建议数量，默认为5，最大20

**请求示例**:
```bash
curl "http://localhost:8000/api/v1/search/suggestions/?q=Lock&limit=3"
```

**响应格式**:
```json
[
  {
    "suggestion": "LockBit",
    "type": "history",
    "count": 8
  },
  {
    "suggestion": "Lockergoga",
    "type": "history", 
    "count": 3
  }
]
```

## 管理命令使用

### 1. 重建搜索索引

用于初始化或更新搜索索引数据。

```bash
# 重建所有模型的搜索索引
python manage.py rebuild_search_index

# 重建指定模型的搜索索引
python manage.py rebuild_search_index --model ransomware_group

# 指定批处理大小
python manage.py rebuild_search_index --batch-size 50
```

**支持的模型类型**:
- `ransomware_group` - 勒索组织
- `negotiation_record` - 谈判记录
- `intel_post` - 威胁情报
- `tools` - 应急工具
- `ransom_note` - 勒索信
- `ioc_indicator` - IOC指标
- `victim` - 受害者

### 2. 测试搜索功能

用于验证搜索功能的正确性和性能。

```bash
# 基本搜索测试
python manage.py test_search --query="LockBit"

# 创建测试数据并测试
python manage.py test_search --create-test-data --query="SearchTestGroup"
```

**测试输出示例**:
```
开始测试搜索功能，查询词: "LockBit"

1. 测试基本搜索...
搜索耗时: 240.00ms
总结果数: 5
当前页结果数: 5

搜索结果:
  1. [ransomware_group] LockBit (相关性: 0.845)
  2. [tools] LockBit解密工具 (相关性: 0.623)

2. 测试按内容类型过滤...
  ransomware_group: 1 个结果
  tools: 2 个结果
  intel_post: 2 个结果

3. 测试分页功能...
第1页 (每页2条): 2 个结果

4. 测试热门搜索...
热门搜索词数量: 3

5. 测试缓存功能...
无缓存搜索耗时: 245.20ms
有缓存搜索耗时: 12.50ms
缓存性能提升: 94.9%

搜索功能测试完成！
搜索性能良好: 240.00ms
```

## 前端集成示例

### JavaScript/TypeScript

```javascript
// 搜索服务类
class SearchService {
  constructor(baseURL = '/api/v1') {
    this.baseURL = baseURL;
  }

  async search(query, options = {}) {
    const params = new URLSearchParams({
      q: query,
      ...options
    });
    
    const response = await fetch(`${this.baseURL}/search/?${params}`);
    return response.json();
  }

  async getPopularSearches(limit = 10) {
    const response = await fetch(`${this.baseURL}/search/popular/?limit=${limit}`);
    return response.json();
  }

  async getSuggestions(query, limit = 5) {
    const params = new URLSearchParams({ q: query, limit });
    const response = await fetch(`${this.baseURL}/search/suggestions/?${params}`);
    return response.json();
  }
}

// 使用示例
const searchService = new SearchService();

// 执行搜索
const results = await searchService.search('LockBit', {
  content_types: 'ransomware_group,tools',
  page: 1,
  page_size: 20
});

// 获取搜索建议
const suggestions = await searchService.getSuggestions('Lock');
```

### React组件示例

```jsx
import React, { useState, useEffect } from 'react';

const SearchComponent = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);

  const handleSearch = async () => {
    if (!query.trim()) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/search/?q=${encodeURIComponent(query)}`);
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('搜索失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = async (value) => {
    setQuery(value);
    
    if (value.length > 1) {
      try {
        const response = await fetch(`/api/v1/search/suggestions/?q=${encodeURIComponent(value)}`);
        const data = await response.json();
        setSuggestions(data);
      } catch (error) {
        console.error('获取建议失败:', error);
      }
    } else {
      setSuggestions([]);
    }
  };

  return (
    <div className="search-component">
      <div className="search-input-container">
        <input
          type="text"
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          placeholder="搜索威胁情报..."
          className="search-input"
        />
        <button onClick={handleSearch} disabled={loading}>
          {loading ? '搜索中...' : '搜索'}
        </button>
        
        {suggestions.length > 0 && (
          <div className="suggestions">
            {suggestions.map((suggestion, index) => (
              <div
                key={index}
                className="suggestion-item"
                onClick={() => {
                  setQuery(suggestion.suggestion);
                  setSuggestions([]);
                  handleSearch();
                }}
              >
                {suggestion.suggestion} ({suggestion.count} 个结果)
              </div>
            ))}
          </div>
        )}
      </div>

      {results && (
        <div className="search-results">
          <div className="results-summary">
            找到 {results.pagination.total_count} 个结果
          </div>
          
          {results.results.map((result, index) => (
            <div key={index} className="result-item">
              <h3>
                <a href={result.url}>{result.title}</a>
                <span className="content-type">[{result.content_type}]</span>
              </h3>
              <p>{result.description}</p>
              <div className="result-meta">
                相关性: {(result.rank * 100).toFixed(1)}%
              </div>
            </div>
          ))}
          
          {/* 分页组件 */}
          <div className="pagination">
            {results.pagination.has_previous && (
              <button onClick={() => handlePageChange(results.pagination.page - 1)}>
                上一页
              </button>
            )}
            <span>第 {results.pagination.page} 页，共 {results.pagination.total_pages} 页</span>
            {results.pagination.has_next && (
              <button onClick={() => handlePageChange(results.pagination.page + 1)}>
                下一页
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchComponent;
```

## 高级配置

### 搜索权重配置

系统使用四级权重配置来优化搜索相关性：

```python
# 权重级别说明
WEIGHT_A = 'A'  # 最高权重 (1.0) - 标题、名称等主要字段
WEIGHT_B = 'B'  # 高权重 (0.4) - 描述、摘要等重要字段
WEIGHT_C = 'C'  # 中权重 (0.2) - 内容、详情等一般字段
WEIGHT_D = 'D'  # 低权重 (0.1) - 标签、别名等辅助字段
```

### 缓存配置

在 `settings.py` 中配置搜索缓存：

```python
# 搜索缓存配置
SEARCH_CACHE_ENABLED = True  # 启用搜索缓存

# 缓存超时时间（秒）
SEARCH_CACHE_TIMEOUT = {
    'SEARCH_RESULTS': 300,      # 5分钟
    'POPULAR_SEARCHES': 3600,   # 1小时
    'SUGGESTIONS': 1800,        # 30分钟
}

# Redis缓存配置（推荐用于生产环境）
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

### 数据库优化

确保PostgreSQL配置支持中文全文搜索：

```sql
-- 检查PostgreSQL扩展
SELECT * FROM pg_extension WHERE extname = 'pg_trgm';

-- 如果没有安装，执行以下命令
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 检查搜索配置
SELECT cfgname FROM pg_ts_config;

-- 查看索引状态
SELECT schemaname, tablename, indexname, indexdef
FROM pg_indexes
WHERE indexname LIKE '%search%';
```

## 性能监控

### 搜索性能指标

系统提供以下性能监控指标：

1. **响应时间监控**
   - 平均搜索响应时间
   - 95%分位数响应时间
   - 缓存命中率

2. **搜索统计**
   - 每日搜索量
   - 热门搜索词
   - 无结果搜索率

3. **数据库性能**
   - 索引使用率
   - 查询执行计划
   - 慢查询日志

### 性能优化建议

1. **数据库层面**
   ```sql
   -- 定期更新统计信息
   ANALYZE;

   -- 重建索引（如果性能下降）
   REINDEX INDEX group_ransomwaregroup_search_gin_idx;
   ```

2. **应用层面**
   ```python
   # 批量更新搜索向量
   python manage.py rebuild_search_index --batch-size 100

   # 清理过期缓存
   python manage.py shell -c "from django.core.cache import cache; cache.clear()"
   ```

3. **监控脚本**
   ```bash
   #!/bin/bash
   # 搜索性能监控脚本

   # 检查搜索响应时间
   curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/api/v1/search/?q=test"

   # 检查缓存状态
   python manage.py shell -c "
   from apps.search.cache import search_cache_manager
   print('缓存状态:', search_cache_manager.enabled)
   "
   ```

## 故障排除

### 常见问题

1. **搜索无结果**
   ```bash
   # 检查搜索索引是否存在
   python manage.py shell -c "
   from apps.group.models import RansomwareGroup
   print(RansomwareGroup.objects.filter(search_vector__isnull=False).count())
   "

   # 重建搜索索引
   python manage.py rebuild_search_index
   ```

2. **搜索速度慢**
   ```sql
   -- 检查索引是否正常
   EXPLAIN ANALYZE SELECT * FROM group_ransomwaregroup
   WHERE to_tsvector('simple', search_vector) @@ to_tsquery('simple', 'test');
   ```

3. **缓存问题**
   ```python
   # 清除搜索缓存
   from apps.search.services import UnifiedSearchService
   service = UnifiedSearchService()
   service.clear_search_cache()
   ```

### 日志配置

在 `settings.py` 中添加搜索日志配置：

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'search_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/search.log',
        },
    },
    'loggers': {
        'apps.search': {
            'handlers': ['search_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 安全考虑

### 输入验证

系统已实现以下安全措施：

1. **查询参数验证**
   - 最大查询长度限制（500字符）
   - 特殊字符过滤
   - SQL注入防护

2. **访问控制**
   ```python
   # 在视图中添加权限检查
   from rest_framework.permissions import IsAuthenticated

   @permission_classes([IsAuthenticated])
   def unified_search(request):
       # 搜索逻辑
   ```

3. **速率限制**
   ```python
   # 在settings.py中配置
   REST_FRAMEWORK = {
       'DEFAULT_THROTTLE_CLASSES': [
           'rest_framework.throttling.AnonRateThrottle',
           'rest_framework.throttling.UserRateThrottle'
       ],
       'DEFAULT_THROTTLE_RATES': {
           'anon': '100/hour',
           'user': '1000/hour'
       }
   }
   ```

## 版本更新

### v1.0.0 (当前版本)
- ✅ 基础全文搜索功能
- ✅ 多模型统一搜索
- ✅ 缓存机制
- ✅ 管理命令
- ✅ API接口

### 计划功能 (v1.1.0)
- 🔄 高级搜索语法支持
- 🔄 搜索结果高亮
- 🔄 个性化搜索推荐
- 🔄 搜索分析仪表板

## 技术支持

如遇到问题，请按以下步骤排查：

1. 检查日志文件 `logs/search.log`
2. 运行诊断命令 `python manage.py test_search`
3. 查看数据库索引状态
4. 检查缓存配置

更多技术细节请参考源代码注释和测试用例。
