# Mock API 配置说明

本项目使用 `vite-plugin-mock` 插件来提供模拟API数据，用于前端开发和测试。

## 配置文件结构

```
mock/
├── README.md              # 本说明文件
├── index.ts              # Mock配置入口文件
├── threat-intelligence.ts # 威胁情报相关API
├── vulnerability.ts      # 漏洞管理相关API
├── security-events.ts    # 安全事件相关API
└── dashboard.ts          # 仪表板相关API
```

## 已配置的API端点

### 系统API
- `GET /api/health` - 系统健康检查

### 认证API
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户退出
- `GET /api/auth/profile` - 获取用户信息

### 威胁情报API
- `GET /api/threat-intelligence` - 获取威胁情报列表
- `GET /api/threat-intelligence/:id` - 获取威胁情报详情
- `POST /api/threat-intelligence` - 创建威胁情报
- `PUT /api/threat-intelligence/:id` - 更新威胁情报
- `DELETE /api/threat-intelligence/:id` - 删除威胁情报
- `GET /api/threat-intelligence/stats` - 获取威胁情报统计数据

### 漏洞管理API
- `GET /api/vulnerabilities` - 获取漏洞列表
- `GET /api/vulnerabilities/:id` - 获取漏洞详情
- `GET /api/vulnerabilities/stats` - 获取漏洞统计数据

### 安全事件API
- `GET /api/security-events` - 获取安全事件列表
- `GET /api/security-events/:id` - 获取安全事件详情
- `PUT /api/security-events/:id/status` - 更新事件状态
- `GET /api/security-events/stats` - 获取安全事件统计数据

### 仪表板API
- `GET /api/dashboard/overview` - 获取仪表板概览数据
- `GET /api/dashboard/threat-trends` - 获取威胁趋势数据
- `GET /api/dashboard/vulnerability-trends` - 获取漏洞趋势数据
- `GET /api/dashboard/security-event-trends` - 获取安全事件趋势数据
- `GET /api/dashboard/system-performance` - 获取系统性能数据

## 使用方法

### 1. 启动开发服务器
```bash
pnpm dev
```

### 2. 测试API
访问 `http://localhost:4322/test-api` 来测试所有mock API端点。

### 3. 在代码中使用
```typescript
import { threatIntelligenceApi } from '@/lib/api';

// 获取威胁情报列表
const response = await threatIntelligenceApi.getList();
console.log(response.data);
```

## Mock数据特点

### 数据生成
- 使用 `mockjs` 库生成随机但真实的数据
- 支持中文内容生成
- 数据结构符合威胁情报数据中心的业务需求

### 响应格式
所有API响应都遵循统一格式：
```typescript
{
  code: number;     // 状态码，200表示成功
  message: string;  // 响应消息
  data: any;        // 实际数据
}
```

### 分页数据格式
列表类API返回分页数据：
```typescript
{
  list: T[];        // 数据列表
  total: number;    // 总数量
  page: number;     // 当前页码
  pageSize: number; // 每页大小
}
```

## 自定义Mock数据

### 添加新的API端点
1. 在相应的mock文件中添加新的配置
2. 或者创建新的mock文件并在 `index.ts` 中导入

### 修改现有数据
直接编辑对应的mock文件，修改 `Mock.mock()` 的配置即可。

### Mock配置示例
```typescript
{
  url: '/api/example',
  method: 'get',
  response: () => {
    const data = Mock.mock({
      'list|10-20': [
        {
          'id|+1': 1,
          'name': '@cname',
          'date': '@datetime'
        }
      ]
    });
    
    return {
      code: 200,
      message: 'success',
      data: data
    };
  }
}
```

## 注意事项

1. **开发环境专用**: Mock配置仅在开发环境生效
2. **数据持久性**: Mock数据不会持久化，每次刷新都会重新生成
3. **类型安全**: 配合TypeScript类型定义使用，确保类型安全
4. **性能**: Mock数据生成可能有轻微延迟，模拟真实API调用

## 相关文件

- `astro.config.mjs` - Vite插件配置
- `src/types/api.ts` - API类型定义
- `src/lib/api.ts` - API调用工具函数
- `src/pages/test-api.astro` - API测试页面
