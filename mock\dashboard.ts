import type { MockMethod } from 'vite-plugin-mock';
import Mock from 'mockjs';

// 仪表板数据模拟
export default [
  // 获取仪表板概览数据
  {
    url: '/api/dashboard/overview',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'threat_intelligence': {
          'total': '@integer(1000, 5000)',
          'new_today': '@integer(10, 50)',
          'high_severity': '@integer(50, 200)',
          'active_campaigns': '@integer(5, 20)'
        },
        'vulnerabilities': {
          'total': '@integer(500, 2000)',
          'critical': '@integer(20, 100)',
          'unpatched': '@integer(100, 500)',
          'new_this_week': '@integer(15, 60)'
        },
        'security_events': {
          'total_today': '@integer(100, 500)',
          'critical_events': '@integer(5, 25)',
          'in_progress': '@integer(10, 50)',
          'resolved_today': '@integer(50, 200)'
        },
        'system_health': {
          'sensors_online': '@integer(80, 100)',
          'sensors_total': 100,
          'data_sources_active': '@integer(15, 20)',
          'data_sources_total': 20,
          'system_uptime': '@float(95, 100, 2, 2)'
        },
        'recent_alerts|5': [
          {
            'id|+1': 1,
            'title': '@ctitle(10, 25)',
            'type|1': ['威胁情报', '漏洞', '安全事件'],
            'severity|1': ['严重', '高', '中'],
            'time': '@datetime',
            'status|1': ['新', '处理中', '已处理']
          }
        ]
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取威胁趋势数据
  {
    url: '/api/dashboard/threat-trends',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'daily_threats|30': [
          {
            'date': '@date("MM-dd")',
            'count': '@integer(20, 100)',
            'severity': {
              '严重': '@integer(2, 10)',
              '高': '@integer(5, 20)',
              '中': '@integer(8, 30)',
              '低': '@integer(5, 40)'
            }
          }
        ],
        'threat_types|7': [
          {
            'type|1': ['恶意软件', '钓鱼攻击', 'DDoS攻击', '数据泄露', '勒索软件', 'APT攻击', '其他'],
            'count': '@integer(50, 300)',
            'trend|1': ['up', 'down', 'stable']
          }
        ],
        'geographic_distribution|10': [
          {
            'country|1': ['中国', '美国', '俄罗斯', '德国', '英国', '法国', '日本', '韩国', '印度', '巴西'],
            'threat_count': '@integer(10, 200)',
            'coordinates': ['@float(-180, 180, 2, 6)', '@float(-90, 90, 2, 6)']
          }
        ]
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取漏洞趋势数据
  {
    url: '/api/dashboard/vulnerability-trends',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'monthly_vulnerabilities|12': [
          {
            'month': '@date("yyyy-MM")',
            'discovered': '@integer(30, 120)',
            'patched': '@integer(20, 100)',
            'remaining': '@integer(10, 50)'
          }
        ],
        'cvss_distribution': {
          '9.0-10.0': '@integer(10, 50)',
          '7.0-8.9': '@integer(50, 150)',
          '4.0-6.9': '@integer(100, 300)',
          '0.1-3.9': '@integer(50, 200)'
        },
        'top_vendors|10': [
          {
            'vendor': '@company',
            'vulnerability_count': '@integer(20, 100)',
            'critical_count': '@integer(2, 20)'
          }
        ],
        'patch_status': {
          'patched': '@integer(300, 800)',
          'patch_available': '@integer(50, 200)',
          'no_patch': '@integer(20, 100)',
          'workaround_available': '@integer(30, 150)'
        }
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取安全事件趋势数据
  {
    url: '/api/dashboard/security-event-trends',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'hourly_events|24': [
          {
            'hour|+1': 0,
            'events': '@integer(10, 80)',
            'critical_events': '@integer(0, 10)'
          }
        ],
        'event_resolution_time': {
          'average_hours': '@float(2, 24, 1, 1)',
          'median_hours': '@float(1, 12, 1, 1)',
          'sla_compliance': '@float(85, 98, 1, 1)'
        },
        'top_attack_vectors|8': [
          {
            'vector|1': ['网络扫描', '恶意邮件', '恶意下载', 'SQL注入', 'XSS攻击', '暴力破解', '社会工程', '其他'],
            'count': '@integer(20, 200)',
            'percentage': '@float(5, 25, 1, 1)'
          }
        ],
        'analyst_workload|5': [
          {
            'analyst': '@cname',
            'assigned_events': '@integer(10, 50)',
            'resolved_events': '@integer(8, 45)',
            'avg_resolution_time': '@float(1, 8, 1, 1)'
          }
        ]
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取系统性能数据
  {
    url: '/api/dashboard/system-performance',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'data_ingestion': {
          'events_per_second': '@integer(100, 1000)',
          'data_volume_mb': '@integer(500, 2000)',
          'processing_latency_ms': '@integer(50, 500)'
        },
        'storage_usage': {
          'total_gb': '@integer(10000, 50000)',
          'used_gb': '@integer(5000, 30000)',
          'usage_percentage': '@float(50, 85, 1, 1)'
        },
        'api_performance': {
          'requests_per_minute': '@integer(500, 2000)',
          'average_response_time_ms': '@integer(100, 800)',
          'error_rate_percentage': '@float(0.1, 2, 1, 1)'
        },
        'resource_utilization': {
          'cpu_percentage': '@float(20, 80, 1, 1)',
          'memory_percentage': '@float(40, 85, 1, 1)',
          'disk_io_percentage': '@float(10, 60, 1, 1)',
          'network_io_mbps': '@integer(50, 500)'
        }
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  }
] as MockMethod[];
