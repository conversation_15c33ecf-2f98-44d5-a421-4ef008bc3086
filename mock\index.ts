import type { MockMethod } from 'vite-plugin-mock';

// 导入所有mock模块
import threatIntelligence from './threat-intelligence';
import vulnerability from './vulnerability';
import securityEvents from './security-events';
import dashboard from './dashboard';
import search from './search';
import ransomwareGroups from './ransomware-groups';

// 合并所有mock配置
export default [
  ...threatIntelligence,
  ...vulnerability,
  ...securityEvents,
  ...dashboard,
  ...search,
  ...ransomwareGroups,

  // 通用API
  {
    url: '/api/health',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'API服务正常',
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        }
      };
    }
  },

  // 用户认证相关
  {
    url: '/api/auth/login',
    method: 'post',
    response: ({ body }: { body: any }) => {
      // 简单的模拟登录验证
      if (body.username && body.password) {
        return {
          code: 200,
          message: '登录成功',
          data: {
            token: 'mock-jwt-token-' + Date.now(),
            user: {
              id: 1,
              username: body.username,
              name: '安全分析师',
              role: 'analyst',
              permissions: ['read', 'write', 'admin']
            }
          }
        };
      } else {
        return {
          code: 400,
          message: '用户名或密码不能为空'
        };
      }
    }
  },

  {
    url: '/api/auth/logout',
    method: 'post',
    response: () => {
      return {
        code: 200,
        message: '退出成功'
      };
    }
  },

  {
    url: '/api/auth/profile',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          id: 1,
          username: 'analyst',
          name: '安全分析师',
          email: '<EMAIL>',
          role: 'analyst',
          department: '网络安全部',
          last_login: new Date().toISOString(),
          permissions: ['read', 'write', 'admin']
        }
      };
    }
  }
] as MockMethod[];
