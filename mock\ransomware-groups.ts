import type { MockMethod } from 'vite-plugin-mock';
import Mock from 'mockjs';

// 勒索软件组织数据模拟
export default [
  // 获取勒索软件组织列表
  {
    url: '/api/ransomware-groups',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'list|15-25': [
          {
            'id|+1': 1,
            'name|1': ['LockBit', 'BlackCat', 'Cl0p', 'Conti', 'REvil', 'DarkSide', 'Maze', 'Ryuk', 'Sodinokibi', 'GandCrab', 'WannaCry', 'Petya', 'BadRabbit', 'SamSam', 'Dharma'],
            'slug': function() {
              // 使用箭头函数会导致this指向问题，所以使用常规函数
              // 通过显式类型断言告诉TypeScript this有name属性
              return (this as any).name.toLowerCase().replace(/\s+/g, '-');
            },
            'aliases|1-3': ['@word', '@word'],
            'status|1': ['活跃', '不活跃', '已解散', '监控中'],
            'threat_level|1': ['极高', '高', '中', '低'],
            'first_seen': '@date("2018-01-01")',
            'last_activity': '@date("2023-01-01")',
            'description': '@cparagraph(2, 4)',
            'detailed_analysis': '@cparagraph(5, 8)',
            'origin_country|1': ['俄罗斯', '中国', '朝鲜', '伊朗', '美国', '未知'],
            'target_sectors|2-5': ['金融', '医疗', '教育', '政府', '制造业', '能源', '零售', '科技'],
            'target_countries|3-8': ['美国', '英国', '德国', '法国', '日本', '韩国', '澳大利亚', '加拿大'],
            'attack_methods|2-4': ['钓鱼邮件', 'RDP暴力破解', '供应链攻击', '漏洞利用', '社会工程学'],
            'ransom_demand_range': {
              'min|10000-100000': 50000,
              'max|500000-10000000': 1000000,
              'currency': 'USD'
            },
            'encryption_methods|1-3': ['AES-256', 'RSA-2048', 'ChaCha20', 'Salsa20'],
            'payment_methods|1-2': ['比特币', '门罗币', '以太坊'],
            'communication_channels|1-3': ['Tor网站', 'Telegram', '暗网论坛', '邮件'],
            'known_affiliates|0-3': ['@cname', '@cname'],
            'attack_statistics': {
              'total_attacks|50-500': 200,
              'successful_attacks|20-200': 100,
              'total_victims|100-1000': 500,
              'estimated_revenue|1000000-50000000': 10000000
            },
            'recent_attacks|3-8': [
              {
                'id|+1': 1,
                'target': '@company',
                'date': '@date("2023-01-01")',
                'sector|1': ['金融', '医疗', '教育', '政府', '制造业'],
                'country|1': ['美国', '英国', '德国', '法国', '日本'],
                'ransom_amount|100000-5000000': 1000000,
                'status|1': ['成功', '失败', '未知']
              }
            ],
            'technical_details': {
              'file_extensions|2-5': ['.locked', '.encrypted', '.crypto', '.vault', '.secure'],
              'ransom_note_filenames|1-3': ['README.txt', 'DECRYPT_INSTRUCTIONS.txt', 'HOW_TO_DECRYPT.html'],
              'registry_modifications|2-4': ['HKEY_LOCAL_MACHINE\\SOFTWARE\\...', 'HKEY_CURRENT_USER\\SOFTWARE\\...'],
              'network_indicators|2-5': ['@ip', '@domain', '@url']
            },
            'mitigation_strategies|3-6': [
              '定期备份重要数据',
              '保持系统和软件更新',
              '使用端点检测和响应(EDR)解决方案',
              '实施网络分段',
              '员工安全意识培训',
              '部署多因素认证'
            ],
            'tags|2-5': ['勒索软件', '网络犯罪', 'APT', '数据加密'],
            'created_at': '@datetime',
            'updated_at': '@datetime',
            'analyst': '@cname'
          }
        ],
        total: '@integer(50, 100)',
        page: 1,
        pageSize: 20
      });

      // 为每个组织添加封面图片和logo
      data.list = data.list.map((group: any, index: number) => ({
        ...group,
        coverImage: [
          'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=250&fit=crop&auto=format'
        ][index % 8],
        logo: [
          'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100&h=100&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1614064641938-3bbee52942c7?w=100&h=100&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1618477247222-acbdb0e159b3?w=100&h=100&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1633265486064-086b219458ec?w=100&h=100&fit=crop&auto=format'
        ][index % 4]
      }));

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取勒索软件组织详情
  {
    url: '/api/ransomware-groups/:slug',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const data = Mock.mock({
        'id': '@integer(1, 100)',
        'name': query.slug.charAt(0).toUpperCase() + query.slug.slice(1),
        'slug': query.slug,
        'aliases|2-4': ['@word', '@word', '@word'],
        'status|1': ['活跃', '不活跃', '已解散', '监控中'],
        'threat_level|1': ['极高', '高', '中', '低'],
        'first_seen': '@date("2018-01-01")',
        'last_activity': '@date("2023-01-01")',
        'description': '@cparagraph(3, 5)',
        'detailed_analysis': '@cparagraph(8, 12)',
        'origin_country|1': ['俄罗斯', '中国', '朝鲜', '伊朗', '美国', '未知'],
        'target_sectors|4-8': ['金融', '医疗', '教育', '政府', '制造业', '能源', '零售', '科技'],
        'target_countries|5-10': ['美国', '英国', '德国', '法国', '日本', '韩国', '澳大利亚', '加拿大'],
        'attack_methods|3-6': ['钓鱼邮件', 'RDP暴力破解', '供应链攻击', '漏洞利用', '社会工程学', '零日漏洞'],
        'ransom_demand_range': {
          'min|50000-200000': 100000,
          'max|1000000-20000000': 5000000,
          'currency': 'USD'
        },
        'encryption_methods|2-4': ['AES-256', 'RSA-2048', 'ChaCha20', 'Salsa20'],
        'payment_methods|1-3': ['比特币', '门罗币', '以太坊'],
        'communication_channels|2-4': ['Tor网站', 'Telegram', '暗网论坛', '邮件'],
        'known_affiliates|1-4': ['@cname', '@cname', '@cname'],
        'attack_statistics': {
          'total_attacks|100-800': 400,
          'successful_attacks|50-400': 200,
          'total_victims|200-2000': 1000,
          'estimated_revenue|5000000-*********': 25000000
        },
        'recent_attacks|8-15': [
          {
            'id|+1': 1,
            'target': '@company',
            'date': '@date("2023-01-01")',
            'sector|1': ['金融', '医疗', '教育', '政府', '制造业', '能源'],
            'country|1': ['美国', '英国', '德国', '法国', '日本', '韩国'],
            'ransom_amount|200000-8000000': 2000000,
            'status|1': ['成功', '失败', '未知']
          }
        ],
        'technical_details': {
          'file_extensions|3-6': ['.locked', '.encrypted', '.crypto', '.vault', '.secure', '.crypt'],
          'ransom_note_filenames|2-4': ['README.txt', 'DECRYPT_INSTRUCTIONS.txt', 'HOW_TO_DECRYPT.html', 'RECOVERY_INSTRUCTIONS.txt'],
          'registry_modifications|3-6': [
            'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
            'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
            'HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services'
          ],
          'network_indicators|4-8': ['@ip', '@domain', '@url', '@ip']
        },
        'mitigation_strategies|5-10': [
          '定期备份重要数据并离线存储',
          '保持操作系统和软件更新',
          '使用端点检测和响应(EDR)解决方案',
          '实施网络分段和零信任架构',
          '员工安全意识培训和钓鱼模拟',
          '部署多因素认证',
          '限制管理员权限',
          '监控异常网络活动',
          '制定事件响应计划',
          '定期进行安全评估'
        ],
        'tags|3-7': ['勒索软件', '网络犯罪', 'APT', '数据加密', '勒索即服务'],
        'created_at': '@datetime',
        'updated_at': '@datetime',
        'analyst': '@cname',
        'coverImage': 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=800&h=400&fit=crop&auto=format',
        'logo': 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=150&h=150&fit=crop&auto=format',
        // 添加模拟IOC数据用于表格显示
        'mock_ioc_data': [
          {
            type: 'url',
            value: `https://${query.slug}-blog.onion/victims`,
            status: '活跃',
            first_seen: '2025-05-10',
            source: 'DarkWeb监控',
            description: '勒索软件组织官方网站'
          },
          {
            type: 'hash',
            value: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
            status: '不活跃',
            first_seen: '2025-05-10',
            source: '内部检测',
            description: '主要勒索软件可执行文件'
          },
          {
            type: 'ip',
            value: '***************',
            status: '活跃',
            first_seen: '2025-05-10',
            source: 'MISP',
            description: 'C&C服务器IP地址'
          },
          {
            type: 'domain',
            value: `${query.slug}supp.com`,
            status: '活跃',
            first_seen: '2025-05-10',
            source: 'VirusTotal',
            description: '恶意域名'
          },
          {
            type: 'url',
            value: `http://evil-${query.slug}.onion/pay`,
            status: '活跃',
            first_seen: '2025-05-08',
            source: 'DarkWeb监控',
            description: '支付页面URL'
          },
          {
            type: 'hash',
            value: 'da39a3ee5e6b4b0d3255bfef95601890afd80709',
            status: '不活跃',
            first_seen: '2025-05-07',
            source: '内部检测',
            description: '恶意加载器文件'
          },
          {
            type: 'ip',
            value: '*************',
            status: '活跃',
            first_seen: '2025-05-06',
            source: 'MISP',
            description: '内网横向移动目标'
          },
          {
            type: 'domain',
            value: `malicious-${query.slug}.onion`,
            status: '活跃',
            first_seen: '2025-05-05',
            source: 'VirusTotal',
            description: 'Tor隐藏服务域名'
          }
        ],
        // 添加谈判记录真实数据
        'negotiation_records': function() {
          const records = [
            {
              id: 1,
              case_id: 'NEG-2024-001',
              title: 'TechCorp制造企业勒索谈判',
              victim_organization: 'TechCorp Manufacturing Ltd.',
              victim_contact: '<EMAIL>',
              attacker_group: query.slug,
              status: '已完成',
              start_date: '2024-01-15T09:00:00Z',
              end_date: '2024-01-20T15:30:00Z',
              initial_demand: {
                amount: 2000000,
                currency: 'USD'
              },
              final_amount: {
                amount: 800000,
                currency: 'USD'
              },
              payment_deadline: '2024-01-25T23:59:59Z',
              communication_platform: 'Tor聊天室',
              total_messages: 9,
              last_activity: '2024-01-20T15:30:00Z',
              summary: '该案例涉及一家大型制造企业的勒索软件攻击。攻击者最初要求200万美元赎金，经过5天的谈判，最终以80万美元达成协议。受害者提供了部分支付证明，攻击者提供了解密工具。',
              outcome: '支付成功',
              messages: [
                {
                  id: 1,
                  sender: 'attacker',
                  sender_name: '操作员_Alpha',
                  content: '你好，TechCorp。我们已经加密了你们的所有关键系统。如果想要恢复数据，需要支付200万美元的比特币。',
                  message_type: 'demand',
                  timestamp: '2024-01-15T09:00:00Z',
                  status: 'read',
                  metadata: {
                    amount: 2000000,
                    currency: 'USD',
                    deadline: '2024-01-25T23:59:59Z'
                  }
                },
                {
                  id: 2,
                  sender: 'victim',
                  sender_name: 'TechCorp安全团队',
                  content: '我们收到了你们的消息。这个金额对我们来说太高了，我们需要时间评估损失并考虑其他选择。',
                  message_type: 'text',
                  timestamp: '2024-01-15T14:30:00Z',
                  status: 'read'
                },
                {
                  id: 3,
                  sender: 'attacker',
                  sender_name: '操作员_Alpha',
                  content: '时间就是金钱。每延迟一天，价格就会上涨10%。我们已经获取了你们的客户数据，如果不支付，我们将公开这些信息。',
                  message_type: 'threat',
                  timestamp: '2024-01-16T10:15:00Z',
                  status: 'read'
                },
                {
                  id: 4,
                  sender: 'victim',
                  sender_name: 'TechCorp安全团队',
                  content: '我们理解情况的严重性。但是200万美元超出了我们的承受能力。我们能否协商一个更合理的金额？比如50万美元？',
                  message_type: 'text',
                  timestamp: '2024-01-16T16:45:00Z',
                  status: 'read',
                  metadata: {
                    amount: 500000,
                    currency: 'USD'
                  }
                },
                {
                  id: 5,
                  sender: 'attacker',
                  sender_name: '操作员_Alpha',
                  content: '50万太少了。考虑到你们的业务规模和数据价值，最低150万。这是我们的底线。',
                  message_type: 'demand',
                  timestamp: '2024-01-17T09:20:00Z',
                  status: 'read',
                  metadata: {
                    amount: 1500000,
                    currency: 'USD'
                  }
                },
                {
                  id: 6,
                  sender: 'victim',
                  sender_name: 'TechCorp安全团队',
                  content: '我们最多能支付80万美元。这已经是我们能够筹集的最大金额了。请考虑我们的提议。',
                  message_type: 'text',
                  timestamp: '2024-01-18T11:00:00Z',
                  status: 'read',
                  metadata: {
                    amount: 800000,
                    currency: 'USD'
                  }
                },
                {
                  id: 7,
                  sender: 'attacker',
                  sender_name: '操作员_Alpha',
                  content: '经过内部讨论，我们接受80万美元的提议。请在48小时内完成支付，支付后我们将提供解密工具。',
                  message_type: 'demand',
                  timestamp: '2024-01-19T14:30:00Z',
                  status: 'read',
                  metadata: {
                    amount: 800000,
                    currency: 'USD',
                    deadline: '2024-01-21T14:30:00Z'
                  }
                },
                {
                  id: 8,
                  sender: 'victim',
                  sender_name: 'TechCorp安全团队',
                  content: '支付已完成。交易ID: 1a2b3c4d5e6f7g8h9i0j。请提供解密工具。',
                  message_type: 'payment_proof',
                  timestamp: '2024-01-20T10:15:00Z',
                  status: 'read',
                  metadata: {
                    amount: 800000,
                    currency: 'USD'
                  }
                },
                {
                  id: 9,
                  sender: 'attacker',
                  sender_name: '操作员_Alpha',
                  content: '支付已确认。解密工具已通过安全渠道发送。感谢合作。',
                  message_type: 'file',
                  timestamp: '2024-01-20T15:30:00Z',
                  status: 'delivered',
                  metadata: {
                    file_name: 'decryptor.exe',
                    file_size: 2048576
                  }
                }
              ],
              tags: ['制造业', '大型企业', '成功支付'],
              analyst: '安全分析师',
              created_at: '2024-01-15T09:00:00Z',
              updated_at: '2024-01-20T15:30:00Z'
            },
            {
              id: 2,
              case_id: 'NEG-2024-002',
              title: '中央医院拒绝支付案例',
              victim_organization: 'Central Hospital',
              victim_contact: '<EMAIL>',
              attacker_group: query.slug,
              status: '已中断',
              start_date: '2024-01-10T08:00:00Z',
              end_date: '2024-01-12T12:00:00Z',
              initial_demand: {
                amount: 1000000,
                currency: 'USD'
              },
              payment_deadline: '2024-01-20T23:59:59Z',
              communication_platform: 'Telegram',
              total_messages: 4,
              last_activity: '2024-01-12T12:00:00Z',
              summary: '医疗机构遭受攻击后拒绝支付赎金，选择通过备份系统恢复数据。攻击者威胁公开患者数据，但最终未执行威胁。',
              outcome: '拒绝支付',
              messages: [
                {
                  id: 10,
                  sender: 'attacker',
                  sender_name: '操作员_Beta',
                  content: 'Central Hospital，你们的系统已被我们控制。支付100万美元比特币，否则患者数据将被公开。',
                  message_type: 'demand',
                  timestamp: '2024-01-10T08:00:00Z',
                  status: 'read',
                  metadata: {
                    amount: 1000000,
                    currency: 'USD',
                    deadline: '2024-01-20T23:59:59Z'
                  }
                },
                {
                  id: 11,
                  sender: 'victim',
                  sender_name: 'Central Hospital IT',
                  content: '我们不会向网络犯罪分子支付任何费用。我们已经联系了执法部门，并正在通过备份恢复系统。',
                  message_type: 'text',
                  timestamp: '2024-01-10T16:30:00Z',
                  status: 'read'
                },
                {
                  id: 12,
                  sender: 'attacker',
                  sender_name: '操作员_Beta',
                  content: '你们的选择。48小时后，我们将在暗网上公开所有患者记录。这将对你们的声誉造成不可挽回的损害。',
                  message_type: 'threat',
                  timestamp: '2024-01-11T09:15:00Z',
                  status: 'read'
                },
                {
                  id: 13,
                  sender: 'victim',
                  sender_name: 'Central Hospital IT',
                  content: '我们已经通知了所有患者可能的数据泄露风险，并采取了相应的保护措施。我们不会屈服于勒索。',
                  message_type: 'text',
                  timestamp: '2024-01-12T12:00:00Z',
                  status: 'delivered'
                }
              ],
              tags: ['医疗', '拒绝支付', '备份恢复'],
              analyst: '安全分析师',
              created_at: '2024-01-10T08:00:00Z',
              updated_at: '2024-01-12T12:00:00Z'
            },
            {
              id: 3,
              case_id: 'NEG-2024-003',
              title: '金融机构部分支付协商',
              victim_organization: 'SecureBank Financial',
              victim_contact: '<EMAIL>',
              attacker_group: query.slug,
              status: '已完成',
              start_date: '2024-02-05T14:00:00Z',
              end_date: '2024-02-08T18:45:00Z',
              initial_demand: {
                amount: 5000000,
                currency: 'USD'
              },
              final_amount: {
                amount: 1500000,
                currency: 'USD'
              },
              payment_deadline: '2024-02-15T23:59:59Z',
              communication_platform: '暗网论坛',
              total_messages: 12,
              last_activity: '2024-02-08T18:45:00Z',
              summary: '金融机构遭受攻击后，经过激烈谈判，最终同意支付部分赎金。攻击者最初要求500万美元，经过多轮协商，最终以150万美元成交。',
              outcome: '部分支付',
              messages: [
                {
                  id: 14,
                  sender: 'attacker',
                  sender_name: '操作员_Gamma',
                  content: 'SecureBank，你们的核心银行系统已被完全加密。我们要求500万美元比特币赎金。',
                  message_type: 'demand',
                  timestamp: '2024-02-05T14:00:00Z',
                  status: 'read',
                  metadata: {
                    amount: 5000000,
                    currency: 'USD',
                    deadline: '2024-02-15T23:59:59Z'
                  }
                },
                {
                  id: 15,
                  sender: 'victim',
                  sender_name: 'SecureBank应急响应团队',
                  content: '我们正在评估情况。500万美元的要求过高，我们需要时间与董事会讨论。',
                  message_type: 'text',
                  timestamp: '2024-02-05T18:30:00Z',
                  status: 'read'
                },
                {
                  id: 16,
                  sender: 'attacker',
                  sender_name: '操作员_Gamma',
                  content: '我们已经获取了客户的银行记录和交易数据。如果不在72小时内支付，这些数据将被出售给竞争对手。',
                  message_type: 'threat',
                  timestamp: '2024-02-06T09:00:00Z',
                  status: 'read'
                },
                {
                  id: 17,
                  sender: 'victim',
                  sender_name: 'SecureBank应急响应团队',
                  content: '我们理解数据泄露的严重性。但500万超出预算，我们最多能支付100万美元。',
                  message_type: 'text',
                  timestamp: '2024-02-06T15:20:00Z',
                  status: 'read',
                  metadata: {
                    amount: 1000000,
                    currency: 'USD'
                  }
                },
                {
                  id: 18,
                  sender: 'attacker',
                  sender_name: '操作员_Gamma',
                  content: '100万太少。考虑到你们是大型金融机构，最低300万。这是我们的让步。',
                  message_type: 'demand',
                  timestamp: '2024-02-07T10:30:00Z',
                  status: 'read',
                  metadata: {
                    amount: 3000000,
                    currency: 'USD'
                  }
                },
                {
                  id: 19,
                  sender: 'victim',
                  sender_name: 'SecureBank应急响应团队',
                  content: '我们能接受150万美元。这是我们的最终报价，请考虑。',
                  message_type: 'text',
                  timestamp: '2024-02-07T16:45:00Z',
                  status: 'read',
                  metadata: {
                    amount: 1500000,
                    currency: 'USD'
                  }
                },
                {
                  id: 20,
                  sender: 'attacker',
                  sender_name: '操作员_Gamma',
                  content: '150万可以接受。请在24小时内完成支付，我们将提供部分解密密钥。',
                  message_type: 'demand',
                  timestamp: '2024-02-08T11:00:00Z',
                  status: 'read',
                  metadata: {
                    amount: 1500000,
                    currency: 'USD',
                    deadline: '2024-02-09T11:00:00Z'
                  }
                },
                {
                  id: 21,
                  sender: 'victim',
                  sender_name: 'SecureBank应急响应团队',
                  content: '支付已完成。交易哈希：******************************************',
                  message_type: 'payment_proof',
                  timestamp: '2024-02-08T18:45:00Z',
                  status: 'read',
                  metadata: {
                    amount: 1500000,
                    currency: 'USD'
                  }
                }
              ],
              tags: ['金融', '部分支付', '数据泄露威胁'],
              analyst: '高级安全分析师',
              created_at: '2024-02-05T14:00:00Z',
              updated_at: '2024-02-08T18:45:00Z'
            }
          ];

          // 根据组织名称返回不同数量的记录
          if (query.slug === 'lockbit') {
            return records;
          } else if (query.slug === 'conti') {
            return [records[0], records[2]]; // 返回第一个和第三个记录
          } else {
            return [records[1]]; // 返回第二个记录
          }
        },
        // 添加简化谈判记录数据
        'simple_negotiation_records': function() {
          const groupName = query.slug.charAt(0).toUpperCase() + query.slug.slice(1);

          // 为所有组织提供简化谈判记录
          if (query.slug === 'akira') {
            return [
              {
                group: "Akira",
                chat_id: "20250423",
                initialransom: "$600,000",
                negotiatedransom: "$200,000",
                paid: true,
                message_count: 65,
                messages: [
                  {
                    party: "Victim",
                    content: "hi how much for decryption?",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Hello. You've reached an Akira support chat. Currently, we are preparing the list of data we took from your network. For now you have to know that dealing with us is the best possible way to settle this quick and cheap. Keep in touch and be patient with us. We will reach out to you soon.  Do you have a permission to conduct a negotiation on behalf of your organization? Once we get a response you will be provided with all the details.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yes",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "List.7z // 141 KB",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "These files were taken from your network prior to encryption. You can pick 2-3 random files up to 10 MB each from the list and we will upload them to this chat as a proof of possession. To prove that we can properly decrypt your data you can upload 2-3 encrypted files up to 10 MB each to our chat and we will upload decrypted copies back. We're looking through your financial papers to come up with a reasonable demand to you. We offer: 1) full decryption assistance;\\n2) evidence of data removal;\\n3) security report on vulnerabilities we found;\\n4) guarantees not to publish or sell your data;\\n5) guarantees not to attack you in the future. Let me know whether you're interested in a whole deal or in parts. This will affect the final price.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "we need the decryptor. evidence of data removal, and guarantee to not publish or sell data",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "We will let you know the price soon.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "We're willing to set a $600,000 price for ALL the services we offer. We accept payments in BTC. To gain bitcoins you need to go to any exchange platform as binance or coinbase. Here are the guides: https://www.coinbase.com/how-to-buy/bitcoin\\nhttps://www.binance.com/en/how-to-buy/bitcoin You also can buy bitcoin from any local brokers. If you withdraw funds from your bank account, then you have to inform the bank that you need this money for investment purposes only. Do you have any file requests?",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Are you going to work with us?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yes. the VMs are encrypted so hard time to get to the file to provide sample.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "can we give you a directory and file name and you can provide the file as proof of possession?",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Yes, please do asap.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yes, the team is looking at the list to pick out file to show proof of possession.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Any success?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yes, they are sending directories to me soon and i will send to you.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "F:\\\\[redacted].com\\\\unpack\\\\[redacted].docx",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "F:\\\\[redacted].com\\\\unpack\\\\[redacted].pdf",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "F:\\\\[redacted].com\\\\unpack\\\\[redacted].csv",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "F:\\\\[redacted].com\\\\unpack\\\\[redacted].html",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "hello? we are ready to work with you for payment.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "files.rar // 214 KB",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "You can review. Do you want to test our decryption tool before payment?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yes, can we test the decryption tool?",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Sure. Provide the files today.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Hello. Have you managed to gather files?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "sorry. this site was offline for a while",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Now it is on. Where are the files?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "how much for just evidence of data deletion and not leaing data?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "we will pay",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "$320,000 for the rest options.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "No cyber insurance. Can you work with us? can we do $35,000? We can get you paid today if so.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "No. You have to be serious. $35,0000 won't work at all. Please reconsider asap.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yea. just evidence of deletion and not leaking data. what about $95000?",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "$260,000 if you pay today.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "we can do $155,000 today.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "$220,000 today. Here is our BTC wallet [redacted]. Let us know when you are ready to make payment.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "we can't do 220K. we're already loss of business because you entrypted us and we are shut down. let's get you paid. $160,000 today.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Don't tell us stories. $200,000 is the lowest we can accept. Take it or leave it.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "it's the truth but we want this over with. let's meet in the middle at $180k. say yes and we are sending to [redacted]",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Guys, we've already reduced the price significantly. $200,000 is the lowest possible.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yes and we appreciate it. let me check to make sure we can do that.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "ok. we are buying the BTC to send over.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "What's your progress?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "we'll have it today. delay with bank. we will let you know when we are sending. we will first send a smaller amount to confirm receipt.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Standing by. Thank you.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "still waiting on bank. thank you for your patience",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Waiting.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yep we are still waiting on bank transfers to complete",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Keep us posted.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "will do. still waiting on bank",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Any success?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yea, we have the money. purchasing BTC now. can you resend your wallet again? we will send $500 first to make sure you get it. then we will send the rest.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "$500 sent to [redacted]. Confirm reciept.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "0.005 received. You can proceed with the full amount.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "how will you provide evidence of data deletion?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "can with get video evidence of data deletion?",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "You will receive a deletion log which means the raid drives that contained the only copy of your data are fully formatted and erased.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "and guarantee that no data is leaked?",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Sure. Guarantees will be provided as well. Are you going to send the rest?",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "yes, sending now.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "the send is under review.",
                    timestamp: ""
                  },
                  {
                    party: "Victim",
                    content: "rest of the money has been sent over. please provide deletion logs.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Received. Please wait.",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Deletion.7z // 316 KB",
                    timestamp: ""
                  },
                  {
                    party: "Akira",
                    content: "Initial access to your network was purchased on the dark web. Then kerberoasting was carried out and we got passwords hashes. Then we just bruted these and got domain admin password. Spending weeks inside of your network we've managed to detect some fails we highly recommend to eliminate: 1. None of your employees should open suspicious emails, suspicious links or download any files, much less run them on their computer.\\n2. Use strong passwords, change them as often as possible (1-2 times per month at least). Passwords should not match or be repeated on different resources.\\n3. Install 2FA wherever possible.\\n4. Use the latest versions of operating systems, as they are less vulnerable to attacks.\\n5. Update all software versions.\\n6. Use antivirus solutions and traffic monitoring tools.\\n7. Create a jump host for your VPN. Use unique credentials on it that differ from domain one.\\n8. Use backup software with cloud storage which supports a token key.\\n9. Instruct your employees as often as possible about online safety precautions. The most vulnerable point is the human factor and the irresponsibility of your employees, system administrators, etc. We wish you safety, calmness and lots of benefits in the future. Thank you for working with us and your careful attitude to your security.",
                    timestamp: ""
                  }
                ]
              }
            ];
          } else if (query.slug === 'lockbit') {
            return [
              {
                group: "LockBit",
                chat_id: "20250315",
                initialransom: "$2,500,000",
                negotiatedransom: "$800,000",
                paid: false,
                message_count: 28,
                messages: [
                  {
                    party: "Victim",
                    content: "We received your message about our encrypted files. What are your demands?",
                    timestamp: "2025-03-15T08:30:00Z"
                  },
                  {
                    party: "LockBit",
                    content: "Welcome to LockBit support. Your network has been compromised and all critical data encrypted. We have also exfiltrated sensitive information. Payment required: $2,500,000 USD in Bitcoin.",
                    timestamp: "2025-03-15T09:15:00Z"
                  },
                  {
                    party: "Victim",
                    content: "This amount is impossible for us. We are a small company. Can we negotiate?",
                    timestamp: "2025-03-15T10:45:00Z"
                  },
                  {
                    party: "LockBit",
                    content: "We understand your situation. However, the value of your data and potential damage justifies this price. What can you afford?",
                    timestamp: "2025-03-15T11:30:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We can gather maximum $500,000. This would require liquidating assets.",
                    timestamp: "2025-03-15T14:20:00Z"
                  },
                  {
                    party: "LockBit",
                    content: "$500,000 is too low. We can consider $1,200,000 as our final offer. This includes decryption and data deletion guarantee.",
                    timestamp: "2025-03-15T15:45:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We simply cannot afford that amount. Our maximum is $800,000. Please consider this.",
                    timestamp: "2025-03-16T09:00:00Z"
                  },
                  {
                    party: "LockBit",
                    content: "After internal discussion, we can accept $800,000. You have 48 hours to arrange payment.",
                    timestamp: "2025-03-16T16:30:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We need more time to arrange such a large sum. Can you give us one week?",
                    timestamp: "2025-03-17T08:15:00Z"
                  },
                  {
                    party: "LockBit",
                    content: "Maximum 72 hours. After that, price increases to $1,500,000 and data will be published.",
                    timestamp: "2025-03-17T10:00:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We are working with law enforcement and will not pay any ransom. We have backups.",
                    timestamp: "2025-03-18T11:30:00Z"
                  },
                  {
                    party: "LockBit",
                    content: "Your choice. Your customer data and financial records will be published on our leak site in 24 hours. Good luck with your backups.",
                    timestamp: "2025-03-18T12:00:00Z"
                  }
                ]
              }
            ];
          } else if (query.slug === 'conti') {
            return [
              {
                group: "Conti",
                chat_id: "20250201",
                initialransom: "$1,800,000",
                negotiatedransom: "$650,000",
                paid: true,
                message_count: 35,
                messages: [
                  {
                    party: "Victim",
                    content: "Our systems have been encrypted. We need to understand the situation.",
                    timestamp: "2025-02-01T07:45:00Z"
                  },
                  {
                    party: "Conti",
                    content: "Hello. This is Conti ransomware group. Your network is under our control. All files encrypted with military-grade encryption. We also downloaded your sensitive data. Payment: $1,800,000 Bitcoin.",
                    timestamp: "2025-02-01T08:30:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We are a healthcare organization. This will impact patient care. Can you provide some files for free?",
                    timestamp: "2025-02-01T09:15:00Z"
                  },
                  {
                    party: "Conti",
                    content: "We understand healthcare importance. We can provide 10% of files for free as goodwill gesture. But full recovery requires payment.",
                    timestamp: "2025-02-01T10:00:00Z"
                  },
                  {
                    party: "Victim",
                    content: "Thank you for understanding. However, $1.8M is beyond our budget. We can offer $400,000.",
                    timestamp: "2025-02-01T11:30:00Z"
                  },
                  {
                    party: "Conti",
                    content: "$400,000 is insufficient. Your patient database alone is worth more. Minimum $1,200,000.",
                    timestamp: "2025-02-01T14:20:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We are negotiating with insurance. Can we get to $650,000? This is really our maximum.",
                    timestamp: "2025-02-02T08:00:00Z"
                  },
                  {
                    party: "Conti",
                    content: "Considering healthcare nature of your business, we accept $650,000. Payment must be completed within 48 hours.",
                    timestamp: "2025-02-02T10:30:00Z"
                  },
                  {
                    party: "Victim",
                    content: "Agreed. We are arranging Bitcoin purchase. Will send payment confirmation soon.",
                    timestamp: "2025-02-02T15:45:00Z"
                  },
                  {
                    party: "Victim",
                    content: "Payment sent. Transaction ID: 7f8e9d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c5b4a3f2e1d0c",
                    timestamp: "2025-02-03T12:15:00Z"
                  },
                  {
                    party: "Conti",
                    content: "Payment confirmed. Decryption tool and instructions being prepared. You will receive them within 2 hours.",
                    timestamp: "2025-02-03T13:00:00Z"
                  },
                  {
                    party: "Conti",
                    content: "Decryption package sent via secure channel. Data deletion proof will follow. Thank you for cooperation.",
                    timestamp: "2025-02-03T14:30:00Z"
                  }
                ]
              }
            ];
          } else if (query.slug === 'cl0p') {
            return [
              {
                group: "Cl0p",
                chat_id: "20250110",
                initialransom: "$3,200,000",
                negotiatedransom: "$1,100,000",
                paid: true,
                message_count: 42,
                messages: [
                  {
                    party: "Victim",
                    content: "We discovered our files are encrypted. Are you responsible for this?",
                    timestamp: "2025-01-10T06:30:00Z"
                  },
                  {
                    party: "Cl0p",
                    content: "Yes, this is Cl0p ransomware operation. Your entire infrastructure is compromised. We have encrypted all critical systems and exfiltrated confidential data. Ransom demand: $3,200,000 USD in Monero cryptocurrency.",
                    timestamp: "2025-01-10T07:15:00Z"
                  },
                  {
                    party: "Victim",
                    content: "This is a financial institution. We need proof that you actually have our data before any negotiation.",
                    timestamp: "2025-01-10T08:45:00Z"
                  },
                  {
                    party: "Cl0p",
                    content: "Proof.zip uploaded. Contains samples of customer financial records, internal communications, and regulatory documents. Full dataset: 2.3TB.",
                    timestamp: "2025-01-10T09:30:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We verified the samples. However, $3.2M is excessive. Our board approved maximum $800,000.",
                    timestamp: "2025-01-10T11:00:00Z"
                  },
                  {
                    party: "Cl0p",
                    content: "$800,000 is insulting for a bank of your size. We know your annual revenue. Minimum $2,500,000 or data goes public in 72 hours.",
                    timestamp: "2025-01-10T12:15:00Z"
                  },
                  {
                    party: "Victim",
                    content: "We understand the severity. Can we meet at $1,500,000? This requires board approval and liquidating emergency funds.",
                    timestamp: "2025-01-11T09:30:00Z"
                  },
                  {
                    party: "Cl0p",
                    content: "We appreciate the increased offer. Our final price: $1,800,000. This includes full decryption, data deletion, and vulnerability report.",
                    timestamp: "2025-01-11T11:45:00Z"
                  },
                  {
                    party: "Victim",
                    content: "$1,800,000 is still too high. Our absolute maximum is $1,100,000. Please consider the reputational damage to both parties if this becomes public.",
                    timestamp: "2025-01-11T14:20:00Z"
                  },
                  {
                    party: "Cl0p",
                    content: "After team consultation, we accept $1,100,000. Payment must be completed within 24 hours. Monero address: 4AdUndXHHZ6cfufTMvppY6JwXNouMBzSkbLYfpAV5Usx3skxNgYeYTRJ5Lq5qQQhiQ5nTdMQVYdRJLLfmKWXOkcTGn1JMZH3pBv4fQ2yrHuYKKa",
                    timestamp: "2025-01-11T16:00:00Z"
                  },
                  {
                    party: "Victim",
                    content: "Agreed. We are purchasing Monero now. Will confirm payment within 12 hours.",
                    timestamp: "2025-01-11T17:30:00Z"
                  },
                  {
                    party: "Victim",
                    content: "Payment completed. Transaction hash: 8f7e6d5c4b3a2f1e0d9c8b7a6f5e4d3c2b1a0f9e8d7c6b5a4f3e2d1c0b9a8f7e6d5c4b3a2f1e0d9c8b7a6f5e4d3c2b1a",
                    timestamp: "2025-01-12T05:45:00Z"
                  },
                  {
                    party: "Cl0p",
                    content: "Payment verified. Decryption tools and deletion certificates being prepared. Professional transaction. Thank you.",
                    timestamp: "2025-01-12T06:30:00Z"
                  }
                ]
              }
            ];
          } else {
            // 为其他组织提供通用的简化谈判记录
            return [
              {
                group: groupName,
                chat_id: Mock.mock('@date("yyyyMMdd")'),
                initialransom: `$${Mock.mock('@integer(500, 5000)')}K`,
                negotiatedransom: `$${Mock.mock('@integer(200, 2000)')}K`,
                paid: Mock.mock('@boolean'),
                message_count: Mock.mock('@integer(15, 50)'),
                messages: [
                  {
                    party: "Victim",
                    content: "We need to discuss the situation with our encrypted files.",
                    timestamp: Mock.mock('@datetime')
                  },
                  {
                    party: groupName,
                    content: `This is ${groupName} ransomware group. Your systems have been encrypted and data exfiltrated. Payment required for recovery.`,
                    timestamp: Mock.mock('@datetime')
                  },
                  {
                    party: "Victim",
                    content: "What is your ransom demand?",
                    timestamp: Mock.mock('@datetime')
                  },
                  {
                    party: groupName,
                    content: `Our demand is $${Mock.mock('@integer(500, 5000)')}K USD in Bitcoin. You have 72 hours to respond.`,
                    timestamp: Mock.mock('@datetime')
                  },
                  {
                    party: "Victim",
                    content: "This amount is too high for our organization. Can we negotiate?",
                    timestamp: Mock.mock('@datetime')
                  },
                  {
                    party: groupName,
                    content: "We can consider a reduced amount based on your financial situation. What can you afford?",
                    timestamp: Mock.mock('@datetime')
                  },
                  {
                    party: "Victim",
                    content: `Our maximum budget is $${Mock.mock('@integer(200, 2000)')}K. Please consider this offer.`,
                    timestamp: Mock.mock('@datetime')
                  },
                  {
                    party: groupName,
                    content: "After internal discussion, we can accept your offer. Proceed with payment arrangements.",
                    timestamp: Mock.mock('@datetime')
                  }
                ]
              }
            ];
          }
        },
        // 添加勒索信样本数据
        'ransom_notes': function() {
          const notes = [
            {
              id: 1,
              filename: 'README.txt',
              version: 'v2.1',
              language: '中文/英文',
              content: `!!! 您的文件已被加密 !!!

亲爱的 ${query.slug.toUpperCase()} 受害者，

我们是 ${query.slug.charAt(0).toUpperCase() + query.slug.slice(1)} 勒索软件组织。您的所有重要文件已被我们的高级加密算法锁定。

=== 发生了什么？ ===
• 您的文件已使用军用级AES-256加密算法加密
• 您的网络已被完全渗透
• 我们已获取您的敏感数据副本
• 没有我们的解密密钥，您无法恢复任何文件

=== 如何恢复文件？ ===
1. 访问我们的Tor网站: http://${query.slug.toLowerCase()}-decrypt.onion
2. 使用您的唯一ID: ${Math.random().toString(36).substring(2, 15).toUpperCase()}
3. 按照指示支付赎金
4. 获得解密工具和密钥

=== 重要警告 ===
• 不要尝试自行解密文件，这将导致永久数据丢失
• 不要重启计算机或关闭此程序
• 不要联系执法部门，这将导致数据公开
• 您有72小时时间支付，逾期价格翻倍

=== 支付信息 ===
赎金金额: $2,000,000 USD (比特币)
支付地址: 1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa
截止时间: 72小时

=== 联系我们 ===
Telegram: @${query.slug.toLowerCase()}_support
Tor聊天: http://${query.slug.toLowerCase()}-chat.onion

记住：时间就是金钱。每延迟一小时，恢复的可能性就降低一分。

${query.slug.charAt(0).toUpperCase() + query.slug.slice(1)} 团队`,
              content_preview: `!!! 您的文件已被加密 !!!

亲爱的 ${query.slug.toUpperCase()} 受害者，

我们是 ${query.slug.charAt(0).toUpperCase() + query.slug.slice(1)} 勒索软件组织。您的所有重要文件已被我们的高级加密算法锁定。

=== 发生了什么？ ===
• 您的文件已使用军用级AES-256加密算法加密
• 您的网络已被完全渗透
• 我们已获取您的敏感数据副本

[内容已截断，点击展开查看完整内容]`,
              discovered_date: '2024-01-15T10:30:00Z',
              file_size: 2048,
              hash: 'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',
              analysis: {
                tone: '威胁性',
                threats: ['数据泄露威胁', '时间压力', '执法警告', '技术恐吓'],
                payment_instructions: '包含详细的比特币支付指南和Tor网站访问方式',
                contact_methods: ['Telegram', 'Tor聊天室'],
                deadline_mentioned: true,
                amount_mentioned: true
              },
              tags: ['双重勒索', '高赎金', '多语言', '专业化']
            },
            {
              id: 2,
              filename: 'DECRYPT_INSTRUCTIONS.html',
              version: 'v1.8',
              language: '英文',
              content: `<!DOCTYPE html>
<html>
<head>
    <title>${query.slug.toUpperCase()} Decryption Instructions</title>
    <style>
        body { background: #000; color: #ff0000; font-family: monospace; }
        .warning { color: #ffff00; font-size: 24px; }
        .important { color: #ff6600; }
    </style>
</head>
<body>
    <div class="warning">⚠️ YOUR FILES ARE ENCRYPTED ⚠️</div>

    <h2>What happened to your files?</h2>
    <p>All your important files have been encrypted with military-grade encryption.</p>
    <p>Your network has been compromised and we have downloaded sensitive data.</p>

    <h2 class="important">Recovery Instructions:</h2>
    <ol>
        <li>Download Tor Browser from: https://www.torproject.org/</li>
        <li>Visit our payment portal: http://${query.slug.toLowerCase()}-pay.onion</li>
        <li>Enter your unique victim ID: ${Math.random().toString(36).substring(2, 15).toUpperCase()}</li>
        <li>Follow payment instructions</li>
        <li>Download decryption tool after payment confirmation</li>
    </ol>

    <h2>Payment Details:</h2>
    <p><strong>Amount:</strong> $1,500,000 USD in Bitcoin</p>
    <p><strong>Wallet:</strong> ******************************************</p>
    <p><strong>Deadline:</strong> 48 hours from infection</p>

    <h2 class="warning">⚠️ WARNINGS ⚠️</h2>
    <ul>
        <li>Do NOT attempt to decrypt files yourself</li>
        <li>Do NOT contact law enforcement</li>
        <li>Do NOT use third-party decryption tools</li>
        <li>Price DOUBLES after deadline</li>
    </ul>

    <p>Contact us: ${query.slug.toLowerCase()}@protonmail.com</p>
    <p>Backup contact: @${query.slug.toLowerCase()}_official (Telegram)</p>
</body>
</html>`,
              content_preview: `<!DOCTYPE html>
<html>
<head>
    <title>${query.slug.toUpperCase()} Decryption Instructions</title>
    <style>
        body { background: #000; color: #ff0000; font-family: monospace; }
        .warning { color: #ffff00; font-size: 24px; }
    </style>
</head>
<body>
    <div class="warning">⚠️ YOUR FILES ARE ENCRYPTED ⚠️</div>

    <h2>What happened to your files?</h2>
    <p>All your important files have been encrypted with military-grade encryption.</p>

[HTML内容已截断，点击展开查看完整内容]`,
              discovered_date: '2024-01-20T14:15:00Z',
              file_size: 3584,
              hash: 'b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567a',
              analysis: {
                tone: '商业化',
                threats: ['数据泄露', '价格上涨', '技术警告'],
                payment_instructions: '包含详细的Tor浏览器下载和比特币支付流程',
                contact_methods: ['ProtonMail', 'Telegram', 'Tor网站'],
                deadline_mentioned: true,
                amount_mentioned: true
              },
              tags: ['HTML格式', '专业设计', '多联系方式', '技术指导']
            },
            {
              id: 3,
              filename: 'RECOVERY_INFO.txt',
              version: 'v3.0',
              language: '中文',
              content: `████████████████████████████████████████
█                                      █
█    您的数据已被 ${query.slug.toUpperCase()} 加密    █
█                                      █
████████████████████████████████████████

尊敬的用户：

很遗憾地通知您，您的计算机系统已被我们的高级勒索软件感染。
所有重要文件已使用不可破解的加密算法进行加密。

【当前状况】
✗ 所有文档、图片、数据库文件已加密
✗ 系统备份已被删除
✗ 影子副本已被清除
✗ 网络共享文件已加密

【恢复方案】
要恢复您的文件，您需要：
1. 支付解密费用：￥1,200,000 (等值比特币)
2. 获取专用解密工具
3. 获取唯一解密密钥

【支付流程】
第一步：下载并安装Tor浏览器
第二步：访问 http://${query.slug.toLowerCase()}-cn.onion
第三步：输入您的ID：${Math.random().toString(36).substring(2, 12).toUpperCase()}
第四步：按照页面指示完成支付
第五步：下载解密工具

【重要提醒】
• 请在48小时内完成支付，逾期价格上涨50%
• 不要尝试修改加密文件，可能导致永久损坏
• 不要使用第三方解密工具
• 我们保证支付后立即提供解密方案

【联系方式】
主要联系：${query.slug.toLowerCase()}.<EMAIL>
备用联系：@${query.slug.toLowerCase()}_cn (Telegram)
技术支持：访问我们的Tor网站获取在线客服

我们理解这种情况给您带来的不便，但请相信我们是专业的。
只要按照指示操作，您的文件将100%恢复。

${query.slug.charAt(0).toUpperCase() + query.slug.slice(1)} 技术团队
2024年1月`,
              content_preview: `████████████████████████████████████████
█                                      █
█    您的数据已被 ${query.slug.toUpperCase()} 加密    █
█                                      █
████████████████████████████████████████

尊敬的用户：

很遗憾地通知您，您的计算机系统已被我们的高级勒索软件感染。
所有重要文件已使用不可破解的加密算法进行加密。

【当前状况】
✗ 所有文档、图片、数据库文件已加密
✗ 系统备份已被删除

[内容已截断，点击展开查看完整内容]`,
              discovered_date: '2024-01-25T09:45:00Z',
              file_size: 1856,
              hash: 'c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567ab2',
              analysis: {
                tone: '恐吓性',
                threats: ['数据永久丢失', '价格上涨', '时间压力'],
                payment_instructions: '提供中文本地化的详细支付指南',
                contact_methods: ['Tutanota邮箱', 'Telegram', 'Tor在线客服'],
                deadline_mentioned: true,
                amount_mentioned: true
              },
              tags: ['中文本地化', '视觉设计', '客服支持', '专业术语']
            }
          ];

          // 根据组织返回不同的勒索信样本
          if (query.slug === 'lockbit') {
            return notes;
          } else if (query.slug === 'conti') {
            return [notes[0], notes[2]]; // 返回第一个和第三个
          } else {
            return [notes[1]]; // 返回第二个
          }
        }
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取勒索软件组织统计数据
  {
    url: '/api/ransomware-groups/stats',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'total_groups': '@integer(50, 150)',
        'active_groups': '@integer(20, 60)',
        'inactive_groups': '@integer(10, 40)',
        'dissolved_groups': '@integer(5, 20)',
        'threat_levels': {
          '极高': '@integer(5, 15)',
          '高': '@integer(15, 30)',
          '中': '@integer(20, 40)',
          '低': '@integer(10, 25)'
        },
        'monthly_activity|12': [
          {
            'month': '@date("MM")',
            'attacks': '@integer(20, 100)',
            'new_groups': '@integer(0, 5)'
          }
        ],
        'top_targets': {
          '金融': '@integer(100, 300)',
          '医疗': '@integer(80, 250)',
          '教育': '@integer(60, 200)',
          '政府': '@integer(40, 150)',
          '制造业': '@integer(70, 220)'
        }
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  }
] as MockMethod[];

