import type { MockMethod } from 'vite-plugin-mock';
// import Mock from 'mockjs'; // 移除未使用的导入

// 模拟威胁情报数据
const mockThreats = [
  {
    id: 1,
    title: 'LockBit 3.0 勒索软件新变种分析',
    description: '发现 LockBit 3.0 的新变种，采用了更强的加密算法和反分析技术，主要针对企业网络进行攻击。该变种使用了新的传播机制，能够快速在网络中横向移动。',
    severity: '高危',
    source: '暗网监控',
    publishTime: '2小时前',
    type: 'threats',
    tags: ['勒索软件', 'LockBit', '企业安全', '加密']
  },
  {
    id: 2,
    title: 'APT29 针对政府机构的新攻击活动',
    description: '俄罗斯 APT29 组织近期针对多个国家政府机构发起新一轮攻击，使用了零日漏洞和社会工程学技术。攻击目标主要是外交部门和国防机构。',
    severity: '严重',
    source: 'Telegram',
    publishTime: '4小时前',
    type: 'threats',
    tags: ['APT29', '政府', '零日漏洞', '社会工程']
  },
  {
    id: 3,
    title: 'Conti 勒索组织内部聊天记录泄露',
    description: 'Conti 勒索组织内部聊天记录被泄露，揭示了其运作模式和目标选择策略。泄露的信息包含了组织架构、分工和收益分配方式。',
    severity: '中等',
    source: '人工情报',
    publishTime: '6小时前',
    type: 'threats',
    tags: ['Conti', '勒索组织', '内部泄露', '运作模式']
  }
];

// 模拟安全事件数据
const mockEvents = [
  {
    id: 1,
    title: '某大型制造企业遭受勒索攻击',
    description: '该企业的生产系统被加密，攻击者要求支付500万美元赎金。目前企业已停止生产，正在评估损失和恢复方案。',
    status: '进行中',
    targetIndustry: '制造业',
    targetRegion: '北美',
    incidentTime: '1小时前',
    type: 'events',
    tags: ['勒索攻击', '制造业', '生产中断', '赎金']
  },
  {
    id: 2,
    title: '医疗机构数据泄露事件',
    description: '某医疗机构患者数据被窃取，涉及超过10万条个人信息，包括姓名、地址、病历等敏感信息。',
    status: '已确认',
    targetIndustry: '医疗',
    targetRegion: '欧洲',
    incidentTime: '3小时前',
    type: 'events',
    tags: ['数据泄露', '医疗', '个人信息', '隐私']
  },
  {
    id: 3,
    title: '金融机构网络钓鱼攻击',
    description: '多家银行客户收到伪造的钓鱼邮件，试图窃取登录凭证。攻击者使用了高度仿真的银行网站页面。',
    status: '已解决',
    targetIndustry: '金融',
    targetRegion: '亚太',
    incidentTime: '5小时前',
    type: 'events',
    tags: ['网络钓鱼', '金融', '凭证窃取', '邮件攻击']
  }
];

// 模拟勒索组织数据
const mockGroups = [
  {
    id: 1,
    name: 'LockBit',
    description: '当前最活跃的勒索组织之一，采用RaaS模式运营，拥有完善的技术支持和客户服务体系。',
    activityLevel: '极高',
    recentTargets: 15,
    lastActivity: '2小时前',
    type: 'groups',
    tags: ['RaaS', '活跃', '技术先进', '客户服务']
  },
  {
    id: 2,
    name: 'BlackCat',
    description: '使用Rust编程语言开发的新型勒索软件，具有跨平台能力，能够攻击Windows、Linux和VMware ESXi系统。',
    activityLevel: '高',
    recentTargets: 8,
    lastActivity: '1天前',
    type: 'groups',
    tags: ['Rust', '跨平台', 'VMware', '新型']
  },
  {
    id: 3,
    name: 'Hive',
    description: '专门针对医疗和教育行业的勒索组织，使用双重勒索策略，既加密数据又威胁泄露敏感信息。',
    activityLevel: '中等',
    recentTargets: 5,
    lastActivity: '3天前',
    type: 'groups',
    tags: ['医疗', '教育', '双重勒索', '敏感信息']
  }
];

// 合并所有数据
const allData = [
  ...mockThreats,
  ...mockEvents,
  ...mockGroups
];

export default [
  // 全局搜索
  {
    url: '/api/search',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const {
        query: searchQuery = '',
        type = 'all',
        severity = 'all',
        source = 'all',
        page = 1,
        pageSize = 20
      } = query;

      let results = [...allData];

      // 文本搜索
      if (searchQuery.trim()) {
        const q = searchQuery.toLowerCase();
        results = results.filter((item: any) => {
          const searchableText = [
            item.title || item.name,
            item.description,
            item.source,
            item.severity,
            item.status,
            item.targetIndustry,
            item.targetRegion,
            item.activityLevel,
            ...(item.tags || [])
          ].filter(Boolean).join(' ').toLowerCase();

          return searchableText.includes(q);
        });
      }

      // 类型筛选
      if (type !== 'all') {
        results = results.filter(item => item.type === type);
      }

      // 严重程度筛选
      if (severity !== 'all') {
        results = results.filter((item: any) => item.severity === severity);
      }

      // 来源筛选
      if (source !== 'all') {
        results = results.filter((item: any) => item.source === source);
      }

      // 分页
      const total = results.length;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const paginatedResults = results.slice(start, end);

      return {
        code: 200,
        message: 'success',
        data: {
          items: paginatedResults,
          total,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(total / pageSize)
        }
      };
    }
  },

  // 搜索建议
  {
    url: '/api/search/suggestions',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const { q = '' } = query;

      // 基于现有数据生成搜索建议
      const suggestions = [
        'LockBit',
        'APT29',
        'Conti',
        'BlackCat',
        'Hive',
        '勒索软件',
        '数据泄露',
        '网络钓鱼',
        '制造业',
        '医疗',
        '金融',
        '政府',
        '暗网监控',
        'Telegram',
        '人工情报'
      ].filter(suggestion =>
        suggestion.toLowerCase().includes(q.toLowerCase())
      ).slice(0, 8);

      return {
        code: 200,
        message: 'success',
        data: {
          suggestions
        }
      };
    }
  },

  // 搜索历史
  {
    url: '/api/search/history',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          history: [
            'LockBit 勒索软件',
            'APT29 攻击',
            '医疗数据泄露',
            '金融网络钓鱼',
            'Conti 组织'
          ]
        }
      };
    }
  },

  // 保存搜索历史
  {
    url: '/api/search/history',
    method: 'post',
    response: ({ body: _body }: { body: any }) => {
      return {
        code: 200,
        message: '搜索历史已保存',
        data: null
      };
    }
  }
] as MockMethod[];
