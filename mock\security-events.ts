import type { MockMethod } from 'vite-plugin-mock';
import Mock from 'mockjs';

// 定义类型接口
interface MockContext {
  id: number;
  severity: '严重' | '高' | '中' | '低';
  detection_time: string;
}

// 安全事件数据模拟
export default [
  // 获取安全事件列表
  {
    url: '/api/security-events',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'list|20-30': [
          {
            'id|+1': 1,
            'event_id': function(this: MockContext): string {
              return 'SEC' + String(this.id).padStart(6, '0')
            },
            'title': function(this: MockContext): string {
              const types = ['恶意软件感染', 'DDoS攻击', '数据泄露', '异常登录', '网络入侵', '系统漏洞', 'APT攻击', '勒索软件', '钓鱼攻击', '内网横移']
              const targets = ['Web服务器', '数据库服务器', '邮件服务器', '文件服务器', '域控制器', '办公终端', '生产系统', '测试环境']
              const type: string = types[this.id % types.length]
              const target: string = targets[this.id % targets.length]
              return `${type}事件 - ${target}${type}检测`
            },
            'type|1': ['入侵检测', '恶意软件', '异常行为', '数据泄露', '网络攻击', '系统异常'],
            'severity|1': ['严重', '高', '中', '低'],
            'status|1': ['新事件', '处理中', '已处理', '已关闭', '误报'],
            'source_ip': function(this: MockContext): string {
              // 生成更真实的IP地址
              const ranges = ['192.168.1.', '10.0.0.', '172.16.1.', '203.0.113.', '198.51.100.']
              const range: string = ranges[this.id % ranges.length]
              return range + (this.id % 254 + 1)
            },
            'target_ip': function(this: MockContext): string {
              const ranges = ['10.0.1.', '192.168.100.', '172.16.10.', '10.10.10.']
              const range: string = ranges[this.id % ranges.length]
              return range + (this.id % 254 + 1)
            },
            'source_system': function(this: MockContext): string {
              const systems = ['外部网络', 'Web服务器', '邮件服务器', '文件服务器', '未知系统', '移动设备', 'VPN连接']
              return systems[this.id % systems.length]
            },
            'target_system': function(this: MockContext): string {
              const systems = ['内网主机', '办公电脑', '数据库服务器', '域控制器', '文件服务器', '生产服务器', '测试环境']
              return systems[this.id % systems.length]
            },
            'description': function(this: MockContext): string {
              const descriptions = [
                '检测到恶意软件在目标系统上执行，具有数据窃取和远程控制能力，需要立即处理。',
                '发现大规模DDoS攻击流量，攻击者试图通过流量洪水使服务不可用。',
                '监测到敏感数据异常外传，可能存在数据泄露风险，需要紧急调查。',
                '发现多次异常登录尝试，疑似暴力破解攻击，建议加强账户安全。',
                '检测到网络入侵行为，攻击者已获得初始访问权限，正在进行横向移动。',
                '发现系统存在高危漏洞，攻击者可能利用该漏洞获取系统权限。',
                '监测到APT攻击活动，攻击者使用高级持续威胁技术进行长期潜伏。',
                '检测到勒索软件活动，恶意软件正在加密文件并要求赎金。',
                '发现钓鱼攻击邮件，攻击者试图窃取用户凭据和敏感信息。',
                '检测到内网横向移动行为，攻击者正在扩大攻击范围。'
              ]
              return descriptions[this.id % descriptions.length]
            },
            'detection_time': function(): string {
              // 生成最近30天内的随机时间
              const now = new Date()
              const randomDays = Math.floor(Math.random() * 30)
              const randomHours = Math.floor(Math.random() * 24)
              const randomMinutes = Math.floor(Math.random() * 60)
              const date = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000 - randomHours * 60 * 60 * 1000 - randomMinutes * 60 * 1000)
              return date.toISOString()
            },
            'first_seen': function(this: MockContext): string {
              // 首次发现时间应该早于或等于检测时间
              const detectionTime = new Date(this.detection_time)
              const randomHours = Math.floor(Math.random() * 48) // 最多早48小时
              const firstSeenTime = new Date(detectionTime.getTime() - randomHours * 60 * 60 * 1000)
              return firstSeenTime.toISOString()
            },
            'last_seen': function(this: MockContext): string {
              // 最后发现时间应该晚于或等于检测时间
              const detectionTime = new Date(this.detection_time)
              const randomHours = Math.floor(Math.random() * 24) // 最多晚24小时
              const lastSeenTime = new Date(detectionTime.getTime() + randomHours * 60 * 60 * 1000)
              return lastSeenTime.toISOString()
            },
            'event_count': function(this: MockContext): number {
              // 根据严重程度生成合理的事件数量
              const severityMap: Record<string, number[]> = { '严重': [50, 200], '高': [20, 100], '中': [5, 50], '低': [1, 20] }
              const range: number[] = severityMap[this.severity] || [1, 10]
              return Math.floor(Math.random() * (range[1] - range[0] + 1)) + range[0]
            },
            'risk_score': function(this: MockContext): number {
              // 根据严重程度生成合理的风险评分
              const severityMap: Record<string, number[]> = { '严重': [80, 100], '高': [60, 85], '中': [30, 65], '低': [1, 35] }
              const range: number[] = severityMap[this.severity] || [1, 50]
              return Math.floor(Math.random() * (range[1] - range[0] + 1)) + range[0]
            },
            'analyst': function(this: MockContext): string {
              const analysts = ['张安全', '李防护', '王监控', '赵分析', '刘响应', '陈检测', '杨调查', '周处理']
              return analysts[this.id % analysts.length]
            },
            'tags': function(this: MockContext): string[] {
              const tagGroups = [
                ['恶意软件', '木马', '远程控制', '数据窃取'],
                ['DDoS', '流量攻击', '服务中断', '可用性'],
                ['数据泄露', '敏感信息', '隐私泄露', '合规风险'],
                ['异常登录', '暴力破解', '账户安全', '身份验证'],
                ['网络入侵', 'APT攻击', '横向移动', '权限提升'],
                ['系统漏洞', '代码执行', '权限提升', '补丁管理'],
                ['APT攻击', '高级威胁', '持续威胁', '定向攻击'],
                ['勒索软件', '文件加密', '赎金要求', '业务中断'],
                ['钓鱼攻击', '社会工程', '凭据窃取', '邮件安全'],
                ['内网横移', '权限扩展', '网络渗透', '资产发现']
              ]
              return tagGroups[this.id % tagGroups.length]
            }
          }
        ],
        total: '@integer(200, 1000)',
        page: 1,
        pageSize: 20
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取安全事件详情
  {
    url: '/api/security-events/:id',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const eventId = parseInt(query.id)
      const data = Mock.mock({
        'id': eventId,
        'event_id': function(): string {
          return 'SEC' + String(eventId).padStart(6, '0')
        },
        'title': function(): string {
          const titles = [
            '恶意软件感染事件 - Trojan.Win32.Agent变种检测',
            'DDoS攻击事件 - 大规模流量攻击',
            '数据泄露事件 - 客户信息异常外传',
            '异常登录事件 - 多次失败登录尝试',
            '网络入侵事件 - 未授权访问检测',
            '系统漏洞事件 - 高危漏洞利用尝试',
            'APT攻击事件 - 高级持续威胁活动',
            '勒索软件事件 - 文件加密攻击',
            '钓鱼攻击事件 - 恶意邮件传播',
            '内网横移事件 - 权限扩展行为'
          ]
          return titles[eventId % titles.length]
        },
        'type|1': ['入侵检测', '恶意软件', '异常行为', '数据泄露', '网络攻击', '系统异常'],
        'severity|1': ['严重', '高', '中', '低'],
        'status|1': ['新事件', '处理中', '已处理', '已关闭', '误报'],
        'source_ip': function(): string {
          const ranges = ['192.168.1.', '10.0.0.', '172.16.1.', '203.0.113.', '198.51.100.']
          const range: string = ranges[eventId % ranges.length]
          return range + (eventId % 254 + 1)
        },
        'target_ip': function(): string {
          const ranges = ['10.0.1.', '192.168.100.', '172.16.10.', '10.10.10.']
          const range: string = ranges[eventId % ranges.length]
          return range + (eventId % 254 + 1)
        },
        'source_system': function(): string {
          const systems = ['外部网络', 'Web服务器', '邮件服务器', '文件服务器', '未知系统', '移动设备', 'VPN连接']
          return systems[eventId % systems.length]
        },
        'target_system': function(): string {
          const systems = ['内网主机', '办公电脑', '数据库服务器', '域控制器', '文件服务器', '生产服务器', '测试环境']
          return systems[eventId % systems.length]
        },
        'description': function(): string {
          const descriptions = [
            '检测到恶意软件Trojan.Win32.Agent变种在内网主机上活动，该恶意软件具有远程控制、数据窃取和横向移动能力。',
            '检测到针对Web服务器的大规模DDoS攻击，攻击流量峰值达到500Gbps，导致服务暂时不可用。',
            '监测到客户敏感信息异常外传，涉及个人身份信息和财务数据，存在严重的数据泄露风险。',
            '发现针对管理员账户的多次异常登录尝试，疑似暴力破解攻击，已触发账户锁定机制。',
            '检测到未授权网络访问行为，攻击者已绕过防火墙获得内网访问权限，正在进行侦察活动。',
            '发现系统存在高危远程代码执行漏洞，攻击者正在尝试利用该漏洞获取系统控制权。',
            '监测到APT攻击组织的恶意活动，攻击者使用零日漏洞和定制化恶意软件进行长期潜伏。',
            '检测到勒索软件活动，恶意软件正在加密重要文件并显示赎金要求界面。',
            '发现大量钓鱼攻击邮件，攻击者冒充银行和政府机构试图窃取用户凭据。',
            '检测到内网横向移动行为，攻击者正在利用合法凭据访问多个系统和资源。'
          ]
          return descriptions[eventId % descriptions.length]
        },
        'detailed_analysis': function(): string {
          const analyses = [
            `<h2 class="text-2xl lg:text-3xl font-bold text-base-content mt-8 mb-6 first:mt-0">事件概述</h2>
            <p class="mb-6 leading-relaxed">本次安全事件涉及Trojan.Win32.Agent恶意软件变种，该恶意软件通过钓鱼邮件传播，成功感染了内网主机。恶意软件具有多种恶意功能，包括远程控制、敏感数据窃取、键盘记录和横向移动能力。</p>
            <h2 class="text-2xl lg:text-3xl font-bold text-base-content mt-8 mb-6">技术分析</h2>
            <p class="mb-6 leading-relaxed">该恶意软件采用了多种反检测技术，包括代码混淆、反虚拟机检测和进程注入。分析发现其主要功能模块包括远程控制、数据窃取、键盘记录和横向移动等。</p>`,

            `<h2 class="text-2xl lg:text-3xl font-bold text-base-content mt-8 mb-6 first:mt-0">攻击概述</h2>
            <p class="mb-6 leading-relaxed">本次DDoS攻击采用了多种攻击手段，包括UDP洪水攻击、TCP SYN洪水攻击和HTTP洪水攻击。攻击者利用僵尸网络发起协调攻击，试图耗尽服务器资源。</p>
            <h2 class="text-2xl lg:text-3xl font-bold text-base-content mt-8 mb-6">攻击特征</h2>
            <p class="mb-6 leading-relaxed">攻击流量峰值达到500Gbps，持续时间超过2小时，涉及全球超过10,000个IP地址，主要采用UDP洪水和HTTP洪水攻击方式。</p>`
          ]
          return analyses[eventId % analyses.length]
        },
        'detection_time': function(): string {
          const now = new Date()
          const randomDays = Math.floor(Math.random() * 7)
          const randomHours = Math.floor(Math.random() * 24)
          const date = new Date(now.getTime() - randomDays * 24 * 60 * 60 * 1000 - randomHours * 60 * 60 * 1000)
          return date.toISOString()
        },
        'first_seen': function(this: MockContext): string {
          const detectionTime = new Date(this.detection_time)
          const randomHours = Math.floor(Math.random() * 48)
          const firstSeenTime = new Date(detectionTime.getTime() - randomHours * 60 * 60 * 1000)
          return firstSeenTime.toISOString()
        },
        'last_seen': function(this: MockContext): string {
          const detectionTime = new Date(this.detection_time)
          const randomHours = Math.floor(Math.random() * 24)
          const lastSeenTime = new Date(detectionTime.getTime() + randomHours * 60 * 60 * 1000)
          return lastSeenTime.toISOString()
        },
        'event_count': function(this: MockContext): number {
          const severityMap: Record<string, number[]> = { '严重': [50, 200], '高': [20, 100], '中': [5, 50], '低': [1, 20] }
          const range: number[] = severityMap[this.severity] || [1, 10]
          return Math.floor(Math.random() * (range[1] - range[0] + 1)) + range[0]
        },
        'risk_score': function(this: MockContext): number {
          const severityMap: Record<string, number[]> = { '严重': [80, 100], '高': [60, 85], '中': [30, 65], '低': [1, 35] }
          const range: number[] = severityMap[this.severity] || [1, 50]
          return Math.floor(Math.random() * (range[1] - range[0] + 1)) + range[0]
        },
        'analyst': function(): string {
          const analysts = ['张安全', '李防护', '王监控', '赵分析', '刘响应', '陈检测', '杨调查', '周处理']
          return analysts[eventId % analysts.length]
        },
        'tags': function(): string[] {
          const tagGroups = [
            ['恶意软件', '木马', '远程控制', '数据窃取', '横向移动'],
            ['DDoS', '流量攻击', '服务中断', '僵尸网络'],
            ['数据泄露', '敏感信息', '隐私泄露', '合规风险'],
            ['异常登录', '暴力破解', '账户安全', '身份验证'],
            ['网络入侵', 'APT攻击', '横向移动', '权限提升'],
            ['系统漏洞', '代码执行', '权限提升', '补丁管理'],
            ['APT攻击', '高级威胁', '持续威胁', '定向攻击'],
            ['勒索软件', '文件加密', '赎金要求', '业务中断'],
            ['钓鱼攻击', '社会工程', '凭据窃取', '邮件安全'],
            ['内网横移', '权限扩展', '网络渗透', '资产发现']
          ]
          return tagGroups[eventId % tagGroups.length]
        },
        'timeline|5-10': [
          {
            'timestamp': '@datetime',
            'action': '@csentence(5, 15)',
            'user': '@cname'
          }
        ],
        'related_events|2-5': [
          {
            'id|+1': 1,
            'title': '@ctitle(8, 20)',
            'type|1': ['入侵检测', '恶意软件', '异常行为'],
            'time': '@datetime'
          }
        ],
        'indicators': {
          'ip_addresses|2-5': ['@ip'],
          'domains|1-3': ['@domain'],
          'file_hashes|1-3': ['@string("lower", 32)'],
          'urls|1-2': ['@url']
        },
        'mitigation_actions|3-6': ['@csentence(10, 25)'],
        'evidence_files|2-4': [
          {
            'filename': '@word.@word',
            'size': '@integer(1024, 1048576)',
            'hash': '@string("lower", 32)',
            'upload_time': '@datetime'
          }
        ]
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取安全事件统计数据
  {
    url: '/api/security-events/stats',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'total_events': '@integer(1000, 5000)',
        'new_events': '@integer(50, 200)',
        'in_progress_events': '@integer(30, 150)',
        'resolved_events': '@integer(800, 4000)',
        'false_positives': '@integer(100, 500)',
        'severity_distribution': {
          '严重': '@integer(20, 100)',
          '高': '@integer(50, 200)',
          '中': '@integer(200, 800)',
          '低': '@integer(300, 1000)'
        },
        'type_distribution': {
          '入侵检测': '@integer(200, 800)',
          '恶意软件': '@integer(100, 400)',
          '异常行为': '@integer(150, 600)',
          '数据泄露': '@integer(50, 200)',
          '网络攻击': '@integer(100, 500)',
          '系统异常': '@integer(80, 300)'
        },
        'hourly_events|24': [
          {
            'hour|+1': 0,
            'count': '@integer(5, 50)'
          }
        ],
        'daily_events|7': [
          {
            'day': '@date("MM-dd")',
            'count': '@integer(50, 200)'
          }
        ],
        'top_sources|10': [
          {
            'ip': '@ip',
            'count': '@integer(10, 100)',
            'country|1': ['中国', '美国', '俄罗斯', '德国', '英国']
          }
        ]
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 更新安全事件状态
  {
    url: '/api/security-events/:id/status',
    method: 'put',
    response: ({ query, body }: { query: any; body: any }) => {
      return {
        code: 200,
        message: '事件状态更新成功',
        data: {
          id: query.id,
          status: body.status,
          updated_at: new Date().toISOString(),
          updated_by: body.analyst || '@cname'
        }
      };
    }
  }
] as MockMethod[];
