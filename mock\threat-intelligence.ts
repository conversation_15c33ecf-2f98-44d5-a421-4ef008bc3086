import type { MockMethod } from 'vite-plugin-mock';
import Mock from 'mockjs';

// 威胁情报数据模拟
export default [
  // 获取威胁情报列表
  {
    url: '/api/threat-intelligence',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'list|10-20': [
          {
            'id|+1': 1,
            'title': '@ctitle(10, 30)',
            'type|1': ['恶意软件', '钓鱼攻击', 'DDoS攻击', '数据泄露', '勒索软件', 'APT攻击'],
            'severity|1': ['高', '中', '低'],
            'source|1': ['内部监测', '第三方情报', '开源情报', '合作伙伴'],
            'status|1': ['待处理', '处理中', '已处理', '已关闭'],
            'description': '@cparagraph(1, 3)',
            'indicators': {
              'ip|1-5': ['@ip'],
              'domain|1-3': ['@domain'],
              'hash|1-2': ['@string("lower", 32)']
            },
            'tags|2-5': ['@word'],
            'confidence|60-100': 60,
            'created_at': '@datetime',
            'updated_at': '@datetime',
            'analyst': '@cname'
          }
        ],
        total: '@integer(50, 200)',
        page: 1,
        pageSize: 20
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取威胁情报详情
  {
    url: '/api/threat-intelligence/:id',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const data = Mock.mock({
        'id': query.id,
        'title': '@ctitle(10, 30)',
        'type|1': ['恶意软件', '钓鱼攻击', 'DDoS攻击', '数据泄露', '勒索软件', 'APT攻击'],
        'severity|1': ['高', '中', '低'],
        'source|1': ['内部监测', '第三方情报', '开源情报', '合作伙伴'],
        'status|1': ['待处理', '处理中', '已处理', '已关闭'],
        'description': '@cparagraph(3, 5)',
        'detailed_analysis': '@cparagraph(5, 10)',
        'indicators': {
          'ip|3-8': ['@ip'],
          'domain|2-5': ['@domain'],
          'hash|2-4': ['@string("lower", 32)'],
          'url|1-3': ['@url']
        },
        'tags|3-8': ['@word'],
        'confidence|60-100': 60,
        'created_at': '@datetime',
        'updated_at': '@datetime',
        'analyst': '@cname',
        'related_incidents|2-5': [
          {
            'id|+1': 1,
            'title': '@ctitle(8, 20)',
            'date': '@date'
          }
        ],

      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取威胁情报统计数据
  {
    url: '/api/threat-intelligence/stats',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'total_threats': '@integer(1000, 5000)',
        'active_threats': '@integer(50, 200)',
        'resolved_threats': '@integer(800, 4000)',
        'high_severity': '@integer(20, 100)',
        'medium_severity': '@integer(100, 500)',
        'low_severity': '@integer(200, 1000)',
        'threat_types': {
          '恶意软件': '@integer(100, 500)',
          '钓鱼攻击': '@integer(80, 400)',
          'DDoS攻击': '@integer(50, 200)',
          '数据泄露': '@integer(30, 150)',
          '勒索软件': '@integer(40, 180)',
          'APT攻击': '@integer(20, 100)'
        },
        'monthly_trends|12': [
          {
            'month': '@date("MM")',
            'count': '@integer(50, 200)'
          }
        ]
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 创建威胁情报
  {
    url: '/api/threat-intelligence',
    method: 'post',
    response: ({ body }: { body: any }) => {
      return {
        code: 200,
        message: '威胁情报创建成功',
        data: {
          id: Mock.mock('@integer(1000, 9999)'),
          ...body,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      };
    }
  },

  // 更新威胁情报
  {
    url: '/api/threat-intelligence/:id',
    method: 'put',
    response: ({ query, body }: { query: any; body: any }) => {
      return {
        code: 200,
        message: '威胁情报更新成功',
        data: {
          id: query.id,
          ...body,
          updated_at: new Date().toISOString()
        }
      };
    }
  },

  // 删除威胁情报
  {
    url: '/api/threat-intelligence/:id',
    method: 'delete',
    response: () => {
      return {
        code: 200,
        message: '威胁情报删除成功'
      };
    }
  }
] as MockMethod[];
