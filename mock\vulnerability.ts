import type { MockMethod } from 'vite-plugin-mock';
import Mock from 'mockjs';

// 漏洞管理数据模拟
export default [
  // 获取漏洞列表
  {
    url: '/api/vulnerabilities',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'list|15-25': [
          {
            'id|+1': 1,
            'cve_id': 'CVE-@date("yyyy")-@integer(1000, 9999)',
            'title': '@ctitle(15, 40)',
            'severity|1': ['严重', '高', '中', '低'],
            'cvss_score': '@float(0, 10, 1, 1)',
            'category|1': ['远程代码执行', 'SQL注入', '跨站脚本', '权限提升', '信息泄露', '拒绝服务'],
            'affected_systems|2-5': ['@word'],
            'vendor': '@company',
            'product': '@word',
            'version': '@semver',
            'description': '@cparagraph(2, 4)',
            'status|1': ['新发现', '已确认', '修复中', '已修复', '已忽略'],
            'published_date': '@date',
            'discovered_date': '@date',
            'last_modified': '@datetime',
            'exploit_available|1': [true, false],
            'patch_available|1': [true, false],
            'references|2-4': ['@url'],
            'tags|2-6': ['@word']
          }
        ],
        total: '@integer(100, 500)',
        page: 1,
        pageSize: 20
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取漏洞详情
  {
    url: '/api/vulnerabilities/:id',
    method: 'get',
    response: ({ query }: { query: any }) => {
      const data = Mock.mock({
        'id': query.id,
        'cve_id': 'CVE-@date("yyyy")-@integer(1000, 9999)',
        'title': '@ctitle(15, 40)',
        'severity|1': ['严重', '高', '中', '低'],
        'cvss_score': '@float(0, 10, 1, 1)',
        'cvss_vector': 'CVSS:3.1/AV:@word/AC:@word/PR:@word/UI:@word/S:@word/C:@word/I:@word/A:@word',
        'category|1': ['远程代码执行', 'SQL注入', '跨站脚本', '权限提升', '信息泄露', '拒绝服务'],
        'affected_systems|3-8': ['@word'],
        'vendor': '@company',
        'product': '@word',
        'version': '@semver',
        'description': '@cparagraph(3, 6)',
        'technical_details': '@cparagraph(5, 10)',
        'impact_analysis': '@cparagraph(3, 5)',
        'status|1': ['新发现', '已确认', '修复中', '已修复', '已忽略'],
        'published_date': '@date',
        'discovered_date': '@date',
        'last_modified': '@datetime',
        'exploit_available|1': [true, false],
        'patch_available|1': [true, false],
        'patch_info': {
          'patch_id': '@word',
          'patch_url': '@url',
          'patch_date': '@date',
          'patch_description': '@csentence(10, 30)'
        },
        'references|3-6': ['@url'],
        'tags|3-8': ['@word'],
        'affected_assets|5-15': [
          {
            'asset_id|+1': 1,
            'asset_name': '@word',
            'asset_type|1': ['服务器', '网络设备', '应用程序', '数据库'],
            'risk_level|1': ['高', '中', '低']
          }
        ],
        'remediation_steps|4-8': ['@csentence(15, 30)']
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  },

  // 获取漏洞统计数据
  {
    url: '/api/vulnerabilities/stats',
    method: 'get',
    response: () => {
      const data = Mock.mock({
        'total_vulnerabilities': '@integer(500, 2000)',
        'critical_vulnerabilities': '@integer(20, 100)',
        'high_vulnerabilities': '@integer(50, 200)',
        'medium_vulnerabilities': '@integer(100, 500)',
        'low_vulnerabilities': '@integer(200, 800)',
        'patched_vulnerabilities': '@integer(300, 1500)',
        'unpatched_vulnerabilities': '@integer(100, 500)',
        'severity_distribution': {
          '严重': '@integer(20, 100)',
          '高': '@integer(50, 200)',
          '中': '@integer(100, 500)',
          '低': '@integer(200, 800)'
        },
        'category_distribution': {
          '远程代码执行': '@integer(50, 200)',
          'SQL注入': '@integer(30, 150)',
          '跨站脚本': '@integer(40, 180)',
          '权限提升': '@integer(25, 120)',
          '信息泄露': '@integer(35, 160)',
          '拒绝服务': '@integer(20, 100)'
        },
        'monthly_trends|12': [
          {
            'month': '@date("MM")',
            'discovered': '@integer(10, 50)',
            'patched': '@integer(5, 40)'
          }
        ]
      });

      return {
        code: 200,
        message: 'success',
        data: data
      };
    }
  }
] as MockMethod[];
