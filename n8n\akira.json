{"group": "a<PERSON>ra", "description": "The Akira ransomware group is said to have emerged in March 2023, and there's much speculation about its ties to the former CONTI ransomware group.<br> <br> It's worth noting that with the end of CONTI's operation, several affiliates migrated to independent campaigns such as Royal, BlackBasta, and others.<br> <br> According to some reports, Akira affiliates also work with other ransomware operations, such as Snatch and BlackByte, as an open directory of tools used by an Akira operator was identified, which also had connections to the Snatch ransomware.<br> <br> The first version of the Akira ransomware was written in C++ and appended files with the '.akira' extension, creating a ransom note named 'akira_readme.txt,' partially based on the Conti V2 source code. However, on June 29, 2023, a decryptor for this version was reportedly released by Avast.<br> <br> Subsequently, a version was released that fixed the decryption flaw on July 2, 2023. Since then, the new version is said to be written in Rust, this time called 'megazord.exe,' and it changes the extension to '.powerranges' for encrypted files.<br> <br> Most of <PERSON>'s initial access vectors use brute-force attempts on Cisco VPN devices (which use single-factor authentication only).<br> Additionally, exploitation of CVEs: CVE-2019-6693 and CVE-2022-40684 for initial access has been identified.<BR>Source: https://github.com/crocodyli/ThreatActors-TTPs", "added_date": null, "victims": 874, "firstseen": "2023-04-12", "lastseen": "2025-07-18", "ttps": [{"tactic_id": "TA0001", "tactic_name": "Initial Access", "techniques": [{"technique_name": "Valid Accounts", "technique_id": "T1078", "technique_details": "Utilizes compromised VPN credentials."}, {"technique_name": "Valid Accounts: Domain Accounts", "technique_id": "T1078.002", "technique_details": "Operators use obtained domain accounts for access."}, {"technique_name": "External Remote Services", "technique_id": "T1133", "technique_details": "Actors exploit CVE-2023-20269 remote service vulnerabilities."}, {"technique_name": "Exploit Public-Facing Application", "technique_id": "T1190", "technique_details": "Targets vulnerable CISCO devices via CVE-2023-20269."}]}, {"tactic_id": "TA0003", "tactic_name": "Persistence", "techniques": [{"technique_name": "Create Account: Domain Account", "technique_id": "T1136.002", "technique_details": "Upon initial access, Akira operators create a domain account on the compromised system."}, {"technique_name": "Create Account: Local Account", "technique_id": "T1136.001", "technique_details": "Upon initial access, Akira operators create a local account on the compromised system."}]}, {"tactic_id": "TA0004", "tactic_name": "Privilege Escalation", "techniques": [{"technique_name": "Valid Accounts: Domain Accounts", "technique_id": "T1078.002", "technique_details": "Utilizes valid domain accounts for privilege escalation."}, {"technique_name": "Privilege Escalation", "technique_id": "TA0004", "technique_details": "Utilizes local domain accounts for privilege escalation."}]}, {"tactic_id": "TA0002", "tactic_name": "Execution", "techniques": [{"technique_name": "Command and Scripting Interpreter", "technique_id": "T1059", "technique_details": "Accepts parameters for its routines such as \"-n 10\" (for encryption percentage) or \"-s (filename)\" (for shared folder encryption)."}, {"technique_name": "Command and Scripting Interpreter: PowerShell", "technique_id": "T1059.001", "technique_details": "Operators use PowerShell to launch commands to continue operations."}, {"technique_name": "System Services: Service Execution", "technique_id": "T1059.002", "technique_details": "Akira ransomware uses service execution for persistence."}, {"technique_name": "Command and Scripting Interpreter: Windows Command Shell", "technique_id": "T1059.003", "technique_details": "Operators use CMD to launch commands to continue operations."}, {"technique_name": "Windows Management Instrumentation", "technique_id": "T1047", "technique_details": "Actors may use WMI to continue the attack."}]}, {"tactic_id": "TA0005", "tactic_name": "Defense Evasion", "techniques": [{"technique_name": "Impair Defenses: Disable or Modify Tools", "technique_id": "T1562.001", "technique_details": "Usage of PowerTool or a KillAV tool abusing the Zemana AntiMalware driver to terminate AV-related processes was observed."}, {"technique_name": "Modify Registry", "technique_id": "T1112", "technique_details": "Uses commands in its operation to modify registries."}]}, {"tactic_id": "TA0006", "tactic_name": "Credential Access", "techniques": [{"technique_name": "OS Credential Dumping: LSASS Memory", "technique_id": "T1003.001", "technique_details": "Uses Mimikatz, LaZagne, or a command line to dump LSASS from memory."}]}, {"tactic_id": "TA0007", "tactic_name": "Discovery", "techniques": [{"technique_name": "System Information Discovery", "technique_id": "T1082", "technique_details": "Uses PCHunter and SharpHound to collect system information."}, {"technique_name": "Discovery", "technique_id": "TA0007", "technique_details": "Uses AdFind, Windows net command, and nltest to collect domain information."}, {"technique_name": "Remote System Discovery", "technique_id": "T1018", "technique_details": "Uses Advanced IP Scanner and MASSCAN to discover remote systems."}]}, {"tactic_id": "TA0011", "tactic_name": "Command and Control", "techniques": [{"technique_name": "Remote Access Software", "technique_id": "T1229", "technique_details": "Utilizes AnyDesk, Radmin, Cloudflare Tunnel, MobaXterm, RustDesk, or Ngrok to gain remote access on targeted systems."}]}, {"tactic_id": "TA0008", "tactic_name": "Lateral Movement", "techniques": [{"technique_name": "Lateral Tool Transfer", "technique_id": "T1570", "technique_details": "Uses RDP to move laterally within the victim's network."}, {"technique_name": "Remote Services: Remote Desktop Protocol", "technique_id": "T1021.001", "technique_details": "Utilizes remote services for accessing accounts and machines through remote services."}]}, {"tactic_id": "TA0009", "tactic_name": "Collection", "techniques": [{"technique_name": "Archive Collected Data: Archive via Utility", "technique_id": "T1560.001", "technique_details": "Utilizes discovery to gather information for exfiltration."}]}, {"tactic_id": "TA0010", "tactic_name": "Exfiltration", "techniques": [{"technique_name": "Exfiltration Over Web Service: Exfiltration to Cloud Storage", "technique_id": "T1567.002", "technique_details": "Uses RClone to exfiltrate stolen information via a web service."}, {"technique_name": "Exfiltration Over Alternative Protocol: Exfiltration Over Unencrypted Non-C2 Protocol", "technique_id": "T1048.003", "technique_details": "Utilizes FileZilla or WinSCP to exfiltrate stolen information via FTP."}]}, {"tactic_id": "TA0040", "tactic_name": "Impact", "techniques": [{"technique_name": "Inhibit System Recovery", "technique_id": "T1490", "technique_details": "Deletes shadow copies to inhibit recovery."}, {"technique_name": "Data Encrypted for Impact", "technique_id": "T1486", "technique_details": "Akira ransomware is used to encrypt files."}]}], "vulnerabilities": [{"Vendor": "Cisco", "Product": "ASA & FTD", "CVE": "CVE-2023-20269", "CVSS": 5, "severity": "MEDIUM"}, {"Vendor": "Cisco", "Product": "ASA & FTD", "CVE": "CVE-2023-20263", "CVSS": 4.7, "severity": "MEDIUM"}, {"Vendor": "Cisco", "Product": "ASA & FTD", "CVE": "CVE-2020-3259", "CVSS": 7.5, "severity": "HIGH"}, {"Vendor": "Fortinet", "Product": "FortiOS", "CVE": "CVE-2022-40684", "CVSS": 9.8, "severity": "CRITICAL"}, {"Vendor": "Fortinet", "Product": "FortiOS", "CVE": "CVE-2019-6693", "CVSS": 6.5, "severity": "MEDIUM"}, {"Vendor": "Fortinet", "Product": "FortiClient", "CVE": "CVE-2023-48788", "CVSS": 9.8, "severity": "CRITICAL"}, {"Vendor": "SonicWall", "Product": "SonicOS SSL-VPN", "CVE": "CVE-2024-40766", "CVSS": 9.8, "severity": "CRITICAL"}, {"Vendor": "<PERSON><PERSON><PERSON>", "Product": "Backup & Replication", "CVE": "CVE-2024-40711", "CVSS": 9.8, "severity": "CRITICAL"}, {"Vendor": "<PERSON><PERSON><PERSON>", "Product": "Backup & Replication", "CVE": "CVE-2023-27532", "CVSS": 7.5, "severity": "HIGH"}, {"Vendor": "VMware", "Product": "ESXi", "CVE": "CVE-2024-37085 (\"ESX Admins\")", "CVSS": 6.8, "severity": "MEDIUM"}, {"Vendor": "VMware", "Product": "vSphere Client", "CVE": "CVE-2021-21972", "CVSS": 9.8, "severity": "CRITICAL"}], "tools": {"Exfiltration": ["FileZilla", "MEGA", "<PERSON><PERSON>", "Temp[.]sh", "WinSCP"], "RMM-Tools": ["AnyDesk", "MobaXterm", "<PERSON><PERSON><PERSON>", "RustDesk"], "DiscoveryEnum": ["Advanced IP Scanner", "Masscan", "ReconFTW", "SharpHound", "SoftPerfect NetScan"], "Networking": ["Cloudflared", "OpenSSH", "<PERSON><PERSON>"], "CredentialTheft": ["DonPAPI", "LaZagne", "Mimikatz"], "Offsec": ["Impacket"], "DefenseEvasion": ["PowerTool", "<PERSON><PERSON><PERSON>-Rootkit driver"], "LOLBAS": []}, "locations": [{"fqdn": "akiral2iz6a7qgd3ayp3l6yub7xx2uep76idk3u2kollpj5z3z636bad.onion", "title": "", "slug": "https://akiral2iz6a7qgd3ayp3l6yub7xx2uep76idk3u2kollpj5z3z636bad.onion", "available": true, "updated": "2025-07-21 01:01:23.096146", "lastscrape": "2025-07-21 01:01:23.096146", "type": "DLS"}, {"fqdn": "akiralkzxzq2dsrzsrvbr2xgbbu2wgsmxryd4csgfameg52n7efvr2id.onion", "title": "/", "slug": "https://akiralkzxzq2dsrzsrvbr2xgbbu2wgsmxryd4csgfameg52n7efvr2id.onion", "available": false, "updated": "2025-07-20 23:01:28.418512", "lastscrape": "2025-07-21 01:01:53.135102", "type": "Cha<PERSON>"}], "has_negotiations": true, "negotiation_count": 61, "has_ransomnote": true, "ransomnotes_count": 2, "url": "https://www.ransomware.live/group/akira"}