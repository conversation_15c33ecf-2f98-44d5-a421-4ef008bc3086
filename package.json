{"name": "frontend", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/node": "^9.2.2", "@astrojs/react": "^4.3.0", "@astrojs/vue": "^5.1.0", "@tailwindcss/vite": "^4.1.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vueuse/core": "^13.3.0", "apexcharts": "^4.7.0", "astro": "^5.10.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "dayjs": "^1.11.13", "flyonui": "^2.2.0", "framer-motion": "^12.18.1", "highlight.js": "^11.11.1", "lodash": "^4.17.21", "lucide-react": "^0.516.0", "lucide-vue-next": "^0.515.0", "markdown-it": "^14.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "reka-ui": "^2.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vue": "^3.5.16"}, "packageManager": "pnpm@10.6.5+sha512.cdf928fca20832cd59ec53826492b7dc25dc524d4370b6b4adbf65803d32efaa6c1c88147c0ae4e8d579a6c9eec715757b50d4fa35eea179d868eada4ed043af", "devDependencies": {"@iconify-json/tabler": "^1.2.19", "@iconify/tailwind4": "^1.0.6", "@tailwindcss/typography": "^0.5.16", "@types/markdown-it": "^14.1.2", "@types/mockjs": "^1.0.10", "mockjs": "^1.1.0", "vite-plugin-mock": "^3.0.2"}}