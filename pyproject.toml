[project]
name = "threat-intelligence-center"
version = "0.1.0"
description = "威胁情报数据中心"
requires-python = ">=3.12"
dependencies = [
    "django>=5.2.3",
    "djangorestframework>=3.16.0",
    "drf-spectacular>=0.28.0",
    "psycopg2-binary>=2.9.10",
    "python-dotenv>=1.1.0",
    "django-jsoneditor>=0.2.0",
    "django-filter>=25.1",
    "markdown>=3.8",
    "django-mdeditor>=0.1.20",
    "django-cors-headers>=4.6.0",
    "httpx>=0.28.1",
    "alibabacloud-dysmsapi20170525>=3.0.0",
    "gunicorn>=22.0.0",
    "whitenoise>=6.9.0",
    "pillow>=11.3.0",
    "django5-aliyun-oss>=1.1.2",
    "djangorestframework-simplejwt>=5.3.0",
    "loguru>=0.7.3",
    "pyquery>=2.0.1",
]
