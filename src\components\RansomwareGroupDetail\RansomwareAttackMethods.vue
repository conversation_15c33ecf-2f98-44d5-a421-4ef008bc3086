<template>
  <div class="space-y-6">
    <!-- TTPs 信息 -->
    <div v-if="group.ttps && group.ttps.length > 0">
      <h3 class="text-lg font-semibold mb-4">战术技术程序 (TTPs)</h3>
      <div class="space-y-4">
        <div v-for="ttp in group.ttps" :key="ttp.tactic_id"
          class="card bg-base-200/30 border border-base-300/20">
          <div class="card-body p-4">
            <div class="flex items-center gap-3 mb-3">
              <Shield class="h-5 w-5 text-warning" />
              <div>
                <div class="font-medium">{{ ttp.tactic_name }}</div>
                <div class="text-sm text-base-content/60">{{ ttp.tactic_id }}</div>
              </div>
            </div>
            <div v-if="ttp.techniques && ttp.techniques.length > 0" class="space-y-2">
              <div v-for="technique in ttp.techniques" :key="technique.technique_id"
                class="flex items-start gap-2 p-2 bg-base-100/50 rounded">
                <div class="text-xs bg-primary/20 text-primary px-2 py-1 rounded font-mono">
                  {{ technique.technique_id }}
                </div>
                <div class="flex-1">
                  <div class="font-medium text-sm">{{ technique.technique_name }}</div>
                  <div v-if="technique.technique_details" class="text-xs text-base-content/70 mt-1">
                    {{ technique.technique_details }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 漏洞信息 -->
    <div v-if="group.vulnerabilities && group.vulnerabilities.length > 0">
      <h3 class="text-lg font-semibold mb-4">利用的漏洞</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <div v-for="(vuln, index) in group.vulnerabilities" :key="index"
          class="card bg-base-200/30 border border-base-300/20">
          <div class="card-body p-3">
            <div class="flex items-center justify-between mb-2">
              <span v-if="vuln.cve" class="font-mono text-sm font-medium">{{ vuln.cve }}</span>
              <span v-else class="text-sm text-base-content/50">未知CVE</span>
              <span :class="getSeverityBadgeClass(vuln.severity)">
                {{ vuln.severity }}
              </span>
            </div>
            <div v-if="vuln.vendor || vuln.product" class="text-xs text-base-content/70">
              <div v-if="vuln.vendor">厂商: {{ vuln.vendor }}</div>
              <div v-if="vuln.product">产品: {{ vuln.product }}</div>
            </div>
            <div v-if="vuln.cvss" class="text-xs text-base-content/60 mt-1">
              CVSS: {{ vuln.cvss }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用的工具 -->
    <div v-if="group.tools_used && Object.keys(group.tools_used).length > 0">
      <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
        使用的工具
      </h3>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div v-for="(toolCategory, index) in toolsArray" :key="toolCategory.category"
          class="bg-base-200/30 rounded-lg p-4 border border-base-300/20">
          <h4 class="font-semibold text-sm text-base-content/80 border-b border-base-300/30 pb-2 mb-3">{{ toolCategory.category }}
          </h4>
          <div class="flex flex-wrap gap-2">
            <span v-for="tool in toolCategory.tools" :key="tool"
              :class="['badge badge-soft badge-sm text-xs', getBadgeClass(index)]">
              {{ tool }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Shield } from 'lucide-vue-next'
import type { RansomwareGroup } from '@/types/api'

interface Props {
  group: RansomwareGroup
}

const props = defineProps<Props>()

const toolsArray = computed(() => {
  if (!props.group.tools_used) return []

  // 将工具分类转换为数组
  return Object.entries(props.group.tools_used).map(([category, tools]) => ({
    category,
    tools
  }))
})

const getBadgeClass = (index: number) => {
  const classes = [
    'badge-soft',
    'badge-primary',
    'badge-secondary',
    'badge-accent',
    'badge-info',
    'badge-success',
    'badge-warning',
    'badge-error'
  ]
  return classes[index % classes.length]
}

const getSeverityBadgeClass = (severity: string) => {
  switch (severity?.toUpperCase()) {
    case 'CRITICAL':
      return 'badge badge-error'
    case 'HIGH':
      return 'badge badge-warning'
    case 'MEDIUM':
      return 'badge badge-info'
    case 'LOW':
      return 'badge badge-success'
    default:
      return 'badge badge-ghost'
  }
}
</script>
