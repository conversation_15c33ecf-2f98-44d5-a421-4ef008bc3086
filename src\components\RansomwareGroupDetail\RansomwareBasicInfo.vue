<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold mb-3">组织描述</h3>
      <div class="prose prose-sm max-w-none text-base-content/80 leading-relaxed" v-html="parsedDescription"></div>
    </div>

    <!-- 外部信息来源 -->
    <div v-if="group.external_information_source && group.external_information_source.length > 0">
      <div class="flex items-center gap-2 mb-4">
        <h3 class="text-lg font-semibold text-base-content">外部信息来源</h3>
      </div>
      <div class="bg-base-200/30 rounded-lg p-4 border border-base-300/20">
        <div class="md-container space-y-3" v-html="html"></div>
        <!-- <ul class="space-y-3">
          <li v-for="source in group.external_sources" :key="source.id" class="flex items-start gap-3">
            <span class="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
            <a :href="source.url" target="_blank" rel="noopener noreferrer"
              class="text-blue-600 hover:text-blue-700 underline text-sm leading-relaxed break-all transition-colors duration-200">
              {{ source.title }}
            </a>
          </li>
        </ul> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RansomwareGroup } from '@/types/api'
import { useMarkdown } from '@/lib/markdown'
import { computed } from 'vue';

interface Props {
  group: RansomwareGroup
}

const props = defineProps<Props>()

// 初始化markdown解析器
const { smartParse } = useMarkdown()

const html = computed(() => {
  return smartParse(props.group.external_information_source ?? '')
})

const parsedDescription = computed(() => {
  return smartParse(props.group.description ?? '')
})
</script>