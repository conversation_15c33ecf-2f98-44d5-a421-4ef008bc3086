<template>
  <div ref="toolsContainer" class="space-y-6">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg text-primary"></span>
    </div>

    <!-- 标题和描述 -->
    <div v-else class="mb-6">
      <h3 class="text-xl font-semibold text-base-content mb-2">应急工具</h3>
      <p class="text-base-content/70">
        针对该勒索软件组织的应急响应工具和资源，包括恢复工具、检测脚本和防护措施。
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <Wrench class="h-5 w-5 text-primary" />
            </div>
            <div>
              <div class="text-2xl font-bold text-base-content">{{ totalTools }}</div>
              <div class="text-sm text-base-content/60">总工具数</div>
            </div>
          </div>
        </div>
      </div>

      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-info/10 flex items-center justify-center">
              <FileText class="h-5 w-5 text-info" />
            </div>
            <div>
              <div class="text-2xl font-bold text-base-content">{{ toolsWithDocs }}</div>
              <div class="text-sm text-base-content/60">有文档工具</div>
            </div>
          </div>
        </div>
      </div>

      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-warning/10 flex items-center justify-center">
              <Clock class="h-5 w-5 text-warning" />
            </div>
            <div>
              <div class="text-2xl font-bold text-base-content">{{ recentlyUpdated }}</div>
              <div class="text-sm text-base-content/60">近期更新</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具列表 -->
    <div class="card bg-base-100 border border-gray-200/20">
      <div class="card-body p-0">
        <!-- 工具列表头部 -->
        <div class="p-4 sm:p-6 border-b border-gray-200/20">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h4 class="text-lg font-semibold text-base-content">工具列表</h4>
            <div class="flex items-center gap-2">
              <div class="form-control">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索工具..."
                  class="input input-bordered input-sm w-full max-w-xs"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 工具卡片列表 -->
        <div v-if="filteredTools.length > 0" class="p-4 sm:p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="tool in filteredTools"
              :key="tool.id"
              class="card bg-base-200/30 border border-base-300/20 hover:shadow-md transition-shadow duration-200"
            >
              <div class="card-body p-4">
                <!-- 工具头部 -->
                <div class="flex items-start justify-between mb-3">
                  <div class="flex items-center gap-2">
                    <div class="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center">
                      <Wrench class="h-4 w-4 text-primary" />
                    </div>
                    <h5 class="font-semibold text-base-content truncate">{{ tool.name }}</h5>
                  </div>
                  <div class="flex items-center gap-1">
                    <span v-if="tool.file" class="badge badge-success badge-xs">可下载</span>
                  </div>
                </div>

                <!-- 工具描述 -->
                <div class="mb-4">
                  <p class="text-sm text-base-content/70 line-clamp-3">
                    {{ getToolDescription(tool.description) }}
                  </p>
                </div>

                <!-- 工具操作 -->
                <div class="flex items-center justify-between">
                  <div class="text-xs text-base-content/50">
                    {{ formatDate(tool.updated_at) }}
                  </div>
                  <div class="flex items-center gap-2">
                    <a
                      :href="`/tools/${tool.id}`"
                      target="_blank"
                      class="btn btn-outline btn-xs"
                    >
                      查看
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-12">
          <Wrench class="h-16 w-16 mx-auto text-base-content/30 mb-4" />
          <h3 class="text-lg font-medium text-base-content/70 mb-2">暂无应急工具</h3>
          <p class="text-base-content/50">
            该组织暂无相关的应急响应工具
          </p>
        </div>

        <!-- 分页组件 -->
        <Pagination
          v-if="pagination.total_count > pageSize"
          :current-page="currentPage"
          :total-pages="totalPages"
          :total-items="pagination.total_count"
          :show-info="true"
          :use-events="true"
          @page-change="handlePageChange"
        />
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Wrench,
  FileText,
  Clock
} from 'lucide-vue-next'
import dayjs from 'dayjs'
import { useToast } from '@/composables/useToast'
import { ransomwareGroupApi } from '@/lib/api/security'
import Pagination from '@/components/ui/Pagination.vue'
import { onMounted } from 'vue'

interface Props {
  groupSlug: string
}

const props = defineProps<Props>()

// Toast功能
const { showError } = useToast()

// 模板引用
const toolsContainer = ref<HTMLElement>()

// 响应式数据
const searchQuery = ref('')

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(12) // 每页显示12个工具
const loading = ref(false)

// 工具数据状态
const tools = ref<any[]>([])
const pagination = ref({
  current_page: 1,
  page_size: 12,
  total_count: 0,
  total_pages: 0,
  has_next: false,
  has_prev: false
})

// 获取工具数据
const loadTools = async (page: number = 1) => {
  try {
    loading.value = true
    const response = await ransomwareGroupApi.getTools(props.groupSlug, {
      page,
      page_size: pageSize.value
    })

    tools.value = response.tools
    pagination.value = response.pagination
    currentPage.value = page
  } catch (error: any) {
    showError(error || '加载应急工具失败')
    console.error('加载应急工具失败:', error)
  } finally {
    loading.value = false
  }
}

// 滚动到工具列表顶部
const scrollToTop = () => {
  if (toolsContainer.value) {
    toolsContainer.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 分页控制方法
const handlePageChange = async (page: number) => {
  await loadTools(page)
  // 翻页后滚动到列表顶部
  setTimeout(() => {
    scrollToTop()
  }, 100) // 稍微延迟确保数据已渲染
}

// 计算属性
const totalPages = computed(() => pagination.value.total_pages)

const totalTools = computed(() => pagination.value.total_count)

const toolsWithDocs = computed(() =>
  tools.value.filter(tool => tool.content && tool.content.trim().length > 0).length
)

const recentlyUpdated = computed(() => {
  const thirtyDaysAgo = dayjs().subtract(30, 'day')
  return tools.value.filter(tool => 
    dayjs(tool.updated_at).isAfter(thirtyDaysAgo)
  ).length
})

const filteredTools = computed(() => {
  if (!searchQuery.value.trim()) {
    return tools.value
  }
  
  const query = searchQuery.value.toLowerCase()
  return tools.value.filter(tool =>
    tool.name.toLowerCase().includes(query) ||
    tool.content.toLowerCase().includes(query)
  )
})

// 方法
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const getToolDescription = (content: string) => {
  if (!content) return '暂无描述'

  // 移除HTML标签并截取前100个字符
  const plainText = content.replace(/<[^>]*>/g, '')
  return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText
}

// 组件挂载时加载数据
onMounted(() => {
  loadTools(1)
})


</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.prose {
  color: inherit;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: inherit;
}
</style>
