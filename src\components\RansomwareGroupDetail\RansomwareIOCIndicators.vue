<template>
  <div class="space-y-4 sm:space-y-6 relative">
    <!-- IOC指标统计 -->
    <div class="card bg-base-100 border border-gray-200/20">
      <div class="card-body p-4 sm:p-6">
        <h3 class="text-base sm:text-lg font-semibold mb-3 sm:mb-4">IOC指标统计</h3>
        <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
          <div class="text-center p-3 sm:p-4 bg-base-200/30 rounded-lg">
            <div
              class="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
              <Shield class="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-primary" />
            </div>
            <div class="text-lg sm:text-xl md:text-2xl font-bold text-primary">{{ getAllIOCCount('hash') }}</div>
            <div class="text-xs sm:text-sm text-base-content/60">哈希</div>
          </div>
          <div class="text-center p-3 sm:p-4 bg-base-200/30 rounded-lg">
            <div
              class="w-10 h-10 sm:w-12 sm:h-12 bg-warning/10 rounded-full flex items-center justify-center mx-auto mb-2">
              <Target class="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-warning" />
            </div>
            <div class="text-lg sm:text-xl md:text-2xl font-bold text-warning">{{ getAllIOCCount('ip') }}</div>
            <div class="text-xs sm:text-sm text-base-content/60">IP地址</div>
          </div>
          <div class="text-center p-3 sm:p-4 bg-base-200/30 rounded-lg">
            <div
              class="w-10 h-10 sm:w-12 sm:h-12 bg-info/10 rounded-full flex items-center justify-center mx-auto mb-2">
              <FileText class="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-info" />
            </div>
            <div class="text-lg sm:text-xl md:text-2xl font-bold text-info">{{ getAllIOCCount('domain') }}</div>
            <div class="text-xs sm:text-sm text-base-content/60">文件路径</div>
          </div>
          <div class="text-center p-3 sm:p-4 bg-base-200/30 rounded-lg">
            <div
              class="w-10 h-10 sm:w-12 sm:h-12 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-2">
              <Settings class="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-secondary" />
            </div>
            <div class="text-lg sm:text-xl md:text-2xl font-bold text-secondary">{{ getAllIOCCount('url') }}</div>
            <div class="text-xs sm:text-sm text-base-content/60">URL</div>
          </div>
        </div>
      </div>
    </div>

    <!-- IOC指标列表 -->
    <div class="card bg-base-100 border border-gray-200/20">
      <div class="card-body p-4 sm:p-6">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4">
          <h3 class="text-base sm:text-lg font-semibold">IOC指标列表</h3>
          <div class="flex items-center gap-1 sm:gap-2 overflow-x-auto">
            <div class="flex gap-1 sm:gap-2 min-w-max">
              <button v-for="filter in iocFilters" :key="filter.id" @click="activeIOCFilter = filter.id" :class="[
                'btn btn-xs sm:btn-sm whitespace-nowrap min-h-[36px] sm:min-h-[40px]',
                activeIOCFilter === filter.id ? 'btn-primary' : 'btn-outline'
              ]">
                <span class="text-xs sm:text-sm">{{ filter.label }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 表格头部 -->
        <div class="overflow-x-auto">
          <table class="table table-zebra w-full min-w-[600px]">
            <thead>
              <tr>
                <th class="w-16 sm:w-20 text-xs sm:text-sm">类型</th>
                <th class="text-xs sm:text-sm">指标值</th>
                <th class="w-20 sm:w-24 text-xs sm:text-sm">状态</th>
                <th class="w-24 sm:w-32 text-xs sm:text-sm">首次发现</th>
                <th class="w-24 sm:w-32 text-xs sm:text-sm">来源</th>
                <th class="w-12 sm:w-16 text-xs sm:text-sm">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="ioc in getFilteredIOCs()" :key="ioc.value" class="hover">
                <td>
                  <span class="badge badge-xs sm:badge-sm" :class="getIOCTypeBadgeClass(ioc.type)">
                    <span class="text-xs">{{ ioc.type }}</span>
                  </span>
                </td>
                <td>
                  <div class="flex flex-col">
                    <code class="font-mono text-xs sm:text-sm break-all">{{ ioc.value }}</code>
                    <span v-if="ioc.description" class="text-xs text-base-content/60 mt-1">
                      {{ ioc.description }}
                    </span>
                  </div>
                </td>
                <td>
                  <span class="badge badge-xs sm:badge-sm" :class="getIOCStatusBadgeClass(ioc.status)">
                    <span class="text-xs">{{ ioc.status }}</span>
                  </span>
                </td>
                <td class="text-xs sm:text-sm text-base-content/70">{{ ioc.first_seen }}</td>
                <td class="text-xs sm:text-sm text-base-content/70">{{ ioc.source }}</td>
                <td>
                  <button @click="copyToClipboard(ioc.value)" class="btn btn-xs btn-ghost min-h-[32px] sm:min-h-[36px]"
                    title="复制">
                    <Copy class="h-3 w-3" />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Shield, Target, FileText, Settings, Copy } from 'lucide-vue-next'
import { useToast } from '@/composables/useToast'

interface Props {
  iocIndicators: any[]
}

const props = defineProps<Props>()

// 响应式数据
const activeIOCFilter = ref('全部')

// Toast功能
const { showSuccess, showError } = useToast()

// IOC过滤器配置
const iocFilters = [
  { id: '全部', label: '全部' },
  { id: '域名', label: '域名' },
  { id: 'IP地址', label: 'IP地址' },
  { id: 'URL', label: 'URL' },
  { id: 'hash', label: 'hash' }
]

// 获取所有IOC数量
const getAllIOCCount = (type: string) => {
  if (!props.iocIndicators || !Array.isArray(props.iocIndicators)) return 0

  let count = 0
  props.iocIndicators.forEach(indicator => {
    if (indicator.iocs && indicator.iocs[type]) {
      count += Array.isArray(indicator.iocs[type]) ? indicator.iocs[type].length : 0
    }
  })
  return count
}

// 获取过滤后的IOC数据
const getFilteredIOCs = () => {
  if (!props.iocIndicators || !Array.isArray(props.iocIndicators)) return []

  const allIOCs: any[] = []

  props.iocIndicators.forEach(indicator => {
    if (indicator.iocs) {
      Object.entries(indicator.iocs).forEach(([type, values]) => {
        if (Array.isArray(values)) {
          values.forEach(value => {
            allIOCs.push({
              type,
              value,
              status: '活跃' // 默认状态，可以根据实际数据调整
            })
          })
        }
      })
    }
  })

  if (activeIOCFilter.value === '全部') {
    return allIOCs
  }

  const filterMap: Record<string, string> = {
    '域名': 'domain',
    'IP地址': 'ip',
    'URL': 'url',
    'hash': 'hash'
  }

  const filterType = filterMap[activeIOCFilter.value]
  return allIOCs.filter(ioc => ioc.type === filterType)
}

// 获取IOC类型徽章样式
const getIOCTypeBadgeClass = (type: string) => {
  switch (type) {
    case 'url': return 'badge-warning'
    case 'hash': return 'badge-info'
    case 'ip': return 'badge-error'
    case 'domain': return 'badge-primary'
    default: return 'badge-neutral'
  }
}

// 获取IOC状态徽章样式
const getIOCStatusBadgeClass = (status: string) => {
  switch (status) {
    case '活跃': return 'badge-error'
    case '不活跃': return 'badge-success'
    default: return 'badge-neutral'
  }
}

// 复制文本到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    showSuccess('复制成功！')
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案：使用传统的复制方法
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      showSuccess('复制成功！')
    } catch (fallbackErr) {
      console.error('降级复制也失败:', fallbackErr)
      showError('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}
</script>
