<template>
  <div class="space-y-6">
    <!-- 标题和描述 -->
    <div class="mb-6">
      <h3 class="text-xl font-semibold text-base-content mb-2">已知站点</h3>
      <p class="text-base-content/70">
        该勒索软件组织的已知网络站点和基础设施信息，包括域名、服务器和通信渠道。
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
              <Globe class="h-5 w-5 text-primary" />
            </div>
            <div>
              <div class="text-2xl font-bold text-base-content">{{ knownLocations.length }}</div>
              <div class="text-sm text-base-content/60">总站点数</div>
            </div>
          </div>
        </div>
      </div>

      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-success/10 flex items-center justify-center">
              <CheckCircle class="h-5 w-5 text-success" />
            </div>
            <div>
              <div class="text-2xl font-bold text-base-content">{{ activeLocations }}</div>
              <div class="text-sm text-base-content/60">活跃站点</div>
            </div>
          </div>
        </div>
      </div>

      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-warning/10 flex items-center justify-center">
              <AlertTriangle class="h-5 w-5 text-warning" />
            </div>
            <div>
              <div class="text-2xl font-bold text-base-content">{{ inactiveLocations }}</div>
              <div class="text-sm text-base-content/60">不活跃站点</div>
            </div>
          </div>
        </div>
      </div>

      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 rounded-lg bg-info/10 flex items-center justify-center">
              <Calendar class="h-5 w-5 text-info" />
            </div>
            <div>
              <div class="text-2xl font-bold text-base-content">{{ recentlyUpdated }}</div>
              <div class="text-sm text-base-content/60">近期更新</div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 位置列表表格 -->
    <div class="card bg-base-100 border border-gray-200/20">
      <div class="card-body p-0">
        <div class="overflow-x-auto">
          <table class="table table-zebra">
            <thead>
              <tr class="border-b border-gray-200/20">
                <th class="bg-base-200/50">Favicon</th>
                <th class="bg-base-200/50">标题</th>
                <th class="bg-base-200/50">类型</th>
                <th class="bg-base-200/50">状态</th>
                <th class="bg-base-200/50">最后访问</th>
                <th class="bg-base-200/50">FQDN</th>
                <th class="bg-base-200/50">截图</th>
                <th class="bg-base-200/50">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="location in filteredLocations" :key="location.id" class="hover:bg-base-200/30">
                <td class="w-12">
                  <div class="w-8 h-8 rounded bg-base-300 flex items-center justify-center">
                    <img v-if="location.favicon" :src="location.favicon" :alt="location.title" class="w-6 h-6 rounded"
                      @error="handleImageError" />
                    <Globe v-else class="h-4 w-4 text-base-content/50" />
                  </div>
                </td>
                <td>
                  <div class="font-medium text-base-content">{{ location.title }}</div>
                </td>
                <td>
                  <span :class="getTypeClass(location.type)">
                    {{ getTypeLabel(location.type) }}
                  </span>
                </td>
                <td>
                  <span :class="getStatusClass(location.available)">
                    {{ location.available ? '活跃' : '不活跃' }}
                  </span>
                </td>
                <td>
                  <div class="text-sm">{{ formatDate(location.lastscrape) }}</div>
                </td>
                <td>
                  <div class="font-mono text-sm text-base-content">{{ location.fqdn }}</div>
                </td>
                <td>
                  <div class="w-20 h-12 rounded bg-base-300 flex items-center justify-center">
                    <img v-if="location.screenshot" :src="location.screenshot" :alt="`${location.title}截图`"
                      class="w-full h-full object-cover rounded cursor-pointer"
                      @click="viewScreenshot(location.screenshot, location.title)" @error="handleScreenshotError" />
                    <Camera v-else class="h-4 w-4 text-base-content/50" />
                  </div>
                </td>
                <td>
                  <div class="flex gap-2">
                    <button class="btn btn-ghost btn-xs" @click="copyToClipboard(location.fqdn)" title="复制FQDN">
                      <Copy class="h-3 w-3" />
                    </button>
                    <button v-if="location.available" class="btn btn-ghost btn-xs" @click="openLocation(location.fqdn)"
                      title="访问">
                      <ExternalLink class="h-3 w-3" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredLocations.length === 0" class="text-center py-12">
          <Globe class="h-16 w-16 mx-auto text-base-content/30 mb-4" />
          <h3 class="text-lg font-medium text-base-content/70 mb-2">暂无位置信息</h3>
          <p class="text-base-content/50">
            该组织暂无已知站点信息
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Globe,
  CheckCircle,
  AlertTriangle,
  Calendar,
  Copy,
  ExternalLink,
  Camera
} from 'lucide-vue-next'
import type { RansomwareGroup } from '@/types/api'
import { useToast } from '@/composables/useToast'

interface Props {
  group: RansomwareGroup
}

const props = defineProps<Props>()

// Toast功能
const { showSuccess, showError } = useToast()

// 响应式数据
const knownLocations = ref(props.group.locations || [])

// 计算属性
const activeLocations = computed(() =>
  knownLocations.value.filter(location => location.available).length
)

const inactiveLocations = computed(() =>
  knownLocations.value.filter(location => !location.available).length
)

const recentlyUpdated = computed(() => {
  const oneWeekAgo = new Date()
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
  return knownLocations.value.filter(location =>
    new Date(location.lastscrape || location.updated) > oneWeekAgo
  ).length
})

const filteredLocations = computed(() => {
  return knownLocations.value
})

// 方法
const getTypeClass = (type: string) => {
  const classes = {
    website: 'badge badge-primary badge-outline',
    payment: 'badge badge-warning badge-outline',
    blog: 'badge badge-info badge-outline',
    chat: 'badge badge-success badge-outline',
    api: 'badge badge-secondary badge-outline'
  }
  return classes[type as keyof typeof classes] || 'badge badge-ghost'
}

const getTypeLabel = (type: string) => {
  const labels = {
    website: '网站',
    payment: '支付页面',
    blog: '博客',
    chat: '聊天室',
    api: 'API'
  }
  return labels[type as keyof typeof labels] || type
}

const getStatusClass = (available: boolean) => {
  return available
    ? 'badge badge-success badge-outline'
    : 'badge badge-error badge-outline'
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    showSuccess('复制成功！')
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案：使用传统的复制方法
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      showSuccess('复制成功！')
    } catch (fallbackErr) {
      console.error('降级复制也失败:', fallbackErr)
      showError('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

const openLocation = (fqdn: string) => {
  // 由于是.onion域名，这里只是示例
  console.log('尝试访问:', fqdn)
  // 实际应用中可能需要特殊处理Tor网络访问
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const handleScreenshotError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const viewScreenshot = (screenshot: string, title: string) => {
  // 这里可以实现截图查看功能，比如打开模态框或新窗口
  console.log('查看截图:', title, screenshot)
  // 简单实现：在新窗口中打开截图
  window.open(screenshot, '_blank')
}
</script>
