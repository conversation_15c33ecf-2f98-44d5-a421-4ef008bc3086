<template>
  <div ref="ransomNotesContainer" class="space-y-4 sm:space-y-6">
    <!-- 勒索信列表视图 -->
    <div v-if="!selectedRansomNote" class="space-y-4 sm:space-y-6">
      <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4 mb-4 sm:mb-6">
        <h3 class="text-base sm:text-lg font-semibold">勒索信</h3>
        <div class="flex items-center gap-2 text-xs sm:text-sm text-base-content/60">
          <FileText class="h-3 w-3 sm:h-4 sm:w-4" />
          <span>{{ pagination.total_count || 0 }} 个样本</span>
        </div>
      </div>

      <!-- 勒索信样本列表 -->
      <div v-if="ransomNotes && ransomNotes.length > 0" class="space-y-3 sm:space-y-4">
        <div v-for="note in ransomNotes" :key="note.id"
          class="card bg-base-100 border border-gray-200/20 hover:border-gray-300/30 transition-colors cursor-pointer"
          @click="selectRansomNote(note)">
          <div class="card-body p-4 sm:p-6">
            <!-- 勒索信头部信息 -->
            <div class="flex items-start gap-3 sm:gap-4">
              <div
                class="w-10 h-10 sm:w-12 sm:h-12 bg-error/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Mail class="h-5 w-5 sm:h-6 sm:w-6 text-error" />
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                  <h4 class="font-semibold text-base sm:text-lg truncate">{{ note.note_name }}{{ note.extension }}</h4>
                  <div class="flex items-center gap-2">
                    <span class="badge badge-error badge-xs sm:badge-sm">{{ note.extension }}</span>
                    <span class="badge badge-outline badge-xs sm:badge-sm">勒索信</span>
                  </div>
                </div>
                <div
                  class="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 text-xs sm:text-sm text-base-content/60 mb-3">
                  <div class="flex items-center gap-1">
                    <Calendar class="h-3 w-3 flex-shrink-0" />
                    <span class="truncate">{{ formatDate(note.created_at) }}</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <FileText class="h-3 w-3 flex-shrink-0" />
                    <span class="truncate">{{ note.extension }} 文件</span>
                  </div>
                </div>
                <!-- 内容预览 -->
                <div class="bg-base-200/30 p-3 rounded-lg">
                  <p class="text-xs sm:text-sm text-base-content/70 line-clamp-3">{{ note.content_preview }}</p>
                </div>
              </div>
              <!-- 进入图标 -->
              <div class="flex items-center">
                <ChevronRight class="h-4 w-4 sm:h-5 sm:w-5 text-base-content/50" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg text-primary"></span>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <div class="w-16 h-16 bg-base-300/50 rounded-full flex items-center justify-center mx-auto mb-4">
          <Mail class="h-8 w-8 text-base-content/50" />
        </div>
        <h4 class="text-lg font-medium text-base-content/70 mb-2">暂无勒索信样本</h4>
        <p class="text-sm text-base-content/50">该组织目前没有可用的勒索信样本数据</p>
      </div>

      <!-- 分页组件 -->
      <Pagination
        v-if="pagination.total_count > pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="pagination.total_count"
        :show-info="true"
        :use-events="true"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 勒索信详情视图 -->
    <div v-else class="space-y-4 sm:space-y-6">
      <!-- 详情加载状态 -->
      <div v-if="detailLoading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg text-primary"></span>
      </div>

      <!-- 详情内容 -->
      <template v-else>
        <div class="flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
        <!-- 返回按钮 -->
        <button @click="() => { selectedRansomNote = null; detailLoading = false; }" class="btn btn-sm btn-circle btn-ghost min-h-[40px] min-w-[40px]">
          <ArrowLeft class="h-4 w-4" />
        </button>
        <div class="flex flex-col sm:flex-row sm:items-center gap-2 min-w-0 flex-1">
          <h3 class="text-base sm:text-lg font-semibold truncate">{{ selectedRansomNote.note_name }}{{ selectedRansomNote.extension }}</h3>
          <div class="flex items-center gap-2">
            <span class="badge badge-error badge-xs sm:badge-sm">{{ selectedRansomNote.extension }}</span>
            <span class="badge badge-outline badge-xs sm:badge-sm">勒索信</span>
          </div>
        </div>
      </div>

      <!-- 勒索信详细信息 -->
      <div class="card bg-base-100 border border-gray-200/20">
        <div class="card-body p-4 sm:p-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-4 sm:mb-6">
            <div class="flex items-center gap-2 sm:gap-3">
              <div
                class="w-8 h-8 sm:w-10 sm:h-10 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Calendar class="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              </div>
              <div class="min-w-0">
                <div class="text-xs sm:text-sm text-base-content/60">创建时间</div>
                <div class="font-medium text-sm sm:text-base">{{ formatDate(selectedRansomNote.created_at) }}</div>
              </div>
            </div>
            <div class="flex items-center gap-2 sm:gap-3">
              <div class="w-8 h-8 sm:w-10 sm:h-10 bg-info/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <FileText class="h-4 w-4 sm:h-5 sm:w-5 text-info" />
              </div>
              <div class="min-w-0">
                <div class="text-xs sm:text-sm text-base-content/60">文件类型</div>
                <div class="font-medium text-sm sm:text-base">{{ selectedRansomNote.extension }}</div>
              </div>
            </div>
            <div class="flex items-center gap-2 sm:gap-3">
              <div
                class="w-8 h-8 sm:w-10 sm:h-10 bg-warning/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Shield class="h-4 w-4 sm:h-5 sm:w-5 text-warning" />
              </div>
              <div class="min-w-0">
                <div class="text-xs sm:text-sm text-base-content/60">组织名称</div>
                <div class="font-medium text-sm sm:text-base">{{ selectedRansomNote.group_name }}</div>
              </div>
            </div>
            <div class="flex items-center gap-2 sm:gap-3">
              <div
                class="w-8 h-8 sm:w-10 sm:h-10 bg-error/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <Target class="h-4 w-4 sm:h-5 sm:w-5 text-error" />
              </div>
              <div class="min-w-0">
                <div class="text-xs sm:text-sm text-base-content/60">内容长度</div>
                <div class="font-medium text-sm sm:text-base">{{ selectedRansomNote.content?.length || 0 }} 字符</div>
              </div>
            </div>
          </div>

          <!-- 勒索信完整内容 -->
          <div class="mb-4 sm:mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-3">
              <h4 class="font-medium text-sm sm:text-base">完整内容</h4>
              <div class="flex items-center gap-2 overflow-x-auto">
                <div class="flex gap-2 min-w-max">
                  <!-- 复制按钮 -->
                  <button @click="copyRansomNoteContent(selectedRansomNote)"
                    class="btn btn-xs sm:btn-sm btn-outline btn-primary min-h-[36px] sm:min-h-[40px]">
                    <Copy class="h-3 w-3 sm:h-4 sm:w-4" />
                    <span class="ml-1 text-xs sm:text-sm">复制</span>
                  </button>
                  <!-- 下载按钮 -->
                  <button @click="downloadRansomNote(selectedRansomNote)"
                    class="btn btn-xs sm:btn-sm btn-outline btn-secondary min-h-[36px] sm:min-h-[40px]">
                    <Download class="h-3 w-3 sm:h-4 sm:w-4" />
                    <span class="ml-1 text-xs sm:text-sm">下载</span>
                  </button>
                  <!-- HTML预览按钮 -->
                  <button v-if="isHtmlContent(selectedRansomNote)" @click="previewHtml(selectedRansomNote)"
                    class="btn btn-xs sm:btn-sm btn-outline btn-accent min-h-[36px] sm:min-h-[40px]">
                    <Eye class="h-3 w-3 sm:h-4 sm:w-4" />
                    <span class="ml-1 text-xs sm:text-sm">HTML预览</span>
                  </button>
                </div>
              </div>
            </div>
            <div class="bg-base-200/50 p-4 rounded-lg border border-gray-200/20">
              <div class="font-mono text-sm leading-relaxed whitespace-pre-wrap">{{ selectedRansomNote.content }}</div>
            </div>
          </div>
        </div>
      </div>
      </template>
    </div>
  </div>

</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  FileText,
  Mail,
  Calendar,
  Shield,
  Target,
  ChevronRight,
  ArrowLeft,
  Copy,
  Download,
  Eye
} from 'lucide-vue-next'
import { useToast } from '@/composables/useToast'
import { ransomwareGroupApi } from '@/lib/api/security'
import Pagination from '@/components/ui/Pagination.vue'
import { onMounted } from 'vue'

interface Props {
  groupSlug: string
}

const props = defineProps<Props>()

// Toast功能
const { showSuccess, showError } = useToast()

// 模板引用
const ransomNotesContainer = ref<HTMLElement>()

// 响应式数据
const selectedRansomNote = ref<any>(null)

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(8) // 每页显示8个勒索信
const loading = ref(false)
const detailLoading = ref(false) // 详情加载状态

// 勒索信数据状态
const ransomNotes = ref<any[]>([])
const pagination = ref({
  current_page: 1,
  page_size: 8,
  total_count: 0,
  total_pages: 0,
  has_next: false,
  has_prev: false
})

// 计算属性
const totalPages = computed(() => pagination.value.total_pages)

// 获取勒索信数据
const loadRansomNotes = async (page: number = 1) => {
  try {
    loading.value = true
    const response = await ransomwareGroupApi.getRansomNotes(props.groupSlug, {
      page,
      page_size: pageSize.value
    })

    ransomNotes.value = response.ransom_notes
    pagination.value = response.pagination
    currentPage.value = page
  } catch (error: any) {
    showError(error || '加载勒索信失败')
    console.error('加载勒索信失败:', error)
  } finally {
    loading.value = false
  }
}

// 滚动到勒索信列表顶部
const scrollToTop = () => {
  if (ransomNotesContainer.value) {
    ransomNotesContainer.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 分页控制方法
const handlePageChange = async (page: number) => {
  await loadRansomNotes(page)
  // 翻页后滚动到列表顶部
  setTimeout(() => {
    scrollToTop()
  }, 100) // 稍微延迟确保数据已渲染
}

// 选择勒索信
const selectRansomNote = async (note: any) => {
  try {
    detailLoading.value = true
    // 使用API方法获取勒索信的完整数据
    const result = await ransomwareGroupApi.getRansomNoteDetail(note.id)
    selectedRansomNote.value = result
  } catch (error: any) {
    console.error('获取勒索信详情出错:', error)
    showError(error || '获取勒索信详情失败')
    selectedRansomNote.value = note // 降级使用列表数据
  } finally {
    detailLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}



// 复制勒索信内容到剪贴板
const copyRansomNoteContent = async (note: any) => {
  try {
    await navigator.clipboard.writeText(note.content)
    showSuccess('勒索信内容已复制！')
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案：使用传统的复制方法
    const textArea = document.createElement('textarea')
    textArea.value = note.content
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      showSuccess('勒索信内容已复制！')
    } catch (fallbackErr) {
      console.error('降级复制也失败:', fallbackErr)
      showError('复制失败，请手动复制')
    }
    document.body.removeChild(textArea)
  }
}

// 下载勒索信文件
const downloadRansomNote = (note: any) => {
  const blob = new Blob([note.content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = note.note_name + note.extension
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// 检查是否为HTML内容
const isHtmlContent = (note: any) => {
  if (!note) return false
  // 检查文件扩展名是否为HTML格式
  const isHtmlFile = note.extension && note.extension.toLowerCase().includes('html')
  // 检查内容是否包含HTML标签
  const hasHtmlTags = note.content && (
    note.content.includes('<!DOCTYPE html>') ||
    note.content.includes('<html>') ||
    note.content.includes('<head>') ||
    note.content.includes('<body>')
  )
  return isHtmlFile || hasHtmlTags
}

// HTML预览功能
const previewHtml = (note: any) => {
  if (!note || !note.content) return

  // 创建包含安全警告的完整HTML内容
  const safeHtmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>HTML预览 - ${note.note_name}${note.extension}</title>
      <style>
        .security-warning {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background: #ff4444;
          color: white;
          padding: 10px;
          text-align: center;
          font-family: Arial, sans-serif;
          font-size: 14px;
          z-index: 9999;
          border-bottom: 2px solid #cc0000;
          box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }
        .preview-content {
          margin-top: 60px;
          padding: 20px;
        }
        /* 禁用所有链接和表单 */
        a, form, input, button {
          pointer-events: none !important;
          cursor: not-allowed !important;
        }
        a:hover {
          text-decoration: none !important;
        }
      </style>
    </head>
    <body>
      <div class="security-warning">
        ⚠️ 安全警告：这是勒索软件样本的HTML预览，仅供安全研究使用。所有链接和交互功能已被禁用。
      </div>
      <div class="preview-content">
        ${note.content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '<!-- Script removed for security -->')}
      </div>
    </body>
    </html>
  `

  // 创建Blob URL
  const blob = new Blob([safeHtmlContent], { type: 'text/html' })
  const blobUrl = URL.createObjectURL(blob)

  // 打开新窗口
  const previewWindow = window.open(blobUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes')

  if (!previewWindow) {
    alert('无法打开预览窗口，请检查浏览器的弹窗拦截设置')
    URL.revokeObjectURL(blobUrl)
    return
  }

  // 当窗口关闭时清理Blob URL
  previewWindow.addEventListener('beforeunload', () => {
    URL.revokeObjectURL(blobUrl)
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadRansomNotes(1)
})
</script>
