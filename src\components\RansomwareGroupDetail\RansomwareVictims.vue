<template>
  <div ref="victimsContainer" class="space-y-4 sm:space-y-6 relative">

    <!-- 受害者列表 -->
    <div class="card-body sm:p-6">
      <div class="mb-4">
        <h3 class="text-base sm:text-lg font-semibold">受害者列表</h3>
      </div>

      <!-- 受害者卡片列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        <div v-for="victim in getVictims()" :key="victim.id"
          class="card bg-base-100 border border-gray-200/20 hover:shadow-lg transition-shadow duration-200 relative overflow-hidden">
          <div class="card-body p-4 relative">
            <!-- 头部：网站名称和国旗 -->
            <div class="flex items-start justify-between mb-3">
              <div class="flex items-center gap-3">
                <!-- 公司图标 -->
                <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span class="text-lg font-bold text-primary">{{ victim.post_title.charAt(0).toUpperCase() }}</span>
                </div>
                <div class="flex-1 min-w-0">
                  <a :href="`/victim/${victim.id}`" target="_blank"
                    class="font-semibold text-base text-base-content mb-1 truncate hover:text-primary transition-colors cursor-pointer block">
                    {{ victim.post_title }}
                  </a>
                </div>
              </div>
              <!-- 国旗图标 -->
              <div class="flex-shrink-0">
                <div class="w-8 h-6 bg-secondary/20 rounded flex items-center justify-center">
                  <Flag class="h-4 w-4 text-secondary" />
                </div>
              </div>
            </div>

            <!-- 国家标签 -->
            <div class="mb-3">
              <span class="badge badge-success badge-sm font-medium">
                {{ victim.country || '未知' }}
              </span>
            </div>

            <!-- 日期信息 -->
            <div class="space-y-2 mb-3">
              <div class="flex items-center gap-2 text-sm">
                <Calendar class="h-4 w-4 text-primary" />
                <span class="text-base-content/70">发现日期:</span>
                <span class="font-medium">{{ formatEstimatedDate(victim.discovered) }}</span>
              </div>

              <div class="flex items-center gap-2 text-sm">
                <DollarSign class="h-4 w-4 text-warning" />
                <span class="text-base-content/70">勒索赎金:</span>
                <span class="font-medium">{{ victim.ransom_amount }}</span>
              </div>

              <div class="flex items-center gap-2 text-sm">
                <HandCoins class="h-4 w-4 text-purple-500" />
                <span class="text-base-content/70">支付状态:</span>
                <span class="font-medium">{{ victim.status }}</span>
              </div>
            </div>

            <!-- 描述文本 -->
            <div class="mb-4">
              <p class="text-sm text-base-content/70 line-clamp-2">
                {{ victim.description || '暂无描述信息' }}
              </p>
            </div>

            <!-- 底部操作栏 -->
            <div class="flex items-center justify-between pt-3 border-t border-gray-200/20">
              <div class="flex items-center gap-2">
                <!-- 操作图标 -->
                <button class="btn btn-xs btn-ghost" title="网站信息">
                  <Globe class="h-3 w-3" />
                </button>
                <button class="btn btn-xs btn-ghost" title="公司信息">
                  <Building class="h-3 w-3" />
                </button>
                <button class="btn btn-xs btn-ghost" title="截图">
                  <Camera class="h-3 w-3" />
                </button>
                <button class="btn btn-xs btn-ghost" title="财务信息">
                  <DollarSign class="h-3 w-3" />
                </button>
                <button class="btn btn-xs btn-ghost" title="数据库">
                  <Database class="h-3 w-3" />
                </button>
                <button class="btn btn-xs btn-ghost" title="文档">
                  <FileText class="h-3 w-3" />
                </button>
                <button class="btn btn-xs btn-ghost" title="新闻">
                  <Newspaper class="h-3 w-3" />
                </button>
                <button @click="copyVictimInfo(victim)" class="btn btn-xs btn-ghost" title="复制信息">
                  <Copy class="h-3 w-3" />
                </button>
              </div>

              <!-- 右下角建筑图标 -->
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-info/10 rounded-lg flex items-center justify-center">
                  <Building class="h-4 w-4 text-info" />
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center py-8">
        <span class="loading loading-spinner loading-lg text-primary"></span>
      </div>

      <!-- 分页组件 -->
      <Pagination
        v-else-if="pagination.total_count > pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="pagination.total_count"
        :show-info="true"
        :use-events="true"
        @page-change="handlePageChange"
      />

      <!-- 无数据状态 -->
      <div v-else-if="pagination.total_count === 0 && !loading" class="text-center py-12">
        <Building class="h-16 w-16 mx-auto text-base-content/30 mb-4" />
        <h3 class="text-lg font-medium text-base-content/70 mb-2">暂无受害者记录</h3>
        <p class="text-base-content/50">
          该组织暂无已知的受害者信息
        </p>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Building,
  Globe,
  Copy,
  Calendar,
  DollarSign,
  Camera,
  Database,
  FileText,
  Newspaper,
  HandCoins,
  Flag
} from 'lucide-vue-next'
import { useToast } from '@/composables/useToast'
import Pagination from '@/components/ui/Pagination.vue'
import { ransomwareGroupApi } from '@/lib/api/security'
import { onMounted } from 'vue'

interface Props {
  groupSlug: string
}

const props = defineProps<Props>()

// Toast功能
const { showSuccess, showError } = useToast()

// 模板引用
const victimsContainer = ref<HTMLElement>()

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(12) // 每页显示12个受害者
const loading = ref(false)

// 受害者数据状态
const victims = ref<any[]>([])
const pagination = ref({
  current_page: 1,
  page_size: 12,
  total_count: 0,
  total_pages: 0,
  has_next: false,
  has_prev: false
})

// 计算属性
const totalPages = computed(() => pagination.value.total_pages)

// 获取受害者数据
const loadVictims = async (page: number = 1) => {
  try {
    loading.value = true
    const response = await ransomwareGroupApi.getVictims(props.groupSlug, {
      page,
      page_size: pageSize.value
    })

    victims.value = response.victims
    pagination.value = response.pagination
    currentPage.value = page
  } catch (error: any) {
    showError(error || '加载受害者数据失败')
    console.error('加载受害者数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 滚动到受害者列表顶部
const scrollToTop = () => {
  if (victimsContainer.value) {
    victimsContainer.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 分页控制方法
const handlePageChange = async (page: number) => {
  await loadVictims(page)
  // 翻页后滚动到列表顶部
  setTimeout(() => {
    scrollToTop()
  }, 100) // 稍微延迟确保数据已渲染
}

// 获取当前页受害者数据
const getVictims = () => {
  return victims.value
}

// 格式化预估攻击日期（比发现日期早一些）
const formatEstimatedDate = (dateString: string) => {
  const date = new Date(dateString)
  date.setDate(date.getDate() - Math.floor(Math.random() * 30 + 10)) // 提前10-40天
  return date.toLocaleDateString('zh-CN')
}

// 复制受害者信息
const copyVictimInfo = async (victim: any) => {
  const info = `受害者: ${victim.post_title}\n发现日期: ${formatEstimatedDate(victim.discovered)}\n国家: ${victim.country}\n描述: ${victim.description || '暂无描述信息'}`

  try {
    await navigator.clipboard.writeText(info)
    showSuccess('受害者信息已复制！')
  } catch (err) {
    console.error('复制失败:', err)
    showError('复制失败，请手动复制')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadVictims(1)
})
</script>
