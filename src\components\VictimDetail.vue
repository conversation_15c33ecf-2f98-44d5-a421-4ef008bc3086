<template>
  <div class="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
    <!-- 面包屑导航 -->
    <nav class="mb-6 sm:mb-8">
      <div class="breadcrumbs text-sm">
        <ul>
          <li>
            <a href="/" class="text-primary hover:text-primary/80">首页</a>
          </li>
          <li class="breadcrumbs-separator rtl:-rotate-[40deg]">/</li>
          <li>
            <a href="/victims" class="text-primary hover:text-primary/80">受害者</a>
          </li>
          <li class="breadcrumbs-separator rtl:-rotate-[40deg]">/</li>
          <li aria-current="page" class="text-base-content/70 truncate max-w-xs">
            {{ victim.post_title }}
          </li>
        </ul>
      </div>
    </nav>

    <!-- 受害者头部信息 -->
    <div class="card bg-base-100 border border-gray-200/20">
      <div class="card-body p-4 sm:p-6 lg:p-8">
        <div class="flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8">
          <!-- 左侧企业logo -->
          <div class="w-full sm:w-full lg:w-80 h-32 sm:h-40 lg:h-48 flex-shrink-0 overflow-hidden rounded-lg bg-base-200/30 flex items-center justify-center">
            <div class="text-center">
              <div class="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                <span class="text-2xl font-bold text-primary">{{ getInitials(victim.post_title) }}</span>
              </div>
              <div class="text-sm font-medium text-base-content">{{ victim.post_title }}</div>
              <div class="text-xs text-base-content/60">{{ getCountryName(victim.country) }}</div>
            </div>
          </div>

          <!-- 右侧信息 -->
          <div class="flex-1">
            <!-- 标题和标签 -->
            <div class="mb-4 sm:mb-6">
              <div class="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4 flex-wrap">
                <span class="badge badge-error">
                  {{ victim.group_name?.toUpperCase() || '未知组织' }}
                </span>
                <span class="badge badge-warning">
                  已确认攻击
                </span>
                <span class="badge badge-outline">
                  {{ getCountryName(victim.country) }}
                </span>
              </div>

              <h1 class="text-2xl sm:text-3xl font-bold text-base-content mb-2">{{ victim.post_title }}</h1>

              <div class="text-sm text-base-content/70 mb-3 sm:mb-4">
                {{ victim.description }}
              </div>
            </div>

            <!-- 基本信息网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
              <!-- 发现日期 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Calendar class="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">发现日期</div>
                  <div class="font-medium text-sm text-base-content">{{ formatDateChinese(victim.discovered) }}</div>
                </div>
              </div>

              <!-- 发布日期 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Clock class="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">发布日期</div>
                  <div class="font-medium text-sm text-base-content">{{ formatDateChinese(victim.published) }}</div>
                </div>
              </div>

              <!-- 网站地址 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Globe class="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">网站地址</div>
                  <div class="font-medium text-sm text-base-content">
                    <a :href="`https://${victim.website}`" target="_blank" class="link link-primary">
                      {{ victim.website }}
                    </a>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex items-center gap-2">
                <button
                  @click="copyVictimInfo"
                  class="btn btn-outline btn-sm"
                  title="复制信息"
                >
                  <Copy class="h-4 w-4" />
                  复制
                </button>
                <a
                  :href="`https://${victim.website}`"
                  target="_blank"
                  class="btn btn-primary btn-sm"
                  v-if="victim.website"
                >
                  <ExternalLink class="h-4 w-4" />
                  访问
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细信息 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6 sm:mt-8">
      <!-- 左侧主要信息 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 公司详情 -->
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Info class="h-5 w-5 text-primary" />
              公司详情
            </h3>
            <div class="prose prose-sm max-w-none text-base-content/80" v-html="parsedDescription"></div>
          </div>
        </div>

        <!-- 攻击截图 -->
        <div class="card bg-base-100 border border-gray-200/20" v-if="victim.screenshot">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Camera class="h-5 w-5 text-primary" />
              攻击截图
            </h3>
            <div class="relative">
              <img
                :src="victim.screenshot"
                :alt="`${victim.post_title} 攻击截图`"
                class="w-full rounded-lg border border-gray-200/20"
                @error="handleImageError"
              />
              <div class="absolute top-4 right-4">
                <button
                  @click="openImageInNewTab"
                  class="btn btn-sm btn-circle bg-black/50 hover:bg-black/70 text-white border-0"
                  title="在新标签页中打开"
                >
                  <ExternalLink class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 攻击信息 -->
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Database class="h-5 w-5 text-primary" />
              攻击信息
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div class="p-4 bg-base-200/30 rounded-lg">
                <div class="text-sm text-base-content/60 mb-1">勒索软件组织</div>
                <div class="font-medium">{{ victim.group_name.toUpperCase() }}</div>
              </div>
              <div class="p-4 bg-base-200/30 rounded-lg">
                <div class="text-sm text-base-content/60 mb-1">攻击状态</div>
                <div class="font-medium">已确认攻击</div>
              </div>
              <div class="p-4 bg-base-200/30 rounded-lg">
                <div class="text-sm text-base-content/60 mb-1">目标国家</div>
                <div class="font-medium">{{ getCountryName(victim.country) }}</div>
              </div>
              <div class="p-4 bg-base-200/30 rounded-lg">
                <div class="text-sm text-base-content/60 mb-1">活动状态</div>
                <div class="font-medium">{{ victim.activity || '未知' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 信息窃取统计 -->
        <div class="card bg-base-100 border border-gray-200/20" v-if="victim.infostealer">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Database class="h-5 w-5 text-primary" />
              信息窃取统计
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
              <div class="text-center p-4 bg-base-200/30 rounded-lg">
                <div class="text-2xl font-bold text-warning mb-2">{{ victim.infostealer.employees }}</div>
                <div class="text-sm text-base-content/60">员工数据</div>
              </div>
              <div class="text-center p-4 bg-base-200/30 rounded-lg">
                <div class="text-2xl font-bold text-error mb-2">{{ victim.infostealer.users }}</div>
                <div class="text-sm text-base-content/60">用户数据</div>
              </div>
              <div class="text-center p-4 bg-base-200/30 rounded-lg">
                <div class="text-2xl font-bold text-info mb-2">{{ victim.infostealer.thirdparties }}</div>
                <div class="text-sm text-base-content/60">第三方数据</div>
              </div>
            </div>
            <div class="text-sm text-base-content/60">
              最后更新：{{ formatDateChinese(victim.infostealer.update) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧辅助信息 -->
      <div class="space-y-6">
        <!-- 攻击时间线 -->
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Clock class="h-5 w-5 text-primary" />
              攻击时间线
            </h3>
            <div class="space-y-4">
              <div class="flex items-start gap-3">
                <div class="w-2 h-2 bg-error rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div class="font-medium text-sm">攻击发布</div>
                  <div class="text-xs text-base-content/60">{{ formatDateChinese(victim.published) }}</div>
                </div>
              </div>
              <div class="flex items-start gap-3">
                <div class="w-2 h-2 bg-warning rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div class="font-medium text-sm">攻击发现</div>
                  <div class="text-xs text-base-content/60">{{ formatDateChinese(victim.discovered) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 修改记录 -->
        <div class="card bg-base-100 border border-gray-200/20" v-if="victim.modifications?.length">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Clock class="h-5 w-5 text-primary" />
              修改记录
            </h3>
            <div class="space-y-3">
              <div
                v-for="(mod, index) in victim.modifications"
                :key="index"
                class="flex items-start gap-3 p-3 bg-base-200/30 rounded-lg"
              >
                <div class="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div class="font-medium text-sm">{{ mod.description }}</div>
                  <div class="text-xs text-base-content/60">{{ formatDateChinese(mod.date) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关链接 -->
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
              <Link class="h-5 w-5 text-primary" />
              相关链接
            </h3>
            <div class="space-y-3">
              <a
                :href="victim.permalink"
                target="_blank"
                class="flex items-center gap-3 p-3 bg-base-200/30 rounded-lg hover:bg-base-200/50 transition-colors"
              >
                <ExternalLink class="h-4 w-4 text-primary" />
                <div>
                  <div class="font-medium text-sm">Ransomware.live</div>
                  <div class="text-xs text-base-content/60">查看原始数据</div>
                </div>
              </a>

              <button
                @click="copyDarkWebLink"
                class="flex items-center gap-3 p-3 bg-base-200/30 rounded-lg hover:bg-base-200/50 transition-colors w-full text-left"
                title="复制暗网链接"
              >
                <Copy class="h-4 w-4 text-warning" />
                <div>
                  <div class="font-medium text-sm">暗网链接</div>
                  <div class="text-xs text-base-content/60">复制到剪贴板</div>
                </div>
              </button>

              <a
                :href="`https://${victim.website}`"
                target="_blank"
                class="flex items-center gap-3 p-3 bg-base-200/30 rounded-lg hover:bg-base-200/50 transition-colors"
                v-if="victim.website"
              >
                <Globe class="h-4 w-4 text-blue-600" />
                <div>
                  <div class="font-medium text-sm">官方网站</div>
                  <div class="text-xs text-base-content/60">{{ victim.website }}</div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import {
  Copy, ExternalLink, Globe, Calendar, Clock, Camera, Database, Link, Info
} from 'lucide-vue-next'
import { useToast } from '@/composables/useToast'
import { useMarkdown } from '@/lib/markdown'
import { computed } from 'vue'

import type { Victim } from '@/types/api'

// Props
interface Props {
  victimData: Victim
}

const props = defineProps<Props>()

// 直接使用props中的受害者数据
const victim = props.victimData

// Toast功能
const { showSuccess, showError } = useToast()

// 初始化markdown解析器
const { smartParse } = useMarkdown()

// 解析描述内容
const parsedDescription = computed(() => {
  return smartParse(victim.description ?? '')
})

// 获取网站首字母
const getInitials = (title: string) => {
  return title.split('.')[0].substring(0, 2).toUpperCase()
}

// 获取默认图片
const getDefaultImage = () => {
  return '/images/default-victim.png'
}

// 获取国家名称
const getCountryName = (code: string) => {
  const countries: Record<string, string> = {
    'CN': '中国',
    'US': '美国',
    'UK': '英国',
    'DE': '德国',
    'FR': '法国',
    'JP': '日本',
    'KR': '韩国',
    'CA': '加拿大',
    'AU': '澳大利亚'
  }
  return countries[code] || code
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化中文日期
const formatDateChinese = (dateString: string) => {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}年${month}月${day}日`
}

// 复制受害者信息
const copyVictimInfo = async () => {
  const info = `受害者: ${victim.post_title}
网站: ${victim.website}
勒索软件组织: ${victim.group_name.toUpperCase()}
发现日期: ${formatDate(victim.discovered)}
发布日期: ${formatDate(victim.published)}
国家: ${getCountryName(victim.country)}
描述: ${victim.description}`

  try {
    await navigator.clipboard.writeText(info)
    showSuccess('受害者信息已复制！')
  } catch (err) {
    console.error('复制失败:', err)
    showError('复制失败，请手动复制')
  }
}

// 复制暗网链接
const copyDarkWebLink = async () => {
  try {
    await navigator.clipboard.writeText(victim.post_url)
    showSuccess('暗网链接已复制！')
  } catch (err) {
    console.error('复制失败:', err)
    showError('复制失败，请手动复制')
  }
}

// 在新标签页中打开图片
const openImageInNewTab = () => {
  window.open(victim.screenshot, '_blank')
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  showError('截图加载失败')
}
</script>
