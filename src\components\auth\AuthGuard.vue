<template>
  <div>
    <!-- 加载状态 -->
    <div v-if="!isInitialized || isLoading" class="min-h-screen flex items-center justify-center bg-base-100">
      <div class="text-center">
        <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
        <p class="text-base-content/60">正在验证登录状态...</p>
      </div>
    </div>

    <!-- 未登录状态 -->
    <div v-else-if="!isLoggedIn" class="min-h-screen flex items-center justify-center bg-base-100">
      <div class="text-center max-w-md mx-auto p-6">
        <div class="mb-6">
          <svg class="w-16 h-16 mx-auto text-warning mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
            </path>
          </svg>
          <h2 class="text-2xl font-bold text-base-content mb-2">需要登录</h2>
          <p class="text-base-content/60 mb-6">请先登录以访问此页面</p>

          <!-- 倒计时提示 -->
          <div v-if="props.autoRedirect && countdown > 0" class="mb-4">
            <div class="alert alert-info">
              <span>{{ countdown }} 秒后自动跳转到登录页面</span>
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <button @click="handleLogin" class="btn btn-primary btn-block" :disabled="isRedirecting">
            <span v-if="isRedirecting" class="loading loading-spinner loading-sm"></span>
            {{ isRedirecting ? '正在跳转...' : '立即登录' }}
          </button>

          <button @click="handleGoBack" class="btn btn-ghost btn-block" :disabled="isRedirecting">
            返回上一页
          </button>

        </div>
      </div>
    </div>

    <!-- 已登录，显示内容 -->
    <slot v-else />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useAuth } from '@/composables/useAuth'

interface Props {
  redirectTo?: string
  showLoading?: boolean
  autoRedirect?: boolean
  redirectDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  redirectTo: '/login',
  showLoading: true,
  autoRedirect: false,
  redirectDelay: 5000
})

const {
  isLoggedIn,
  isLoading,
  isInitialized,
  initAuth,
  cleanup,
  redirectToLogin
} = useAuth()

const isRedirecting = ref(false)
const countdown = ref(0)
let countdownTimer: NodeJS.Timeout | null = null
let redirectTimer: NodeJS.Timeout | null = null

// 开始倒计时
const startCountdown = () => {
  if (!props.autoRedirect) return

  countdown.value = Math.ceil(props.redirectDelay / 1000)

  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
      handleLogin()
    }
  }, 1000)
}

// 取消自动跳转
const cancelAutoRedirect = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  if (redirectTimer) {
    clearTimeout(redirectTimer)
    redirectTimer = null
  }
  countdown.value = 0
}

// 处理登录按钮点击
const handleLogin = () => {
  cancelAutoRedirect() // 取消倒计时
  isRedirecting.value = true
  const currentUrl = typeof window !== 'undefined' ? window.location.pathname : ''
  redirectToLogin(currentUrl)
}

// 处理返回按钮点击
const handleGoBack = () => {
  cancelAutoRedirect() // 取消倒计时
  if (typeof window !== 'undefined') {
    if (window.history.length > 1) {
      window.history.back()
    } else {
      window.location.href = '/'
    }
  }
}

// 监听登录状态变化，启动倒计时
watch(
  [isInitialized, isLoggedIn],
  ([initialized, loggedIn]) => {
    if (initialized && !loggedIn && props.autoRedirect) {
      startCountdown()
    } else if (loggedIn) {
      // 如果用户登录了，取消倒计时
      cancelAutoRedirect()
    }
  },
  { immediate: true }
)

onMounted(() => {
  initAuth()
})

onUnmounted(() => {
  cleanup()
  cancelAutoRedirect() // 清理定时器
})
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
