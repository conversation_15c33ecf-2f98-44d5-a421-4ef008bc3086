<template>
  <div class="min-h-screen flex items-center justify-center bg-base-100">
    <div class="text-center max-w-md mx-auto p-6">
      <div class="mb-6">
        <svg class="w-16 h-16 mx-auto text-warning mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
          </path>
        </svg>
        <h2 class="text-2xl font-bold text-base-content mb-2">需要登录</h2>
        <p class="text-base-content/60 mb-6">{{ message }}</p>
      </div>
      <div class="space-y-3">
        <a :href="loginUrl" class="btn btn-primary btn-block">
          立即登录
        </a>
        <a :href="backUrl" class="btn btn-ghost btn-block">
          {{ backText }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  message?: string
  backUrl?: string
  backText?: string
  currentPath?: string
}

const props = withDefaults(defineProps<Props>(), {
  message: '此内容需要登录后才能查看',
  backUrl: '/',
  backText: '返回首页',
  currentPath: ''
})

// 构建登录URL，包含重定向参数
const loginUrl = computed(() => {
  const path = props.currentPath || (typeof window !== 'undefined' ? window.location.pathname : '')
  return path ? `/login?redirect=${encodeURIComponent(path)}` : '/login'
})
</script>
