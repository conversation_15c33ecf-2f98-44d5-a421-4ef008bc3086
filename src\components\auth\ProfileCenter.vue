<template>
  <div class="bg-base-100 min-h-screen pt-20">
    <!-- 页面标题区域 -->
    <div class="bg-gradient-to-r from-primary/5 to-secondary/5 border-b border-base-content/10">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center">
          <h1 class="text-3xl font-bold text-base-content mb-2">个人中心</h1>
          <p class="text-base-content/60 text-lg">管理您的个人信息和密码安全</p>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8 pb-20">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- 侧边栏 -->
        <div class="lg:col-span-1">
          <div
            class="card bg-base-100 shadow-base-300/20 shadow-sm transition-shadow duration-200 ease-in-out hover:shadow-xl">
            <div class="card-body p-0">
              <!-- 用户信息头部 -->
              <div class="p-6 border-b border-base-content/10">
                <div class="flex flex-col items-center text-center">
                  <div class="avatar mb-4">
                    <div class="size-20 rounded-full">
                      <img :src="userInfo.avatar" :alt="getUserDisplayName()" class="object-cover" />
                    </div>
                  </div>
                  <!-- 隐藏的文件输入 -->
                  <input ref="avatarInput" type="file" accept="image/*" class="hidden" @change="handleAvatarChange" />
                  <h3 class="text-lg font-semibold text-base-content">{{ getUserDisplayName() }}</h3>
                  <p class="text-base-content/60 text-sm">{{ userInfo.phone }}</p>
                </div>
              </div>

              <!-- 导航菜单 -->
              <div class="p-2">
                <ul class="menu menu-sm">
                  <li>
                    <a :class="[
                      'flex items-center gap-3 px-4 py-2.5 rounded-lg transition-all duration-200 ease-in-out cursor-pointer',
                      'text-base-content/80 hover:text-base-content hover:bg-base-200/50 hover:translate-x-1',
                      { 'text-primary bg-primary/10 font-medium': activeTab === 'profile' }
                    ]" @click="activeTab = 'profile'">
                      <span class="icon-[tabler--user] size-4"></span>
                      个人资料
                    </a>
                  </li>
                  <li>
                    <a :class="[
                      'flex items-center gap-3 px-4 py-2.5 rounded-lg transition-all duration-200 ease-in-out cursor-pointer',
                      'text-base-content/80 hover:text-base-content hover:bg-base-200/50 hover:translate-x-1',
                      { 'text-primary bg-primary/10 font-medium': activeTab === 'security' }
                    ]" @click="activeTab = 'security'">
                      <span class="icon-[tabler--shield-lock] size-4"></span>
                      安全设置
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="lg:col-span-3">
          <!-- 个人资料标签页 -->
          <div v-if="activeTab === 'profile'" class="space-y-6">
            <!-- 头像设置卡片 -->
            <div
              class="card bg-base-100 shadow-base-300/20 shadow-sm transition-shadow duration-200 ease-in-out hover:shadow-xl">
              <div class="p-4 md:p-6 border-b border-base-content/10">
                <h2 class="text-lg font-semibold text-base-content mb-1">头像设置</h2>
                <p class="text-base-content/60 text-sm">管理您的头像图片</p>
              </div>
              <div class="card-body p-4 md:p-6">
                <form @submit.prevent="uploadAvatarOnly" class="space-y-4">
                  <!-- 头像上传区域 -->
                  <div
                    class="flex flex-col items-center space-y-4 p-6 border border-dashed border-base-content/20 rounded-lg bg-base-200/30">
                    <div class="avatar relative">
                      <div class="size-24 rounded-full">
                        <img :src="avatarPreview || userInfo.avatar" :alt="getUserDisplayName()" class="object-cover" />
                      </div>
                      <!-- 上传进度遮罩 -->
                      <div v-if="isUploadingAvatar"
                        class="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                        <div class="loading loading-spinner loading-md text-white"></div>
                      </div>
                    </div>
                    <div class="text-center">
                      <h4 class="font-medium text-base-content mb-1">头像设置</h4>
                      <p class="text-sm text-base-content/60 mb-3">支持 JPG、PNG、GIF 格式，文件大小不超过 5MB</p>
                      <div class="flex gap-2 justify-center flex-wrap">
                        <button type="button" @click="triggerAvatarUpload" class="btn btn-sm btn-primary"
                          :disabled="isUploadingAvatar">
                          <span v-if="!isUploadingAvatar" class="icon-[tabler--upload] size-4"></span>
                          <span v-else class="loading loading-spinner loading-sm"></span>
                          {{ isUploadingAvatar ? '上传中...' : '选择图片' }}
                        </button>
                        <button type="button" @click="showPresetAvatars = !showPresetAvatars"
                          class="btn btn-sm btn-outline" :disabled="isUploadingAvatar">
                          <span class="icon-[tabler--photo] size-4"></span>
                          预设头像
                        </button>
                        <button v-if="avatarPreview && !isUploadingAvatar" type="submit"
                          class="btn btn-sm btn-success">
                          <span class="icon-[tabler--check] size-4"></span>
                          立即上传
                        </button>
                        <button v-if="avatarPreview && !isUploadingAvatar" type="button" @click="removeAvatarPreview"
                          class="btn btn-sm btn-outline btn-error">
                          <span class="icon-[tabler--trash] size-4"></span>
                          移除
                        </button>
                      </div>

                      <!-- 预设头像选择 -->
                      <div v-if="showPresetAvatars"
                        class="mt-4 p-4 border border-base-content/10 rounded-lg bg-base-100">
                        <h5 class="text-sm font-medium text-base-content mb-3">选择预设头像</h5>
                        <div class="grid grid-cols-6 gap-2">
                          <button v-for="(avatar, index) in presetAvatars" :key="index"
                            @click="selectPresetAvatar(avatar)"
                            class="avatar hover:scale-110 transition-transform duration-200">
                            <div class="size-12 rounded-full ring-2 ring-transparent hover:ring-primary">
                              <img :src="avatar" :alt="`预设头像 ${index + 1}`" class="object-cover" />
                            </div>
                          </button>
                        </div>
                        <button @click="showPresetAvatars = false" class="btn btn-sm btn-outline mt-3 w-full">
                          收起
                        </button>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>

            <!-- 个人资料卡片 -->
            <div
              class="card bg-base-100 shadow-base-300/20 shadow-sm transition-shadow duration-200 ease-in-out hover:shadow-xl">
              <div class="p-4 md:p-6 border-b border-base-content/10">
                <h2 class="text-lg font-semibold text-base-content mb-1">个人资料</h2>
                <p class="text-base-content/60 text-sm">管理您的个人信息和联系方式</p>
              </div>
              <div class="card-body p-4 md:p-6">
                <form @submit.prevent="updateProfile" class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="label-text" for="nickname">昵称</label>
                      <input v-model="profileForm.nickname" type="text" id="nickname"
                        class="input focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                        placeholder="请输入昵称" maxlength="12" />
                    </div>

                  </div>

                  <div>
                    <label class="label-text" for="bio">个性签名</label>
                    <textarea v-model="profileForm.bio" id="bio"
                      class="textarea focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                      rows="3" placeholder="请输入个性签名"></textarea>
                  </div>

                  <div class="flex justify-end gap-2">
                    <button type="button" class="btn btn-outline">取消</button>
                    <button type="submit" class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      :disabled="isUpdating">
                      <span v-if="isUpdating" class="loading loading-spinner loading-sm"></span>
                      {{ isUpdating ? '保存中...' : '保存更改' }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- 安全设置标签页 -->
          <div v-if="activeTab === 'security'" class="space-y-6">
            <!-- 敏感信息设置 -->
            <div
              class="card bg-base-100 shadow-base-300/20 shadow-sm transition-shadow duration-200 ease-in-out hover:shadow-xl">
              <div class="p-4 md:p-6 border-b border-base-content/10">
                <h2 class="text-lg font-semibold text-base-content mb-1">敏感信息设置</h2>
                <p class="text-base-content/60 text-sm">管理您的邮箱地址和手机号码等敏感信息</p>
              </div>
              <div class="card-body p-4 md:p-6">
                <form @submit.prevent="updateSensitiveInfo" class="space-y-4">
                  <div class="alert alert-info">
                    <span class="icon-[tabler--info-circle] size-5"></span>
                    <div>
                      <h3 class="font-medium">安全提示</h3>
                      <div class="text-sm">修改邮箱地址或手机号码需要进行身份验证，系统将发送验证码到您的原邮箱或手机进行确认。</div>
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="label-text" for="securityEmail">邮箱地址</label>
                      <input v-model="securityForm.email" type="email" id="securityEmail"
                        class="input focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                        placeholder="请输入邮箱地址" />
                      <div class="label">
                        <span class="label-text-alt text-base-content/60">
                          <span class="icon-[tabler--shield-check] size-3 inline-block mr-1"></span>
                          用于账户安全验证和重要通知
                        </span>
                      </div>
                    </div>
                    <div>
                      <label class="label-text" for="securityPhone">手机号码</label>
                      <input v-model="securityForm.phone" type="tel" id="securityPhone"
                        class="input focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                        placeholder="请输入手机号码" />
                      <div class="label">
                        <span class="label-text-alt text-base-content/60">
                          <span class="icon-[tabler--shield-check] size-3 inline-block mr-1"></span>
                          用于接收验证码和安全提醒
                        </span>
                      </div>
                    </div>
                  </div>

                  <div class="flex justify-end gap-2">
                    <button type="button" class="btn btn-outline">取消</button>
                    <button type="submit" class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      :disabled="isUpdatingSecurity">
                      <span v-if="isUpdatingSecurity" class="loading loading-spinner loading-sm"></span>
                      {{ isUpdatingSecurity ? '更新中...' : '更新信息' }}
                    </button>
                  </div>
                </form>
              </div>
            </div>

            <!-- 密码设置 -->
            <div
              class="card bg-base-100 shadow-base-300/20 shadow-sm transition-shadow duration-200 ease-in-out hover:shadow-xl">
              <div class="p-4 md:p-6 border-b border-base-content/10">
                <h2 class="text-lg font-semibold text-base-content mb-1">密码设置</h2>
                <p class="text-base-content/60 text-sm">修改您的登录密码</p>
              </div>
              <div class="card-body p-4 md:p-6">
                <form @submit.prevent="updatePassword" class="space-y-4">
                  <div>
                    <label class="label-text" for="currentPassword">当前密码</label>
                    <div
                      class="input focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200">
                      <input v-model="passwordForm.currentPassword" :type="showCurrentPassword ? 'text' : 'password'"
                        id="currentPassword" placeholder="请输入当前密码" class="border-0 outline-0 focus:ring-0" />
                      <button type="button" @click="showCurrentPassword = !showCurrentPassword"
                        class="block cursor-pointer hover:text-primary transition-colors duration-200">
                        <span v-if="showCurrentPassword" class="icon-[tabler--eye-off] size-5"></span>
                        <span v-else class="icon-[tabler--eye] size-5"></span>
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="label-text" for="newPassword">新密码</label>
                    <div
                      class="input focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200">
                      <input v-model="passwordForm.newPassword" :type="showNewPassword ? 'text' : 'password'"
                        id="newPassword" placeholder="请输入新密码" class="border-0 outline-0 focus:ring-0" />
                      <button type="button" @click="showNewPassword = !showNewPassword"
                        class="block cursor-pointer hover:text-primary transition-colors duration-200">
                        <span v-if="showNewPassword" class="icon-[tabler--eye-off] size-5"></span>
                        <span v-else class="icon-[tabler--eye] size-5"></span>
                      </button>
                    </div>
                  </div>

                  <div>
                    <label class="label-text" for="confirmPassword">确认新密码</label>
                    <div
                      class="input focus-within:border-primary focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200">
                      <input v-model="passwordForm.confirmPassword" :type="showConfirmPassword ? 'text' : 'password'"
                        id="confirmPassword" placeholder="请再次输入新密码" class="border-0 outline-0 focus:ring-0" />
                      <button type="button" @click="showConfirmPassword = !showConfirmPassword"
                        class="block cursor-pointer hover:text-primary transition-colors duration-200">
                        <span v-if="showConfirmPassword" class="icon-[tabler--eye-off] size-5"></span>
                        <span v-else class="icon-[tabler--eye] size-5"></span>
                      </button>
                    </div>
                  </div>

                  <div class="flex justify-end gap-2">
                    <button type="button" class="btn btn-outline">取消</button>
                    <button type="submit" class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                      :disabled="isUpdatingPassword">
                      <span v-if="isUpdatingPassword" class="loading loading-spinner loading-sm"></span>
                      {{ isUpdatingPassword ? '更新中...' : '更新密码' }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 头像裁剪模态框 -->
    <AvatarCropper :show="showCropper" :image-src="cropperImageSrc" @confirm="handleCropConfirm"
      @cancel="handleCropCancel" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import AvatarCropper from '../ui/AvatarCropper.vue'
import { useToast } from '@/composables/useToast'
import { authApi } from '@/lib/api'
import { avatarManager } from '@/lib/avatar-manager'

// Toast 实例
const { showSuccess, showError, showWarning } = useToast()

// 响应式数据
const activeTab = ref('profile')
const isUpdating = ref(false)
const isUpdatingPassword = ref(false)
const isUpdatingSecurity = ref(false)

// 密码显示状态
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

// 头像相关
const avatarInput = ref<HTMLInputElement>()
const avatarPreview = ref<string>('')
const avatarFile = ref<File | null>(null)
const isUploadingAvatar = ref(false)
const showCropper = ref(false)
const cropperImageSrc = ref('')

// 预设头像
const presetAvatars = [
  'https://cdn.flyonui.com/fy-assets/avatar/avatar-1.png',
  'https://cdn.flyonui.com/fy-assets/avatar/avatar-2.png',
  'https://cdn.flyonui.com/fy-assets/avatar/avatar-3.png',
  'https://cdn.flyonui.com/fy-assets/avatar/avatar-4.png',
  'https://cdn.flyonui.com/fy-assets/avatar/avatar-5.png',
  'https://cdn.flyonui.com/fy-assets/avatar/avatar-6.png'
]
const showPresetAvatars = ref(false)

// 用户信息
const userInfo = reactive({
  id: '1', // 添加必需的 id 属性
  username: 'zhangsan',
  nickname: '',
  email: '<EMAIL>',
  avatar: 'https://cdn.flyonui.com/fy-assets/avatar/avatar-1.png',
  phone: '+86 138 0013 8000',
  bio: '专注于网络安全威胁情报分析，拥有5年安全行业经验。'
})

// 获取用户显示名称：优先显示昵称，如果没有昵称再显示用户名
const getUserDisplayName = () => {
  return userInfo.nickname || userInfo.username || '用户'
}

// 个人资料表单
const profileForm = reactive({
  nickname: userInfo.nickname,
  bio: userInfo.bio
})

// 安全信息表单
const securityForm = reactive({
  email: userInfo.email,
  phone: userInfo.phone
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 头像上传方法
const triggerAvatarUpload = () => {
  avatarInput.value?.click()
}

const handleAvatarChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    showError('请选择图片文件（支持 JPG、PNG、GIF 格式）')
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    showError('图片大小不能超过 5MB，请选择更小的图片')
    return
  }

  avatarFile.value = file

  // 创建预览并打开裁剪器
  const reader = new FileReader()
  reader.onload = (e) => {
    cropperImageSrc.value = e.target?.result as string
    showCropper.value = true
  }
  reader.onerror = () => {
    showError('图片读取失败，请重试')
  }
  reader.readAsDataURL(file)
}

const removeAvatarPreview = () => {
  avatarPreview.value = ''
  avatarFile.value = null
  if (avatarInput.value) {
    avatarInput.value.value = ''
  }
}

// 裁剪器事件处理
const handleCropConfirm = (croppedImage: string) => {
  avatarPreview.value = croppedImage
  showCropper.value = false

  // 将裁剪后的图片转换为File对象
  fetch(croppedImage)
    .then(res => res.blob())
    .then(blob => {
      const file = new File([blob], 'avatar.png', { type: 'image/png' })
      avatarFile.value = file
      showSuccess('头像裁剪完成，请保存更改')
    })
    .catch(() => {
      showError('头像处理失败，请重试')
    })
}

const handleCropCancel = () => {
  showCropper.value = false
  cropperImageSrc.value = ''
  // 清除文件输入
  if (avatarInput.value) {
    avatarInput.value.value = ''
  }
}

const uploadAvatar = async () => {
  if (!avatarFile.value) return

  isUploadingAvatar.value = true
  try {
    // 调用API上传头像
    const response = await authApi.uploadAvatar(avatarFile.value)

    // 从响应中提取用户数据
    const userData = (response as any).data || response

    // 更新用户头像
    userInfo.avatar = userData.avatar || avatarPreview.value

    // 清除预览
    avatarPreview.value = ''
    avatarFile.value = null

    // 清除文件输入
    if (avatarInput.value) {
      avatarInput.value.value = ''
    }

    // 保存到本地存储
    localStorage.setItem('user', JSON.stringify(userInfo))

    // 通知其他组件头像已更新
    avatarManager.notifyAvatarUpdated(userData)

    showSuccess('头像上传成功！')
    console.log('头像上传成功')
  } catch (error: any) {
    console.error('头像上传失败:', error)
    showError(error?.message || '头像上传失败，请重试')
  } finally {
    isUploadingAvatar.value = false
  }
}

// 选择预设头像
const selectPresetAvatar = (avatarUrl: string) => {
  userInfo.avatar = avatarUrl
  showPresetAvatars.value = false

  // 保存到本地存储
  localStorage.setItem('user', JSON.stringify(userInfo))

  // 通知其他组件头像已更新
  avatarManager.notifyAvatarUpdated(userInfo)

  showSuccess('头像更换成功！')
}

// 立即上传头像（不保存其他信息）
const uploadAvatarOnly = async () => {
  if (!avatarFile.value) {
    showWarning('请先选择头像图片')
    return
  }

  await uploadAvatar()
}

// 方法
const updateProfile = async () => {
  isUpdating.value = true
  try {
    const params = {
      ...profileForm
    }

    // 调用接口更新用户信息
    const response: any = await authApi.updateProfile(params)

    // 从响应中提取用户数据
    const userData = response.data || response

    // 更新用户信息
    Object.assign(userInfo, userData)

    // 保存到本地存储
    localStorage.setItem('user', JSON.stringify(userInfo))

    // 通知其他组件用户信息已更新
    avatarManager.notifyAvatarUpdated(userData)

    showSuccess('个人资料更新成功！')
  } catch (error: any) {
    console.error('更新失败:', error)
    showError(error || '个人资料更新失败，请重试')
  } finally {
    isUpdating.value = false
  }
}

const updateSensitiveInfo = async () => {
  // 验证表单
  if (!securityForm.email) {
    showWarning('请输入邮箱地址')
    return
  }

  if (!securityForm.phone) {
    showWarning('请输入手机号码')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(securityForm.email)) {
    showError('请输入有效的邮箱地址')
    return
  }

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(securityForm.phone.replace(/\D/g, ''))) {
    showError('请输入有效的手机号码')
    return
  }

  isUpdatingSecurity.value = true
  try {
    // 调用接口更新敏感信息
    const response = await authApi.updateProfile({
      email: securityForm.email,
      phone: securityForm.phone
    })

    // 从响应中提取用户数据
    const userData = (response as any).data || response

    // 更新用户信息
    Object.assign(userInfo, userData)

    // 保存到本地存储
    localStorage.setItem('user', JSON.stringify(userInfo))

    // 通知其他组件用户信息已更新
    avatarManager.notifyAvatarUpdated(userData)

    showSuccess('敏感信息更新成功！')
  } catch (error: any) {
    console.error('更新失败:', error)
    showError(error || '敏感信息更新失败，请重试')
  } finally {
    isUpdatingSecurity.value = false
  }
}

const updatePassword = async () => {
  // 验证表单
  if (!passwordForm.currentPassword) {
    console.log(1);

    showWarning('请输入当前密码')
    return
  }

  if (!passwordForm.newPassword) {
    showWarning('请输入新密码')
    return
  }

  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    showError('新密码和确认密码不匹配')
    return
  }

  if (passwordForm.newPassword.length < 6) {
    showError('密码长度至少6位')
    return
  }

  if (passwordForm.newPassword === passwordForm.currentPassword) {
    showWarning('新密码不能与当前密码相同')
    return
  }

  isUpdatingPassword.value = true
  try {
    // 模拟API调用
    const response = await authApi.updatePassword({
      old_password: passwordForm.currentPassword,
      new_password: passwordForm.newPassword,
      confirm_password: passwordForm.confirmPassword
    })

    console.log(response)

    showSuccess('密码更新成功！')
    console.log('密码更新成功')

    // 清空表单
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  } catch (error: any) {
    console.error('密码更新失败:', error)
    showError(error || '密码更新失败，请重试')
  } finally {
    isUpdatingPassword.value = false
  }
}

// 从API获取用户数据
const fetchUserData = async () => {
  try {
    const response: any = await authApi.getProfile()
    // 从响应中提取用户数据
    const userData = response.data || response
    Object.assign(userInfo, userData)
    Object.assign(profileForm, {
      nickname: userData.nickname,
      bio: userData.bio
    })
    Object.assign(securityForm, {
      email: userData.email,
      phone: userData.phone
    })
  } catch (error) {
    console.error('获取用户数据失败:', error)
  }
}

// 生命周期
onMounted(() => {
  fetchUserData()
})
</script>
