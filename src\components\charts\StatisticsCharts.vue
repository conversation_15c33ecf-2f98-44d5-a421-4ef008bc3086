<template>
  <div class="space-y-8">
    <!-- 总体统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-primary">总记录数</h3>
          <div v-if="statsLoading" class="flex items-center justify-center h-12">
            <div class="loading loading-spinner loading-md text-primary"></div>
          </div>
          <div v-else class="text-3xl font-bold">{{ overallStats.totalRecords.toLocaleString() }}</div>
          <p class="text-base-content/70">威胁情报记录总数</p>
        </div>
      </div>

      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-success">涉及国家</h3>
          <div v-if="statsLoading" class="flex items-center justify-center h-12">
            <div class="loading loading-spinner loading-md text-success"></div>
          </div>
          <div v-else class="text-3xl font-bold">{{ overallStats.uniqueCountries }}</div>
          <p class="text-base-content/70">受影响的国家数量</p>
        </div>
      </div>

      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-warning">勒索组织</h3>
          <div v-if="statsLoading" class="flex items-center justify-center h-12">
            <div class="loading loading-spinner loading-md text-warning"></div>
          </div>
          <div v-else class="text-3xl font-bold">{{ overallStats.uniqueGroups }}</div>
          <p class="text-base-content/70">活跃的勒索组织</p>
        </div>
      </div>

      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h3 class="card-title text-error">行业领域</h3>
          <div v-if="statsLoading" class="flex items-center justify-center h-12">
            <div class="loading loading-spinner loading-md text-error"></div>
          </div>
          <div v-else class="text-3xl font-bold">{{ overallStats.uniqueActivities }}</div>
          <p class="text-base-content/70">受影响的行业数量</p>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 国家分布饼图 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title mb-4">受害者地理分布</h2>
          <div class="relative h-96">
            <div id="country-pie-chart" class="h-full"></div>
            <!-- 加载状态 -->
            <div v-if="countryChartLoading" class="absolute inset-0 flex items-center justify-center bg-base-100 rounded-lg">
              <div class="text-center">
                <div class="loading loading-spinner loading-lg text-primary mb-4"></div>
                <p class="text-base-content/70">正在加载地理分布数据...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 勒索组织活跃度柱状图 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title mb-4">勒索组织活跃度</h2>
          <div class="relative h-96">
            <div id="group-bar-chart" class="h-full"></div>
            <!-- 加载状态 -->
            <div v-if="groupChartLoading" class="absolute inset-0 flex items-center justify-center bg-base-100 rounded-lg">
              <div class="text-center">
                <div class="loading loading-spinner loading-lg text-secondary mb-4"></div>
                <p class="text-base-content/70">正在加载组织活跃度数据...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 时间趋势图 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title mb-4">威胁事件时间趋势</h2>
        <div class="relative h-96">
          <div id="time-trend-chart" class="h-full"></div>
          <!-- 加载状态 -->
          <div v-if="trendChartLoading" class="absolute inset-0 flex items-center justify-center bg-base-100 rounded-lg">
            <div class="text-center">
              <div class="loading loading-spinner loading-lg text-accent mb-4"></div>
              <p class="text-base-content/70">正在加载时间趋势数据...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行业分布图 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title mb-4">行业分布统计</h2>
        <div class="relative h-96">
          <div id="activity-chart" class="h-full"></div>
          <!-- 加载状态 -->
          <div v-if="activityChartLoading" class="absolute inset-0 flex items-center justify-center bg-base-100 rounded-lg">
            <div class="text-center">
              <div class="loading loading-spinner loading-lg text-info mb-4"></div>
              <p class="text-base-content/70">正在加载行业分布数据...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 世界地图 -->
    <div class="card bg-base-100 shadow-xl">
      <div class="card-body">
        <h2 class="card-title mb-4">全球威胁分布地图</h2>
        <div class="relative">
          <!-- 地图容器 -->
          <div id="world-threat-map" class="h-96 w-full rounded-lg bg-base-200"></div>

          <!-- 加载状态 -->
          <div v-if="mapLoading" class="absolute inset-0 flex items-center justify-center bg-base-200 rounded-lg">
            <div class="text-center">
              <div class="loading loading-spinner loading-lg text-success mb-4"></div>
              <p class="text-base-content/70">正在加载地图数据...</p>
            </div>
          </div>

          <!-- 地图图例 -->
          <div v-if="!mapLoading" class="mt-4 flex flex-wrap items-center justify-center gap-4 text-sm">
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded" style="background-color: var(--color-error)"></div>
              <span class="text-base-content/80">高风险 (>100)</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded" style="background-color: var(--color-warning)"></div>
              <span class="text-base-content/80">中风险 (50-100)</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded" style="background-color: var(--color-info)"></div>
              <span class="text-base-content/80">低风险 (10-50)</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded" style="background-color: var(--color-success)"></div>
              <span class="text-base-content/80">极低风险 (<10)</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded bg-base-300"></div>
              <span class="text-base-content/80">无数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import ApexCharts from 'apexcharts';
import { DataAnalyzer, type VictimData } from '../../utils/dataAnalysis';

const overallStats = ref({
  totalRecords: 0,
  uniqueCountries: 0,
  uniqueGroups: 0,
  uniqueActivities: 0,
  earliestDate: '',
  latestDate: ''
});

// 各个组件的loading状态
const statsLoading = ref(true);
const countryChartLoading = ref(true);
const groupChartLoading = ref(true);
const trendChartLoading = ref(true);
const activityChartLoading = ref(true);
const mapLoading = ref(true);

let countryChart: ApexCharts | null = null;
let groupChart: ApexCharts | null = null;
let trendChart: ApexCharts | null = null;
let activityChart: ApexCharts | null = null;
let worldMap: any = null;

onMounted(async () => {
  try {
    // 加载数据
    const response = await fetch('/victims.json');
    const data: VictimData[] = await response.json();

    const analyzer = new DataAnalyzer(data);

    // 更新总体统计
    overallStats.value = analyzer.getOverallStats();
    statsLoading.value = false;

    // 并行创建图表以提高加载速度
    const chartPromises = [
      createCountryPieChart(analyzer),
      createGroupBarChart(analyzer),
      createTimeTrendChart(analyzer),
      createActivityChart(analyzer),
      createWorldThreatMap(analyzer)
    ];

    // 等待所有图表创建完成
    await Promise.allSettled(chartPromises);

  } catch (error) {
    console.error('加载数据失败:', error);
    // 出错时关闭所有loading状态
    statsLoading.value = false;
    countryChartLoading.value = false;
    groupChartLoading.value = false;
    trendChartLoading.value = false;
    activityChartLoading.value = false;
    mapLoading.value = false;
  }
});

// 创建国家分布饼图
async function createCountryPieChart(analyzer: DataAnalyzer) {
  try {
    const countryStats = analyzer.getCountryStats();

    const options = {
      chart: {
        type: 'donut',
        height: 380,
        background: 'transparent',
        toolbar: {
          show: false
        }
      },
      series: countryStats.map(stat => stat.count),
      labels: countryStats.map(stat => stat.countryName),
      colors: [
        'var(--color-primary)',
        'var(--color-secondary)',
        'var(--color-accent)',
        'var(--color-success)',
        'var(--color-warning)',
        'var(--color-error)',
        'var(--color-info)',
        '#8B5CF6',
        '#F59E0B',
        '#EF4444',
        '#10B981',
        '#3B82F6',
        '#6366F1',
        '#8B5CF6',
        '#F59E0B'
      ],
      plotOptions: {
        pie: {
          donut: {
            size: '60%',
            labels: {
              show: true,
              total: {
                show: true,
                label: '总计',
                color: 'var(--color-base-content)',
                formatter: function (w: any) {
                  return w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0).toLocaleString();
                }
              }
            }
          }
        }
      },
      legend: {
        position: 'bottom',
        labels: {
          colors: 'var(--color-base-content)'
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val: number) {
          return val.toFixed(1) + '%';
        },
        style: {
          colors: ['#fff']
        }
      },
      tooltip: {
        theme: 'dark',
        y: {
          formatter: function (val: number) {
            return val.toLocaleString() + ' 个受害者';
          }
        }
      }
    };

    const chartElement = document.querySelector('#country-pie-chart');
    if (chartElement) {
      countryChart = new ApexCharts(chartElement, options);
      await countryChart.render();
    }
  } catch (error) {
    console.error('创建国家分布饼图失败:', error);
  } finally {
    countryChartLoading.value = false;
  }
}

// 创建勒索组织柱状图
async function createGroupBarChart(analyzer: DataAnalyzer) {
  try {
    const groupStats = analyzer.getGroupStats();

    const options = {
      chart: {
        type: 'bar',
        height: 380,
        background: 'transparent',
        toolbar: {
          show: false
        }
      },
      series: [{
        name: '攻击次数',
        data: groupStats.map(stat => stat.count)
      }],
      xaxis: {
        categories: groupStats.map(stat => stat.group),
        labels: {
          style: {
            colors: 'var(--color-base-content)'
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: 'var(--color-base-content)'
          }
        }
      },
      colors: [
        'var(--color-primary)',
        'var(--color-secondary)',
        'var(--color-accent)',
        'var(--color-info)',
        'var(--color-success)',
        'var(--color-warning)',
        'var(--color-error)',
        '#8B5CF6', // 紫色
        '#06B6D4', // 青色
        '#10B981', // 绿色
        '#F59E0B', // 橙色
        '#EF4444', // 红色
        '#3B82F6', // 蓝色
        '#6366F1', // 靛蓝色
        '#EC4899'  // 粉色
      ],
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 4,
          distributed: true // 启用分布式颜色，每个柱子使用不同颜色
        }
      },
      dataLabels: {
        enabled: true,
        style: {
          colors: ['#fff']
        }
      },
      tooltip: {
        theme: 'dark',
        y: {
          formatter: function (val: number) {
            return val.toLocaleString() + ' 次攻击';
          }
        }
      },
      grid: {
        borderColor: 'color-mix(in oklab, var(--color-base-content) 20%, transparent)'
      },
      legend: {
        show: false // 隐藏图例，因为每个柱子都有不同颜色
      }
    };

    const chartElement = document.querySelector('#group-bar-chart');
    if (chartElement) {
      groupChart = new ApexCharts(chartElement, options);
      await groupChart.render();
    }
  } catch (error) {
    console.error('创建勒索组织柱状图失败:', error);
  } finally {
    groupChartLoading.value = false;
  }
}

// 创建时间趋势图
async function createTimeTrendChart(analyzer: DataAnalyzer) {
  try {
    const trendData = analyzer.getRecentMonthlyTrend();

    const options = {
      chart: {
        type: 'area',
        height: 380,
        background: 'transparent',
        zoom: {
          enabled: false
        },
        toolbar: {
          show: false
        }
      },
      series: [{
        name: '威胁事件数量',
        data: trendData.map(item => item.count)
      }],
      xaxis: {
        categories: trendData.map(item => item.month),
        labels: {
          style: {
            colors: 'var(--color-base-content)'
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: 'var(--color-base-content)'
          }
        }
      },
      colors: ['var(--color-primary)'],
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.3,
          stops: [0, 90, 100]
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2
      },
      dataLabels: {
        enabled: false
      },
      tooltip: {
        theme: 'dark',
        y: {
          formatter: function (val: number) {
            return val.toLocaleString() + ' 个事件';
          }
        }
      },
      grid: {
        borderColor: 'color-mix(in oklab, var(--color-base-content) 20%, transparent)'
      }
    };

    const chartElement = document.querySelector('#time-trend-chart');
    if (chartElement) {
      trendChart = new ApexCharts(chartElement, options);
      await trendChart.render();
    }
  } catch (error) {
    console.error('创建时间趋势图失败:', error);
  } finally {
    trendChartLoading.value = false;
  }
}

// 创建行业分布图
async function createActivityChart(analyzer: DataAnalyzer) {
  try {
    const activityStats = analyzer.getActivityStats();

    const options = {
      chart: {
        type: 'bar',
        height: 380,
        background: 'transparent',
        toolbar: {
          show: false
        }
      },
      series: [{
        name: '受害者数量',
        data: activityStats.map(stat => stat.count)
      }],
      xaxis: {
        categories: activityStats.map(stat => stat.activityName),
        labels: {
          style: {
            colors: 'var(--color-base-content)'
          },
          rotate: -45
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: 'var(--color-base-content)'
          }
        }
      },
      colors: [
        'var(--color-primary)',
        'var(--color-secondary)',
        'var(--color-accent)',
        'var(--color-info)',
        'var(--color-success)',
        'var(--color-warning)',
        'var(--color-error)',
        '#8B5CF6', // 紫色
        '#06B6D4', // 青色
        '#10B981', // 绿色
        '#F59E0B', // 橙色
        '#EF4444'  // 红色
      ],
      plotOptions: {
        bar: {
          borderRadius: 4,
          columnWidth: '60%',
          distributed: true // 启用分布式颜色，每个柱子使用不同颜色
        }
      },
      dataLabels: {
        enabled: false
      },
      tooltip: {
        theme: 'dark',
        y: {
          formatter: function (val: number) {
            return val.toLocaleString() + ' 个受害者';
          }
        }
      },
      grid: {
        borderColor: 'color-mix(in oklab, var(--color-base-content) 20%, transparent)'
      },
      legend: {
        show: false // 隐藏图例，因为每个柱子都有不同颜色
      }
    };

    const chartElement = document.querySelector('#activity-chart');
    if (chartElement) {
      activityChart = new ApexCharts(chartElement, options);
      await activityChart.render();
    }
  } catch (error) {
    console.error('创建行业分布图失败:', error);
  } finally {
    activityChartLoading.value = false;
  }
}

// 创建世界威胁分布地图
async function createWorldThreatMap(analyzer: DataAnalyzer) {
  try {
    // 动态加载Datamaps依赖
    await loadDatamapsDependencies();

    const countryStats = analyzer.getGeographicDistribution();
    const threatData = prepareThreatMapData(countryStats);

    const mapElement = document.querySelector('#world-threat-map');
    if (!mapElement) {
      console.error('地图容器元素未找到');
      return;
    }

    // 初始化Datamap
    worldMap = new (window as any).Datamap({
      element: mapElement,
      projection: 'mercator',
      responsive: true,
      fills: {
        defaultFill: 'color-mix(in oklab, var(--color-base-300) 60%, transparent)',
        HIGH: 'color-mix(in oklab, var(--color-error) 80%, transparent)',
        MEDIUM: 'color-mix(in oklab, var(--color-warning) 80%, transparent)',
        LOW: 'color-mix(in oklab, var(--color-info) 80%, transparent)',
        VERY_LOW: 'color-mix(in oklab, var(--color-success) 80%, transparent)'
      },
      data: threatData,
      geographyConfig: {
        borderColor: 'color-mix(in oklab, var(--color-base-content) 30%, transparent)',
        borderWidth: 0.5,
        highlightFillColor: 'color-mix(in oklab, var(--color-primary) 40%, transparent)',
        highlightBorderColor: 'var(--color-primary)',
        highlightBorderWidth: 2,
        popupTemplate: function (geo: any, data: any) {
          const countryName = data && data.countryName ? data.countryName : geo.properties.name;
          const threatCount = data ? data.count : 0;
          const percentage = data ? data.percentage : 0;

          return `
            <div class="bg-base-100 rounded-lg overflow-hidden shadow-lg min-w-48">
              <div class="bg-primary/10 p-3 border-b border-base-300">
                <h3 class="font-semibold text-base-content">${countryName}</h3>
              </div>
              <div class="p-3 space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-base-content/70">威胁事件:</span>
                  <span class="font-medium text-base-content">${threatCount.toLocaleString()}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-base-content/70">占比:</span>
                  <span class="font-medium text-base-content">${percentage}%</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-base-content/70">风险等级:</span>
                  <span class="badge ${getRiskBadgeClass(threatCount)}">${getRiskLevel(threatCount)}</span>
                </div>
              </div>
            </div>
          `;
        }
      }
    });

    // 添加窗口大小调整监听器
    window.addEventListener('resize', () => {
      if (worldMap) {
        worldMap.resize();
      }
    });

  } catch (error) {
    console.error('创建世界地图失败:', error);
  } finally {
    mapLoading.value = false;
  }
}

// 动态加载Datamaps依赖
async function loadDatamapsDependencies() {
  // 检查是否已经加载
  if ((window as any).Datamap) {
    return;
  }

  // 加载D3.js
  if (!(window as any).d3) {
    await loadScript('https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.3/d3.min.js');
  }

  // 加载TopoJSON
  if (!(window as any).topojson) {
    await loadScript('https://cdnjs.cloudflare.com/ajax/libs/topojson/1.6.9/topojson.min.js');
  }

  // 加载Datamaps
  if (!(window as any).Datamap) {
    await loadScript('https://cdn.jsdelivr.net/npm/datamaps@0.5.9/dist/datamaps.all.min.js');
  }
}

// 加载外部脚本的辅助函数
function loadScript(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
    document.head.appendChild(script);
  });
}

// 准备威胁地图数据
function prepareThreatMapData(countryStats: any[]) {
  const threatData: any = {};

  // 国家代码映射表（ISO 2字母代码到ISO 3字母代码）
  const countryCodeMap: { [key: string]: string } = {
    'US': 'USA', 'CN': 'CHN', 'DE': 'DEU', 'GB': 'GBR', 'JP': 'JPN',
    'CA': 'CAN', 'AU': 'AUS', 'FR': 'FRA', 'IT': 'ITA', 'RU': 'RUS',
    'BR': 'BRA', 'IN': 'IND', 'KR': 'KOR', 'ES': 'ESP', 'NL': 'NLD',
    'SE': 'SWE', 'NO': 'NOR', 'DK': 'DNK', 'FI': 'FIN', 'CH': 'CHE',
    'AT': 'AUT', 'BE': 'BEL', 'PL': 'POL', 'CZ': 'CZE', 'HU': 'HUN',
    'PT': 'PRT', 'GR': 'GRC', 'IE': 'IRL', 'IL': 'ISR', 'TR': 'TUR',
    'ZA': 'ZAF', 'EG': 'EGY', 'NG': 'NGA', 'KE': 'KEN', 'MA': 'MAR',
    'MX': 'MEX', 'AR': 'ARG', 'CL': 'CHL', 'CO': 'COL', 'PE': 'PER',
    'TH': 'THA', 'VN': 'VNM', 'MY': 'MYS', 'SG': 'SGP', 'ID': 'IDN',
    'PH': 'PHL', 'TW': 'TWN', 'HK': 'HKG', 'NZ': 'NZL', 'UA': 'UKR',
    'RO': 'ROU', 'BG': 'BGR', 'HR': 'HRV', 'SI': 'SVN', 'SK': 'SVK',
    'LT': 'LTU', 'LV': 'LVA', 'EE': 'EST', 'IS': 'ISL', 'LU': 'LUX',
    'MT': 'MLT', 'CY': 'CYP', 'AE': 'ARE', 'SA': 'SAU', 'QA': 'QAT',
    'KW': 'KWT', 'BH': 'BHR', 'OM': 'OMN', 'JO': 'JOR', 'LB': 'LBN',
    'SY': 'SYR', 'IQ': 'IRQ', 'IR': 'IRN', 'AF': 'AFG', 'PK': 'PAK',
    'BD': 'BGD', 'LK': 'LKA', 'NP': 'NPL', 'MM': 'MMR', 'KH': 'KHM',
    'LA': 'LAO', 'MN': 'MNG', 'KZ': 'KAZ', 'UZ': 'UZB', 'TM': 'TKM',
    'KG': 'KGZ', 'TJ': 'TJK', 'GE': 'GEO', 'AM': 'ARM', 'AZ': 'AZE',
    'BY': 'BLR', 'MD': 'MDA', 'RS': 'SRB', 'BA': 'BIH', 'ME': 'MNE',
    'MK': 'MKD', 'AL': 'ALB', 'XK': 'XKX', 'EC': 'ECU', 'BO': 'BOL',
    'PY': 'PRY', 'UY': 'URY', 'VE': 'VEN', 'GY': 'GUY', 'SR': 'SUR',
    'FK': 'FLK', 'GF': 'GUF'
  };

  countryStats.forEach(stat => {
    const countryCode = countryCodeMap[stat.country] || stat.country;
    if (countryCode) {
      threatData[countryCode] = {
        count: stat.count,
        percentage: stat.percentage,
        countryName: stat.countryName,
        fillKey: getThreatLevel(stat.count)
      };
    }
  });

  return threatData;
}

// 根据威胁数量确定威胁等级
function getThreatLevel(count: number): string {
  if (count >= 100) return 'HIGH';
  if (count >= 50) return 'MEDIUM';
  if (count >= 10) return 'LOW';
  if (count > 0) return 'VERY_LOW';
  return 'defaultFill';
}

// 获取风险等级文本
function getRiskLevel(count: number): string {
  if (count >= 100) return '高风险';
  if (count >= 50) return '中风险';
  if (count >= 10) return '低风险';
  if (count > 0) return '极低风险';
  return '无数据';
}

// 获取风险等级徽章样式
function getRiskBadgeClass(count: number): string {
  if (count >= 100) return 'badge-error';
  if (count >= 50) return 'badge-warning';
  if (count >= 10) return 'badge-info';
  if (count > 0) return 'badge-success';
  return 'badge-ghost';
}

// 组件卸载时清理图表
onUnmounted(() => {
  if (countryChart) countryChart.destroy();
  if (groupChart) groupChart.destroy();
  if (trendChart) trendChart.destroy();
  if (activityChart) activityChart.destroy();
  if (worldMap) {
    // Datamaps没有destroy方法，但我们可以清理DOM
    const mapElement = document.querySelector('#world-threat-map');
    if (mapElement) {
      mapElement.innerHTML = '';
    }
  }
});
</script>
