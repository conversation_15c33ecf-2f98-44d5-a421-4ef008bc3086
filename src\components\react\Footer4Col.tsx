'use client';

import React from 'react';
import {
  Mail,
  MapPin,
  Phone,
} from 'lucide-react';

// 移除社交媒体链接，因为图标已弃用
const socialLinks: any[] = [];

const aboutLinks = [
  { text: '公司简介', href: '#' },
  { text: '团队介绍', href: '#' },
  { text: '发展历程', href: '#' },
  { text: '加入我们', href: '#' },
];

const serviceLinks = [
  { text: '威胁情报管理', href: '#' },
  { text: '勒索组织分析', href: '#' },
  { text: '安全事件监控', href: '#' },
  { text: '数据统计分析', href: '#' },
];

const helpfulLinks = [
  { text: '常见问题', href: '#' },
  { text: '技术支持', href: '#' },
  { text: '在线客服', href: '#', hasIndicator: true },
];

const contactInfo = [
  { icon: Mail, text: '<EMAIL>' },
  { icon: Phone, text: '400-6136-816' },
  { icon: MapPin, text: '山东省市中区凯瑞大厦', isAddress: true },
];

export default function Footer4Col() {
  return (
    <footer className="mt-16 w-full place-self-end rounded-t-xl bg-base-200">
      <div className="mx-auto max-w-screen-xl px-4 pb-6 pt-16 sm:px-6 lg:px-8 lg:pt-24">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          <div>
            <div className="flex justify-center gap-2 text-primary sm:justify-start">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                <span className="text-primary-content font-bold text-sm">威</span>
              </div>
              <span className="text-2xl font-semibold text-base-content">
                威胁情报数据中心
              </span>
            </div>

            <p className="mt-6 max-w-md text-center leading-relaxed text-base-content/70 sm:max-w-xs sm:text-left">
              专业的网络安全威胁情报平台，整合暗网、Telegram和人工情报，运用AI技术提供全面的威胁情报服务。
            </p>

            <ul className="mt-8 flex justify-center gap-6 sm:justify-start md:gap-8">
              {socialLinks.map(({ icon: Icon, label }) => (
                <li key={label}>
                  <a
                    href="#"
                    className="text-primary transition hover:text-primary/80"
                  >
                    <span className="sr-only">{label}</span>
                    <Icon className="size-6" />
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4 lg:col-span-2">
            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-base-content">关于我们</p>

              <ul className="mt-8 space-y-4 text-sm">
                {aboutLinks.map(({ text, href }) => (
                  <li key={text}>
                    <a
                      className="text-base-content/70 transition hover:text-base-content"
                      href={href}
                    >
                      {text}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-base-content">产品服务</p>

              <ul className="mt-8 space-y-4 text-sm">
                {serviceLinks.map(({ text, href }) => (
                  <li key={text}>
                    <a
                      className="text-base-content/70 transition hover:text-base-content"
                      href={href}
                    >
                      {text}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-base-content">帮助中心</p>

              <ul className="mt-8 space-y-4 text-sm">
                {helpfulLinks.map(({ text, href, hasIndicator }) => (
                  <li key={text}>
                    <a
                      href={href}
                      className={`${
                        hasIndicator
                          ? 'group flex justify-center gap-1.5 sm:justify-start'
                          : 'text-base-content/70 transition hover:text-base-content'
                      }`}
                    >
                      <span className="text-base-content/70 transition">
                        {text}
                      </span>
                      {hasIndicator && (
                        <span className="relative flex size-2">
                          <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-primary opacity-75" />
                          <span className="relative inline-flex size-2 rounded-full bg-primary" />
                        </span>
                      )}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <div className="text-center sm:text-left">
              <p className="text-lg font-medium text-base-content">联系我们</p>

              <ul className="mt-8 space-y-4 text-sm">
                {contactInfo.map(({ icon: Icon, text, isAddress }) => (
                  <li key={text}>
                    <a
                      className="flex items-center justify-center gap-1.5 sm:justify-start"
                      href="#"
                    >
                      <Icon className="size-5 shrink-0 text-primary shadow-sm" />
                      {isAddress ? (
                        <address className="-mt-0.5 flex-1 not-italic text-base-content/70 transition">
                          {text}
                        </address>
                      ) : (
                        <span className="flex-1 text-base-content/70 transition">
                          {text}
                        </span>
                      )}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-12 border-t border-base-300 pt-6">
          <div className="text-center sm:flex sm:justify-between sm:text-left">
            <p className="text-sm text-base-content/70">
              <span className="block sm:inline">保留所有权利</span>
            </p>

            <p className="text-base-content/70 mt-4 text-sm transition sm:order-first sm:mt-0">
              &copy; 2025 威胁情报数据中心
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
