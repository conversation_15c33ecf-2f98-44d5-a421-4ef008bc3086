'use client';

import Earth from '@/components/ui/globe';
import { motion } from 'framer-motion';
import { Skull, Bug, Calendar, BarChart3 } from 'lucide-react';
import Btn09 from '@/components/ui/Btn09';

export default function GlobeCard() {
  // 跳转到搜索中心的函数
  const scrollToSearchCenter = () => {
    // 轻微延迟，让按钮动画完成
    setTimeout(() => {
      const searchCenter = document.getElementById('search-center');
      if (searchCenter) {
        // 计算目标位置
        const targetPosition = searchCenter.offsetTop;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        const duration = 800; // 优化为800ms，平滑且响应迅速
        let start: number | null = null;

        // 自定义缓动函数 (easeInOutQuart) - 平滑的缓动
        const easeInOutQuart = (t: number): number => {
          return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t;
        };

        const animation = (currentTime: number) => {
          if (start === null) start = currentTime;
          const timeElapsed = currentTime - start;
          const progress = Math.min(timeElapsed / duration, 1);

          const ease = easeInOutQuart(progress);
          window.scrollTo(0, startPosition + distance * ease);

          if (timeElapsed < duration) {
            requestAnimationFrame(animation);
          }
        };

        requestAnimationFrame(animation);
      }
    }, 50); // 减少延迟到50ms
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
      className="relative max-w-6xl mx-auto"
    >
      {/* 标题区域 */}
      <div className="text-center mb-12">
        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-4xl lg:text-5xl xl:text-6xl font-bold text-foreground mb-8 leading-tight tracking-wide"
        >
          智能化<span className="text-primary">威胁情报</span>
          <br />
          数据中心
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="text-muted-foreground text-lg lg:text-xl max-w-4xl mx-auto mb-10 leading-relaxed tracking-wide"
        >
          整合暗网、Telegram和人工情报，运用AI技术为您提供全面的勒索组织、攻击事件
          等威胁情报信息，助力网络安全防护。
        </motion.p>

        {/* 按钮区域 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <Btn09 onClick={scrollToSearchCenter}>
            立即开始探索
          </Btn09>
        </motion.div>
      </div>

      {/* 地球仪 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.5 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 1.2 }}
        className="flex justify-center mb-8 relative"
      >
        {/* 添加光韵溢出容器 */}
        <div className="absolute inset-0 overflow-visible pointer-events-none">
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(0,82,255,0.3)_0%,transparent_70%)]"></div>
        </div>

        {/* 调整地球尺寸和位置 - 性能优化：缩小尺寸提升流畅度 */}
        <Earth
          className="w-[60vw] h-[60vw] max-w-[24rem] max-h-[24rem]"
          dark={0.8}
          scale={1.0}
          baseColor={[0.0, 0.52, 1.0]}
          markerColor={[1.0, 0.3, 0.3]}
          glowColor={[0.0, 0.52, 1.0]}
          mapSamples={15000}
        />
      </motion.div>

      {/* 统计信息 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.4 }}
        className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8"
      >
        {/* 活跃勒索家族统计卡片 */}
        <motion.div
          whileHover={{ scale: 1.02, y: -2 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
          className="relative group"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 via-red-500/10 to-transparent rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
          <div className="relative bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 lg:p-6 text-center hover:shadow-lg hover:shadow-red-500/10 transition-all duration-300">
            <div className="flex items-center justify-center mb-3">
              <div className="p-2 bg-red-500/10 rounded-lg">
                <Skull className="h-5 w-5 lg:h-6 lg:w-6 text-red-500" />
              </div>
            </div>
            <div className="text-2xl lg:text-3xl font-bold text-red-500 mb-1">260</div>
            <div className="text-sm lg:text-base text-muted-foreground mb-2">活跃勒索家族</div>
            <div className="flex items-center justify-center text-xs text-orange-500">
              <span className="mr-1">+3</span>
              <span className="text-muted-foreground">本月新增</span>
            </div>
          </div>
        </motion.div>

        {/* 受害者数量统计卡片 */}
        <motion.div
          whileHover={{ scale: 1.02, y: -2 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
          className="relative group"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 via-orange-500/10 to-transparent rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
          <div className="relative bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 lg:p-6 text-center hover:shadow-lg hover:shadow-orange-500/10 transition-all duration-300">
            <div className="flex items-center justify-center mb-3">
              <div className="p-2 bg-orange-500/10 rounded-lg">
                <Bug className="h-5 w-5 lg:h-6 lg:w-6 text-orange-500" />
              </div>
            </div>
            <div className="text-2xl lg:text-3xl font-bold text-orange-500 mb-1">15,420</div>
            <div className="text-sm lg:text-base text-muted-foreground mb-2">受害者数量</div>
            <div className="flex items-center justify-center text-xs text-red-500">
              <span className="mr-1">+127</span>
              <span className="text-muted-foreground">今日新增</span>
            </div>
          </div>
        </motion.div>

        {/* 本月受害者统计卡片 */}
        <motion.div
          whileHover={{ scale: 1.02, y: -2 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
          className="relative group"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-blue-500/10 to-transparent rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
          <div className="relative bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 lg:p-6 text-center hover:shadow-lg hover:shadow-blue-500/10 transition-all duration-300">
            <div className="flex items-center justify-center mb-3">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <Calendar className="h-5 w-5 lg:h-6 lg:w-6 text-blue-500" />
              </div>
            </div>
            <div className="text-2xl lg:text-3xl font-bold text-blue-500 mb-1">1,247</div>
            <div className="text-sm lg:text-base text-muted-foreground mb-2">本月受害者</div>
            <div className="flex items-center justify-center text-xs text-red-500">
              <span className="mr-1">+18%</span>
              <span className="text-muted-foreground">较上月</span>
            </div>
          </div>
        </motion.div>

        {/* 本年受害者统计卡片 */}
        <motion.div
          whileHover={{ scale: 1.02, y: -2 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
          className="relative group"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-purple-500/10 to-transparent rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
          <div className="relative bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 lg:p-6 text-center hover:shadow-lg hover:shadow-purple-500/10 transition-all duration-300">
            <div className="flex items-center justify-center mb-3">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <BarChart3 className="h-5 w-5 lg:h-6 lg:w-6 text-purple-500" />
              </div>
            </div>
            <div className="text-2xl lg:text-3xl font-bold text-purple-500 mb-1">8,956</div>
            <div className="text-sm lg:text-base text-muted-foreground mb-2">本年受害者</div>
            <div className="flex items-center justify-center text-xs text-red-500">
              <span className="mr-1">+32%</span>
              <span className="text-muted-foreground">较去年</span>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
