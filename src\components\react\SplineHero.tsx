'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import GlobeCard from './GlobeCard';

// 类型定义
type StarSize = 'small' | 'medium' | 'large';

interface Star {
  id: string;
  left: number;
  top: number;
  size: StarSize;
  duration: number;
  delay: number;
  opacity: number;
}

interface ConstellationPoint {
  x: number;
  y: number;
}

interface Constellation {
  id: string;
  points: ConstellationPoint[];
}

interface Meteor {
  id: string;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  duration: number;
  delay: number;
}

// 简单的伪随机数生成器，使用固定种子
class SeededRandom {
  private seed: number;

  constructor(seed: number) {
    this.seed = seed;
  }

  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }
}

// {{CHENGQI:
// Action: Modified
// Timestamp: [2024-12-19 20:30:00 +08:00]
// Reason: Per P3-DEV-001 to reduce star count from 63 to 25 for performance optimization
// Principle_Applied: KISS - 简化星星生成逻辑，减少动画元素数量
// Optimization: 减少60%的动画元素，大幅提升渲染性能
// Architectural_Note (AR): 符合分层渲染架构，保持视觉层次
// }}
// 生成优化后的星星数据（性能优化：63个减少到25个）
const generateStars = (): Star[] => {
  const stars: Star[] = [];
  const rng = new SeededRandom(12345); // 固定种子确保一致性

  // 小星星 (40个 → 15个)
  for (let i = 0; i < 15; i++) {
    stars.push({
      id: `small-${i}`,
      left: rng.next() * 100,
      top: rng.next() * 100,
      size: 'small',
      duration: 2 + rng.next() * 3,
      delay: rng.next() * 5,
      opacity: 0.3 + rng.next() * 0.4,
    });
  }

  // 中等星星 (15个 → 7个)
  for (let i = 0; i < 7; i++) {
    stars.push({
      id: `medium-${i}`,
      left: rng.next() * 100,
      top: rng.next() * 100,
      size: 'medium',
      duration: 3 + rng.next() * 4,
      delay: rng.next() * 5,
      opacity: 0.4 + rng.next() * 0.4,
    });
  }

  // 大星星 (8个 → 3个)
  for (let i = 0; i < 3; i++) {
    stars.push({
      id: `large-${i}`,
      left: rng.next() * 100,
      top: rng.next() * 100,
      size: 'large',
      duration: 4 + rng.next() * 3,
      delay: rng.next() * 5,
      opacity: 0.5 + rng.next() * 0.3,
    });
  }

  return stars;
};

// 生成星座连线数据（简化版）
const generateConstellations = (): Constellation[] => {
  return [
    // 星座1（简化）
    {
      id: 'constellation-1',
      points: [
        { x: 15, y: 20 },
        { x: 25, y: 15 },
        { x: 35, y: 25 },
      ],
    },
    // 星座2（简化）
    {
      id: 'constellation-2',
      points: [
        { x: 60, y: 30 },
        { x: 70, y: 25 },
        { x: 75, y: 35 },
      ],
    },
  ];
};

// 生成流星数据（简化版）
const generateMeteors = (): Meteor[] => {
  return [
    {
      id: 'meteor-1',
      startX: -10,
      startY: 20,
      endX: 110,
      endY: 80,
      duration: 2,
      delay: 8,
    },
    {
      id: 'meteor-2',
      startX: -10,
      startY: 60,
      endX: 110,
      endY: 90,
      duration: 1.5,
      delay: 15,
    },
  ];
};

// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-01-26 22:50:00 +08:00]
// Reason: 修复React水合错误 - 将静态性能配置改为状态管理，确保SSR和客户端渲染一致性
// Principle_Applied: SOLID - 单一职责原则，分离服务器端和客户端的性能检测逻辑
// Optimization: 消除水合错误，提升应用稳定性和用户体验
// Architectural_Note (AR): 使用React状态管理确保渲染一致性，符合SSR最佳实践
// }}

// {{CHENGQI:
// Action: Added
// Timestamp: [2024-12-19 20:50:00 +08:00]
// Reason: Per P3-DEV-008 to extract animation configurations for better maintainability
// Principle_Applied: DRY - 避免重复的动画配置，SOLID - 单一职责原则分离配置和逻辑
// Optimization: 提高代码可维护性，便于后续调优和扩展
// Architectural_Note (AR): 配置与逻辑分离，符合架构设计原则
// }}
// 注释：移除未使用的 ANIMATION_CONFIG 常量以消除警告

// 预生成所有数据以确保SSR和客户端一致性
const stars = generateStars();
const constellations = generateConstellations();
const meteors = generateMeteors();

// {{CHENGQI:
// Action: Added
// Timestamp: [2024-12-19 20:35:00 +08:00]
// Reason: Per P3-DEV-002 to add GPU acceleration properties for all animated elements
// Principle_Applied: SOLID - 不修改现有逻辑，仅增强性能属性
// Optimization: 添加will-change和transform3d强制GPU层合成，减少主线程负担
// Architectural_Note (AR): 符合GPU硬件加速策略，提升渲染性能
// }}
export default function SplineHero() {
  // 性能配置状态管理 - 修复水合错误
  const [performanceConfig, setPerformanceConfig] = useState({
    isLowPerformance: false, // 初始值与服务器端一致，避免水合错误
    reducedMotion: false
  });

  // 客户端挂载后检测性能配置
  useEffect(() => {
    setPerformanceConfig({
      isLowPerformance: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
        window.innerWidth < 768,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
    });
  }, []);

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-background via-background to-muted/80 overflow-hidden">


      {/* 背景网格 */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 133, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 133, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* 星星层 - 添加性能优化 */}
      <div className="absolute inset-0" style={{ willChange: 'transform, opacity' }}>
        {stars.map((star) => {
          const sizeClasses: Record<StarSize, string> = {
            small: 'w-1 h-1',
            medium: 'w-1.5 h-1.5',
            large: 'w-2 h-2',
          };

          return (
            <motion.div
              key={star.id}
              className={`absolute ${sizeClasses[star.size]} bg-white rounded-full`}
              style={{
                left: `${star.left}%`,
                top: `${star.top}%`,
                opacity: star.opacity,
                willChange: 'transform, opacity',
                transform: 'translate3d(0, 0, 0)',
              }}
              animate={{
                opacity: [star.opacity * 0.3, star.opacity, star.opacity * 0.3],
                transform: [
                  'translate3d(0, 0, 0) scale(0.8)',
                  'translate3d(0, 0, 0) scale(1.2)',
                  'translate3d(0, 0, 0) scale(0.8)',
                ],
              }}
              transition={{
                duration: star.duration,
                repeat: Infinity,
                delay: star.delay,
                ease: 'easeInOut',
              }}
            />
          );
        })}
      </div>

      {/* 星座连线层 - 简化动画 (低性能设备时隐藏) */}
      {!performanceConfig.isLowPerformance && !performanceConfig.reducedMotion && (
        <div className="absolute inset-0" style={{ willChange: 'transform, opacity' }}>
          {constellations.map((constellation) => (
          <svg
            key={constellation.id}
            className="absolute inset-0 w-full h-full"
            style={{
              pointerEvents: 'none',
              willChange: 'transform, opacity',
              transform: 'translate3d(0, 0, 0)',
            }}
          >
            {constellation.points.map((point, index) => {
              if (index === constellation.points.length - 1) return null;
              const nextPoint = constellation.points[index + 1];

              return (
                <motion.line
                  key={`${constellation.id}-line-${index}`}
                  x1={`${point.x}%`}
                  y1={`${point.y}%`}
                  x2={`${nextPoint.x}%`}
                  y2={`${nextPoint.y}%`}
                  stroke="rgba(0, 133, 255, 0.3)"
                  strokeWidth="1"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 0.6, 0],
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    delay: index * 0.5 + (index % 3) * 1.2,
                    ease: 'easeInOut',
                  }}
                />
              );
            })}

            {/* 星座节点 */}
            {constellation.points.map((point, index) => (
              <motion.circle
                key={`${constellation.id}-point-${index}`}
                cx={`${point.x}%`}
                cy={`${point.y}%`}
                r="2"
                fill="rgba(0, 133, 255, 0.8)"
                initial={{ scale: 0, opacity: 0 }}
                animate={{
                  transform: [
                    'translate3d(0, 0, 0) scale(0)',
                    'translate3d(0, 0, 0) scale(1.2)',
                    'translate3d(0, 0, 0) scale(1)',
                    'translate3d(0, 0, 0) scale(1.2)',
                    'translate3d(0, 0, 0) scale(0)',
                  ],
                  opacity: [0, 0.8, 0.8, 0.8, 0],
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  delay: index * 0.3 + (index % 4) * 0.8,
                  ease: 'easeInOut',
                }}
              />
            ))}
          </svg>
          ))}
        </div>
      )}

      {/* 流星层 - 简化动画 (低性能设备时隐藏) */}
      {!performanceConfig.isLowPerformance && !performanceConfig.reducedMotion && (
        <div className="absolute inset-0" style={{ willChange: 'transform, opacity' }}>
          {meteors.map((meteor) => (
          <motion.div
            key={meteor.id}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${meteor.startX}%`,
              top: `${meteor.startY}%`,
              willChange: 'transform, opacity',
              transform: 'translate3d(0, 0, 0)',
            }}
            animate={{
              x: [`0%`, `${meteor.endX - meteor.startX}vw`],
              y: [`0%`, `${meteor.endY - meteor.startY}vh`],
              opacity: [0, 1, 1, 0],
            }}
            transition={{
              duration: meteor.duration,
              repeat: Infinity,
              delay: meteor.delay,
              ease: 'easeOut',
              repeatDelay: 20 + (parseInt(meteor.id.split('-')[1]) * 3),
            }}
          >
            {/* 简化流星尾迹 */}
            <motion.div
              className="absolute w-4 h-0.5 bg-gradient-to-r from-white to-transparent rounded-full"
              style={{
                right: '100%',
                top: '50%',
                transformOrigin: 'right center',
                willChange: 'transform, opacity',
              }}
              animate={{
                transform: [
                  'translateY(-50%) rotate(-30deg) translate3d(0, 0, 0) scaleX(0)',
                  'translateY(-50%) rotate(-30deg) translate3d(0, 0, 0) scaleX(1)',
                  'translateY(-50%) rotate(-30deg) translate3d(0, 0, 0) scaleX(0)',
                ],
                opacity: [0, 0.8, 0],
              }}
              transition={{
                duration: meteor.duration,
                repeat: Infinity,
                delay: meteor.delay,
                ease: 'easeOut',
                repeatDelay: 20 + (parseInt(meteor.id.split('-')[1]) * 3),
              }}
            />
          </motion.div>
          ))}
        </div>
      )}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-40 pb-16">
        <div className="flex items-center justify-center min-h-[80vh]">
          {/* 主要内容：3D Globe Card */}
          <GlobeCard />
        </div>
      </div>
    </section>
  );
}
