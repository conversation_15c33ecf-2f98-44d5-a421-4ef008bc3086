import React, { useState, useEffect } from 'react';
import { X, Menu } from 'lucide-react';

interface TocItem {
  id: string;
  text: string;
  level: number;
}

interface TableOfContentsProps {
  content: string;
}

const TableOfContents: React.FC<TableOfContentsProps> = ({ content }) => {
  const [tocItems, setTocItems] = useState<TocItem[]>([]);
  const [activeId, setActiveId] = useState<string>('');
  const [isVisible, setIsVisible] = useState(false);

  // 提取标题并生成目录
  useEffect(() => {
    // 等待DOM渲染完成
    const timer = setTimeout(() => {
      const articleElement = document.querySelector('article');
      if (!articleElement) return;

      const headings = articleElement.querySelectorAll('h1, h2, h3, h4, h5, h6');

      const items: TocItem[] = [];
      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        const text = heading.textContent || '';
        const id = `heading-${index}`;

        // 为标题添加ID（用于跳转）
        heading.id = id;

        items.push({
          id,
          text,
          level
        });
      });

      setTocItems(items);
    }, 100);

    return () => clearTimeout(timer);
  }, [content]);

  // 监听滚动，高亮当前章节
  useEffect(() => {
    const handleScroll = () => {
      const headings = document.querySelectorAll('h1[id], h2[id], h3[id], h4[id], h5[id], h6[id]');
      let currentId = '';
      
      headings.forEach((heading) => {
        const rect = heading.getBoundingClientRect();
        if (rect.top <= 100) {
          currentId = heading.id;
        }
      });
      
      setActiveId(currentId);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始调用
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [tocItems]);

  // 平滑滚动到指定标题
  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const headerHeight = 80; // 考虑固定头部的高度
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - headerHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  // 如果没有标题，不显示目录
  if (tocItems.length === 0) {
    return null;
  }

  return (
    <>
      {/* 移动端目录按钮 */}
      <div className="lg:hidden fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsVisible(!isVisible)}
          className="btn btn-primary btn-circle shadow-lg"
          title="文章目录"
        >
          <Menu className="h-5 w-5" />
        </button>
      </div>

      {/* 移动端目录弹窗 */}
      {isVisible && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black/50" onClick={() => setIsVisible(false)}>
          <div className="fixed bottom-0 left-0 right-0 bg-base-100 rounded-t-2xl p-6 max-h-[70vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-base-content">文章目录</h3>
              <button
                onClick={() => setIsVisible(false)}
                className="btn btn-ghost btn-sm btn-circle"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <nav className="space-y-2">
              {tocItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    scrollToHeading(item.id);
                    setIsVisible(false);
                  }}
                  className={`block w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                    activeId === item.id
                      ? 'bg-primary text-primary-content'
                      : 'text-base-content/70 hover:bg-base-200 hover:text-base-content'
                  }`}
                  style={{ paddingLeft: `${(item.level - 1) * 16 + 12}px` }}
                >
                  {item.text}
                </button>
              ))}
            </nav>
          </div>
        </div>
      )}

      {/* 桌面端侧边目录 */}
      <div className="hidden xl:block">
        <div className="bg-base-100 rounded-xl shadow-xl border border-base-300/50 p-5 w-full max-h-[65vh] overflow-y-auto backdrop-blur-sm">
          <h3 className="text-sm font-bold text-base-content mb-4 flex items-center gap-2 border-b border-base-300/30 pb-2">
            <Menu className="h-4 w-4 text-primary" />
            文章目录
          </h3>
          <nav className="space-y-1">
            {tocItems.map((item) => (
              <button
                key={item.id}
                onClick={() => scrollToHeading(item.id)}
                className={`block w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                  activeId === item.id
                    ? 'bg-primary text-primary-content shadow-sm'
                    : 'text-base-content/70 hover:bg-base-200 hover:text-base-content hover:shadow-sm'
                }`}
                style={{ paddingLeft: `${(item.level - 1) * 16 + 12}px` }}
                title={item.text}
              >
                <span className="line-clamp-2 leading-relaxed">{item.text}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>
    </>
  );
};

export default TableOfContents;
