---
// 最新安全博客组件

// 从API获取最新博客数据
const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

let blogPosts = [];
let error = null;

try {
  const response = await fetch(`${API_BASE_URL}/blog/posts/?page=1&page_size=6&ordering=-created_at`);
  if (response.ok) {
    const result = await response.json();
    if (result.success && result.data) {
      blogPosts = result.data;
    }
  }
} catch (err: any) {
  console.error('获取博客数据失败:', err);
  error = err?.message || '获取数据失败';
}

// 分类徽章样式映射
const getCategoryBadgeClass = (category: string) => {
  switch (category) {
    case '官网WP': return 'badge badge-primary'
    case '病毒分析': return 'badge badge-error'
    case '文章转载': return 'badge badge-info'
    case '应急响应工具教程': return 'badge badge-success'
    case '紧急预警': return 'badge badge-warning'
    case '漏洞与预防': return 'badge badge-info'
    case '攻击手法分析': return 'badge badge-error'
    case '病毒家族历史': return 'badge badge-secondary'
    case '成功案例': return 'badge badge-success'
    default: return 'badge badge-outline'
  }
}

// 格式化日期函数
const formatDisplayDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return dateString;
  }
}
---

<section class="py-16 bg-base-100">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center mb-12">
      <div>
        <h2 class="text-3xl font-bold text-base-content mb-4">最新安全博客</h2>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-info rounded-full animate-pulse"></div>
          <p class="text-lg text-base-content/70">专业的网络安全技术分析与研究</p>
        </div>
      </div>
      <a 
        href="/blog" 
        class="btn btn-outline btn-primary hover:btn-primary transition-all duration-200"
      >
        查看全部
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </a>
    </div>

    <!-- 错误状态 -->
    {error && (
      <div class="text-center py-12">
        <div class="alert alert-warning max-w-md mx-auto">
          <svg class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          <div>
            <div class="font-bold">加载失败</div>
            <div class="text-xs">{error}</div>
          </div>
        </div>
      </div>
    )}

    <!-- 博客文章网格 -->
    {!error && (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {blogPosts.length === 0 ? (
          <div class="col-span-full text-center py-12">
            <div class="text-base-content/60">
              <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <p class="text-lg">暂无博客文章</p>
            </div>
          </div>
        ) : (
          blogPosts.map((post: any) => (
            <div class="card card-border shadow-none hover:shadow-lg transition-all duration-300 border border-gray-50/10 hover:border-gray-100/30">
              <figure>
                <a href={`/blog/${post.id}`} class="block">
                  <img
                    src={post.cover_image || 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'}
                    alt={post.title}
                    class="w-full h-48 object-cover hover:scale-105 transition-transform duration-300 cursor-pointer"
                    loading="lazy"
                  />
                </a>
              </figure>
              <div class="card-body gap-3">
                <!-- 文章分类和发布时间 -->
                <div class="flex items-center justify-between mb-2">
                  <span class={getCategoryBadgeClass(post.category)}>{post.category}</span>
                  <div class="flex items-center gap-1 text-xs text-base-content/60">
                    <svg class="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12,6 12,12 16,14"></polyline>
                    </svg>
                    {formatDisplayDate(post.created_at)}
                  </div>
                </div>

                <!-- 文章标题 -->
                <h5 class="card-title text-xl leading-tight">
                  <a
                    href={`/blog/${post.id}`}
                    class="hover:text-primary transition-colors duration-200 cursor-pointer"
                  >
                    {post.title}
                  </a>
                </h5>

                <!-- 文章摘要 -->
                <p class="mb-5 text-base-content/70 line-clamp-3">
                  {post.excerpt || '暂无摘要...'}
                </p>

                <!-- 文章标签 -->
                <div class="flex flex-wrap gap-2 mb-4">
                  {post.tags && post.tags.slice(0, 3).map((tag: string) => (
                    <span class="badge badge-outline badge-sm">{tag}</span>
                  ))}
                </div>

                <!-- 阅读按钮 -->
                <div class="card-actions">
                  <a
                    href={`/blog/${post.id}`}
                    class="btn btn-primary btn-gradient btn-sm"
                  >
                    阅读全文
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    )}
  </div>
</section>
