<template>
  <section class="py-16 bg-base-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center mb-12">
        <div>
          <h2 class="text-3xl font-bold text-base-content mb-4">最新威胁情报</h2>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-error rounded-full animate-pulse"></div>
            <p class="text-lg text-base-content/70">实时更新的威胁情报和安全事件</p>
          </div>
        </div>

      </div>



      <!-- 默认内容 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 最新威胁情报 -->
        <div>
          <h3 class="text-xl font-semibold text-base-content mb-8">威胁情报</h3>
          <div class="space-y-6">
            <a
              v-for="threat in latestThreats"
              :key="threat.id"
              :href="`/intell/${threat.id}`"
              class="card group cursor-pointer transition-all duration-200 hover:shadow-lg border border-gray-50/10 hover:border-gray-100/30 block"
            >
              <div class="flex items-start p-4">
                <!-- 左侧封面图片 -->
                <div class="w-32 h-32 flex-shrink-0 overflow-hidden rounded-lg">
                  <img
                    :src="threat.coverImage"
                    :alt="threat.title"
                    class="w-full h-full object-cover rounded-lg"
                    loading="lazy"
                  />
                </div>

                <!-- 右侧内容 -->
                <div class="flex-1 pl-4">
                  <div class="flex items-start justify-between h-full">
                    <div class="flex-1 space-y-3">
                      <!-- 头部信息 -->
                      <div class="flex items-center gap-2">
                        <span :class="getSeverityBadgeClass(threat.severity)">
                          {{ threat.severity }}
                        </span>
                        <span class="text-sm text-base-content/70">{{ threat.source }}</span>
                      </div>

                      <!-- 标题 -->
                      <h4 class="font-semibold text-base-content leading-tight">
                        {{ threat.title }}
                      </h4>

                      <!-- 描述 -->
                      <p class="text-sm text-base-content/70 line-clamp-2">
                        {{ threat.description }}
                      </p>

                      <!-- 时间信息 -->
                      <div class="flex items-center gap-1 text-xs text-base-content/60">
                        <Clock class="h-3 w-3" />
                        {{ threat.publishTime }}
                      </div>
                    </div>

                    <!-- 箭头图标 -->
                    <ChevronRight class="h-4 w-4 text-base-content/60 group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
                  </div>
                </div>
              </div>
            </a>
          </div>

          <!-- 查看更多链接 -->
          <div class="text-center mt-6">
            <a href="/intell?category=1" class="text-primary hover:text-primary/80 text-sm font-medium inline-flex items-center gap-1 transition-colors duration-200">
              查看更多威胁情报
              <ArrowRight class="h-4 w-4" />
            </a>
          </div>
        </div>

        <!-- 最新安全事件 -->
        <div>
          <h3 class="text-xl font-semibold text-base-content mb-8">安全事件</h3>
          <div class="space-y-6">
            <a
              v-for="event in latestEvents"
              :key="event.id"
              :href="`/intell/${event.id}`"
              class="card group cursor-pointer transition-all duration-200 hover:shadow-lg border border-gray-50/10 hover:border-gray-100/30 block"
            >
              <div class="flex items-start p-4">
                <!-- 左侧封面图片 -->
                <div class="w-32 h-32 flex-shrink-0 overflow-hidden rounded-lg">
                  <img
                    :src="event.coverImage"
                    :alt="event.title"
                    class="w-full h-full object-cover rounded-lg"
                    loading="lazy"
                  />
                </div>

                <!-- 右侧内容 -->
                <div class="flex-1 pl-4">
                  <div class="flex items-start justify-between h-full">
                    <div class="flex-1 space-y-3">
                      <!-- 头部信息 -->
                      <div class="flex items-center gap-2">
                        <span :class="getStatusBadgeClass(event.status)">
                          {{ event.status }}
                        </span>
                        <span class="text-sm text-base-content/70">{{ event.targetIndustry }}</span>
                      </div>

                      <!-- 标题 -->
                      <h4 class="font-semibold text-base-content leading-tight">
                        {{ event.title }}
                      </h4>

                      <!-- 描述 -->
                      <p class="text-sm text-base-content/70 line-clamp-2">
                        {{ event.description }}
                      </p>

                      <!-- 底部信息 -->
                      <div class="flex items-center justify-between text-xs text-base-content/60">
                        <div class="flex items-center gap-1">
                          <Clock class="h-3 w-3" />
                          {{ event.incidentTime }}
                        </div>
                        <div class="flex items-center gap-1">
                          <MapPin class="h-3 w-3" />
                          {{ event.targetRegion }}
                        </div>
                      </div>
                    </div>

                    <!-- 箭头图标 -->
                    <ChevronRight class="h-4 w-4 text-base-content/60 group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
                  </div>
                </div>
              </div>
            </a>
          </div>

          <!-- 查看更多链接 -->
          <div class="text-center mt-6">
            <a href="/intell?category=2" class="text-primary hover:text-primary/80 text-sm font-medium inline-flex items-center gap-1 transition-colors duration-200">
              查看更多安全事件
              <ArrowRight class="h-4 w-4" />
            </a>
          </div>
        </div>

        <!-- 活跃勒索组织 -->
        <div class="lg:col-span-2 mt-8">
          <div class="flex items-center justify-between mb-8">
            <div>
              <h3 class="text-2xl font-bold text-base-content mb-2">活跃勒索组织</h3>
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                <p class="text-sm text-base-content/70">实时监控的高危勒索组织活动状态</p>
              </div>
            </div>
            <a href="/ransomware" class="btn btn-outline btn-sm">
              查看全部
              <ArrowRight class="ml-2 h-4 w-4" />
            </a>
          </div>

          <!-- 加载状态 -->
          <div v-if="loadingGroups" class="flex justify-center py-12">
            <span class="loading loading-spinner loading-lg text-primary"></span>
          </div>

          <!-- 勒索组织列表 -->
          <div v-else-if="activeGroups.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <RansomwareGroupCard
              v-for="group in activeGroups"
              :key="group.id"
              :group="group"
              @click="handleGroupClick"
            />
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-12">
            <div class="text-base-content/50 mb-6">
              <h4 class="text-lg font-semibold mb-2">暂无活跃勒索组织数据</h4>
              <p class="text-base-content/70">
                当前没有检测到活跃的勒索组织，请稍后再试
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import RansomwareGroupCard from '@/components/ui/RansomwareGroupCard.vue'
import {
  ArrowRight,
  Clock,
  ChevronRight,
  MapPin
} from 'lucide-vue-next'
import type { RansomwareGroup, ThreatIntelligence } from '@/types/api'
import { ransomwareGroupApi, threatIntelligenceApi } from '@/lib/api'
import { useToast } from '@/composables/useToast'

// Toast 实例
const { showError } = useToast()

// 响应式数据
const latestThreats = ref<(ThreatIntelligence & { coverImage?: string; publishTime?: string })[]>([])
const latestEvents = ref<(ThreatIntelligence & { coverImage?: string; incidentTime?: string; targetIndustry?: string; targetRegion?: string; status?: string })[]>([])
const loadingThreats = ref(false)
const loadingEvents = ref(false)

// 活跃勒索组织数据
const activeGroups = ref<RansomwareGroup[]>([])
const loadingGroups = ref(false)

// 时间格式化函数
const formatTimeAgo = (dateString: string) => {
  const now = new Date()
  const date = new Date(dateString)
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

  if (diffInMinutes < 60) {
    return `${diffInMinutes}分钟前`
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}小时前`
  } else {
    return `${Math.floor(diffInMinutes / 1440)}天前`
  }
}

// 加载最新威胁情报
const loadLatestThreats = async () => {
  try {
    loadingThreats.value = true

    // 获取分类1的威胁情报（威胁情报）
    const response = await threatIntelligenceApi.getList({
      page: 1,
      pageSize: 3,
      category: '1'
    })

    if (response && (response as any).success && (response as any).data) {
      const data = (response as any).data
      const threatList = Array.isArray(data) ? data : []

      // 为威胁情报添加封面图片和时间格式化
      latestThreats.value = threatList.map((threat: any, index: number) => ({
        ...threat,
        publishTime: formatTimeAgo(threat.created_at),
        coverImage: threat.cover_image || [
          'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=250&fit=crop&auto=format'
        ][index % 3]
      }))
    }
  } catch (error: any) {
    console.error('加载最新威胁情报失败:', error)
    latestThreats.value = []
  } finally {
    loadingThreats.value = false
  }
}

// 加载最新安全事件
const loadLatestEvents = async () => {
  try {
    loadingEvents.value = true

    // 获取分类2的威胁情报（安全事件）
    const response = await threatIntelligenceApi.getList({
      page: 1,
      pageSize: 3,
      category: '2'
    })

    if (response && (response as any).success && (response as any).data) {
      const data = (response as any).data
      const eventList = Array.isArray(data) ? data : []

      // 为安全事件添加封面图片和时间格式化
      latestEvents.value = eventList.map((event: any, index: number) => ({
        ...event,
        incidentTime: formatTimeAgo(event.created_at),
        status: event.status || '已确认',
        targetIndustry: event.target_industry || '未知',
        targetRegion: event.target_region || '全球',
        coverImage: event.cover_image || [
          'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop&auto=format'
        ][index % 3]
      }))
    }
  } catch (error: any) {
    console.error('加载最新安全事件失败:', error)
    latestEvents.value = []
  } finally {
    loadingEvents.value = false
  }
}

// 获取威胁等级权重（用于排序）
const getThreatLevelWeight = (level: string) => {
  switch (level) {
    case 'critical': return 4
    case 'high': return 3
    case 'medium': return 2
    case 'low': return 1
    default: return 0
  }
}

// 加载活跃勒索组织数据
const loadActiveGroups = async () => {
  try {
    loadingGroups.value = true

    // 调用API获取勒索组织数据
    const response = await ransomwareGroupApi.getList({
      page: 1,
      pageSize: 50 // 获取足够多的数据用于筛选
    })

    if (response.list) {
      // 筛选活跃组织并排序
      const filteredGroups = response.list
        .filter(group => group.status === 'active') // 只显示活跃组织
        .sort((a, b) => {
          // 首先按威胁等级排序
          const threatDiff = getThreatLevelWeight(b.threat_level) - getThreatLevelWeight(a.threat_level)
          if (threatDiff !== 0) return threatDiff

          // 威胁等级相同时，按最近活动时间排序
          const bTime = b.last_activity ? new Date(b.last_activity).getTime() : 0
          const aTime = a.last_activity ? new Date(a.last_activity).getTime() : 0
          return bTime - aTime
        })
        .slice(0, 6) // 只显示前6个最活跃的组织

      activeGroups.value = filteredGroups
    } else {
      console.warn('API返回的数据格式不正确:', response)
      activeGroups.value = []
    }

  } catch (error: any) {
    showError(error?.message || '加载活跃勒索组织数据失败，请稍后再试')
    console.error('加载活跃勒索组织失败:', error)
    activeGroups.value = []
  } finally {
    loadingGroups.value = false
  }
}

// 处理勒索组织点击（保留用于RansomwareGroupCard组件）
const handleGroupClick = (group: RansomwareGroup) => {
  console.log('点击勒索组织:', group)
  // 跳转到勒索组织详情页面
  if (typeof window !== 'undefined') {
    window.location.href = `/ransomware/${group.slug}`
  }
}



// FlyonUI Badge 类辅助函数
const getSeverityBadgeClass = (severity: string) => {
  switch (severity) {
    case '严重': return 'badge badge-error'
    case '高': return 'badge badge-warning'
    case '中': return 'badge badge-info'
    case '低': return 'badge badge-success'
    default: return 'badge badge-secondary'
  }
}

const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case '进行中': return 'badge badge-error'
    case '已确认': return 'badge badge-warning'
    case '已解决': return 'badge badge-success'
    default: return 'badge badge-secondary'
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadLatestThreats()
  loadLatestEvents()
  loadActiveGroups()
})


</script>

<style scoped>
/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}
</style>
