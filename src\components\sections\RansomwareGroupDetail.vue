<template>
  <div v-if="loading" class="flex justify-center py-16">
    <span class="loading loading-spinner loading-lg text-primary"></span>
  </div>

  <div v-else-if="group" class="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
    <!-- 面包屑导航 -->
    <Breadcrumb :items="breadcrumbItems" />

    <!-- 组织头部信息 -->
    <div class="card bg-base-100 border border-gray-200/20 mt-6 sm:mt-8">
      <div class="card-body p-4 sm:p-6 lg:p-8">
        <div class="flex flex-col lg:flex-row gap-4 sm:gap-6 lg:gap-8">
          <!-- 左侧图片 -->
          <div class="w-full sm:w-full lg:w-80 h-32 sm:h-40 lg:h-48 flex-shrink-0 overflow-hidden rounded-lg">
            <img :src="getGroupImage(group)" :alt="group.name"
              class="w-full h-full object-cover" loading="lazy" />
          </div>

          <!-- 右侧信息 -->
          <div class="flex-1">
            <!-- 标题和标签 -->
            <div class="mb-4 sm:mb-6">
              <div class="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4 flex-wrap">
                <span :class="getThreatLevelBadgeClass(group.threat_level)">
                  {{ getThreatLevelBadgeText(group.threat_level) }}
                </span>
                <span :class="getStatusBadgeClass(group.status)">
                  {{ getStatusBadgeText(group.status) }}
                </span>
              </div>

              <h1 class="text-2xl sm:text-3xl font-bold text-base-content mb-2">{{ group.name }}</h1>

              <div v-if="group.aliases && group.aliases.length > 0" class="flex flex-wrap gap-2 mb-3 sm:mb-4">
                <span class="text-sm text-base-content/60">别名:</span>
                <span v-for="alias in group.aliases" :key="alias" class="badge badge-ghost badge-sm">
                  {{ alias }}
                </span>
              </div>
            </div>

            <!-- 基本信息网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 sm:gap-6">
              <!-- 受害者数量 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Users class="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">受害者数量</div>
                  <div class="font-bold text-sm text-base-content">
                    {{ group.victim_count || 0 }}</div>
                </div>
              </div>

              <!-- 首次发现 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Calendar class="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">首次发现</div>
                  <div class="font-medium text-sm text-base-content">{{ group.first_seen ? formatDateChinese(group.first_seen) : '未知' }}</div>
                </div>
              </div>

              <!-- 最后活动 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Activity class="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">最后活动</div>
                  <div class="font-medium text-sm text-base-content">{{ group.last_activity ? formatDateChinese(group.last_activity) : '未知' }}</div>
                </div>
              </div>

              <!-- 平均延迟 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Clock class="h-5 w-5 text-yellow-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">平均延迟</div>
                  <div class="font-medium text-sm text-base-content">
                    {{ group.avg_delay_days || 0 }} 天
                  </div>
                </div>
              </div>

              <!-- 信息窃取比例 -->
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg flex items-center justify-center">
                  <Shield class="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <div class="text-xs sm:text-sm text-base-content/60">信息窃取比例</div>
                  <div class="font-medium text-sm text-base-content">
                    {{ group.info_stealer_percentage || '未知' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细信息标签页 -->
    <div class="card bg-base-100 border border-gray-200/20 mt-6 sm:mt-8">
      <div class="card-body p-0">
        <!-- 标签页导航 -->
        <nav class="tabs tabs-bordered overflow-x-auto px-3 sm:px-4 md:px-6 lg:px-8 pt-4 sm:pt-6" aria-label="Tabs"
          role="tablist" aria-orientation="horizontal">
          <button v-for="tab in tabs" :key="tab.id" type="button" :class="[
            'tab active-tab:tab-active whitespace-nowrap min-h-[44px]',
            activeTab === tab.id ? 'active tab-active' : ''
          ]" @click="switchTab(tab.id)" :aria-selected="activeTab === tab.id" role="tab">
            <component :is="tab.icon" class="h-4 w-4 sm:h-5 sm:w-5 shrink-0 me-2" />
            <span class="text-xs sm:text-sm">{{ tab.label }}</span>
          </button>
        </nav>

        <!-- 标签页内容 -->
        <div class="p-3 sm:p-4 md:p-6 lg:p-8">
          <!-- 概述 -->
          <RansomwareBasicInfo v-if="activeTab === 'overview'" :group="group" />

          <!-- 攻击手段 -->
          <RansomwareAttackMethods v-if="activeTab === 'methods'" :group="group" />

          <!-- 已知位置 -->
          <RansomwareKnownLocations v-if="activeTab === 'locations'" :group="group" />

          <!-- IOC指标 -->
          <div v-if="activeTab === 'technical'">
            <div v-if="tabData.technical.loading" class="flex justify-center py-8">
              <span class="loading loading-spinner loading-lg text-primary"></span>
            </div>
            <RansomwareIOCIndicators v-else-if="tabData.technical.loaded"
              :iocIndicators="tabData.technical.data?.ioc_indicators || []" />
            <div v-else class="flex justify-center py-8">
              <span class="text-base-content/60">点击加载IOC指标</span>
            </div>
          </div>

          <!-- 受害者 -->
          <RansomwareVictims v-if="activeTab === 'victims'"
            :groupSlug="props.slug" />

          <!-- 谈判记录 -->
          <NegotiationDetail v-if="activeTab === 'negotiations'"
            :groupSlug="props.slug" />

          <!-- 勒索信 -->
          <RansomwareRansomNotes v-if="activeTab === 'ransom-notes'"
            :groupSlug="props.slug" />

          <!-- 应急工具 -->
          <RansomwareEmergencyTools v-if="activeTab === 'emergency-tools'"
            :groupSlug="props.slug" />
        </div>
      </div>
    </div>

    <!-- 上一篇/下一篇导航 -->
    <RansomwareNavigation :previous-group="previousGroup" :next-group="nextGroup" />
  </div>

  <!-- 错误状态 -->
  <div v-else class="text-center py-16">
    <div class="text-base-content/50 mb-6">
      <AlertCircle class="h-16 w-16 mx-auto mb-4 opacity-50" />
      <h3 class="text-xl font-semibold mb-2">未找到该勒索软件组织</h3>
      <p class="text-base-content/70">
        请检查URL是否正确，或返回列表页面
      </p>
    </div>
    <a href="/ransomware" class="btn btn-primary">
      返回列表
    </a>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import {
  Calendar,
  Activity,
  AlertCircle,
  FileText,
  Zap,
  Settings,
  Clock,
  MessageSquare,
  Globe,
  Users,
  Shield,
  Wrench
} from 'lucide-vue-next'
import Breadcrumb from '@/components/ui/Breadcrumb.vue'
import RansomwareNavigation from '@/components/ui/RansomwareNavigation.vue'
import NegotiationDetail from '@/components/ui/NegotiationDetail.vue'
import RansomwareBasicInfo from '@/components/RansomwareGroupDetail/RansomwareBasicInfo.vue'
import RansomwareAttackMethods from '@/components/RansomwareGroupDetail/RansomwareAttackMethods.vue'
import RansomwareKnownLocations from '@/components/RansomwareGroupDetail/RansomwareKnownLocations.vue'
import RansomwareIOCIndicators from '@/components/RansomwareGroupDetail/RansomwareIOCIndicators.vue'
import RansomwareVictims from '@/components/RansomwareGroupDetail/RansomwareVictims.vue'

import RansomwareRansomNotes from '@/components/RansomwareGroupDetail/RansomwareRansomNotes.vue'
import RansomwareEmergencyTools from '@/components/RansomwareGroupDetail/RansomwareEmergencyTools.vue'
import type { RansomwareGroup } from '@/types/api'
import { ransomwareGroupApi } from '@/lib/api'
import { useToast } from '@/composables/useToast'

// 设置 dayjs 中文语言
dayjs.locale('zh-cn')

// Toast 实例
const { showError } = useToast()

interface Props {
  slug: string
}

const props = defineProps<Props>()

// 获取URL参数中的标签页
const getTabFromUrl = () => {
  if (typeof window === 'undefined') return 'overview'
  const urlParams = new URLSearchParams(window.location.search)
  const tab = urlParams.get('tab')
  // 验证标签页是否有效
  const validTabs = ['overview', 'methods', 'locations', 'technical', 'victims', 'negotiations', 'ransom-notes', 'emergency-tools']
  return validTabs.includes(tab || '') ? tab : 'overview'
}

// 更新URL中的标签页参数
const updateUrlTab = (tab: string) => {
  if (typeof window === 'undefined') return

  const url = new URL(window.location.href)
  if (tab === 'overview') {
    // 如果是默认标签页，移除tab参数
    url.searchParams.delete('tab')
  } else {
    url.searchParams.set('tab', tab)
  }

  // 更新URL但不刷新页面
  window.history.pushState({}, '', url.toString())
}

// 响应式数据
const loading = ref(true)
const group = ref<RansomwareGroup | null>(null)
const previousGroup = ref<any>(null)
const nextGroup = ref<any>(null)
const activeTab = ref(getTabFromUrl())

// 分离的数据状态
const tabData = ref<{
  [key: string]: {
    data: any;
    loading: boolean;
    loaded: boolean;
  }
}>({
  overview: { data: null, loading: false, loaded: false },
  methods: { data: null, loading: false, loaded: false },
  locations: { data: null, loading: false, loaded: false },
  technical: { data: null, loading: false, loaded: false },
  victims: { data: null, loading: false, loaded: false },
  negotiations: { data: null, loading: false, loaded: false },
  'ransom-notes': { data: null, loading: false, loaded: false },
  'emergency-tools': { data: null, loading: false, loaded: false }
})

// 面包屑数据
const breadcrumbItems = computed(() => [
  {
    label: '首页',
    href: '/'
  },
  {
    label: '勒索软件组织',
    href: '/ransomware'
  },
  {
    label: group.value?.name || '加载中...',
    current: true
  }
])

// 标签页配置
const tabs = [
  { id: 'overview', label: '概述', icon: FileText },
  { id: 'methods', label: '攻击手段', icon: Zap },
  { id: 'locations', label: '已知站点', icon: Globe },
  { id: 'technical', label: 'IOC指标', icon: Settings },
  { id: 'victims', label: '受害者', icon: Users },
  { id: 'negotiations', label: '谈判记录', icon: MessageSquare },
  { id: 'ransom-notes', label: '勒索信', icon: FileText },
  { id: 'emergency-tools', label: '应急工具', icon: Wrench }
]

// 获取威胁等级徽章样式
const getThreatLevelBadgeClass = (level: string) => {
  switch (level) {
    case 'critical': return 'badge badge-error'
    case 'high': return 'badge badge-warning'
    case 'medium': return 'badge badge-info'
    case 'low': return 'badge badge-success'
    default: return 'badge badge-secondary'
  }
}

// 获取威胁等级徽章文本
const getThreatLevelBadgeText = (level: string) => {
  switch (level) {
    case 'critical': return '极高'
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

// 获取状态徽章样式
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'active': return 'badge badge-error badge-outline'
    case 'unknown': return 'badge badge-warning badge-outline'
    case 'inactive': return 'badge badge-info badge-outline'
    case 'disbanded': return 'badge badge-success badge-outline'
    default: return 'badge badge-secondary badge-outline'
  }
}

// 获取状态徽章文本
const getStatusBadgeText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'unknown': return '未知'
    case 'inactive': return '不活跃'
    case 'disbanded': return '已解散'
    default: return '状态未知'
  }
}

// 获取组织图片（优先显示logo，最后默认图片）
const getGroupImage = (group: RansomwareGroup) => {
  // 优先使用logo
  if (group.logo) {
    return group.logo
  }
  // 使用基于威胁等级的默认图片
  return getDefaultImage(group.threat_level)
}

// 获取默认图片
const getDefaultImage = (threatLevel: string) => {
  const imageMap: Record<string, string> = {
    'critical': 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=800&h=400&fit=crop&auto=format',
    'high': 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=800&h=400&fit=crop&auto=format',
    'medium': 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=400&fit=crop&auto=format',
    'low': 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&h=400&fit=crop&auto=format'
  }
  return imageMap[threatLevel] || 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=400&fit=crop&auto=format'
}

// 格式化中文日期
const formatDateChinese = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD')
}

// 按需加载tab数据
const loadTabData = async (tabId: string) => {
  if (!group.value || tabData.value[tabId].loaded || tabData.value[tabId].loading) {
    return
  }

  try {
    tabData.value[tabId].loading = true

    switch (tabId) {
      case 'technical':
        // IOC指标数据暂时保持原有逻辑，因为还没有实现分页
        const iocData = await ransomwareGroupApi.getIOCIndicators(props.slug)
        tabData.value[tabId].data = iocData
        break
      case 'emergency-tools':
      case 'negotiations':
      case 'ransom-notes':
      case 'victims':
        // 这些组件现在都是自己管理数据，不需要在这里加载
        tabData.value[tabId].data = {}
        break
      default:
        // 对于基础信息类的tab（overview, methods, locations），数据已经在基础信息中
        tabData.value[tabId].data = group.value
        break
    }

    tabData.value[tabId].loaded = true
  } catch (error: any) {
    showError(error || `加载${tabId}数据失败`)
    console.warn(`加载${tabId}数据失败:`, error)
  } finally {
    tabData.value[tabId].loading = false
  }
}

// 切换标签页
const switchTab = async (tabId: string) => {
  activeTab.value = tabId
  updateUrlTab(tabId)

  // 按需加载数据
  await loadTabData(tabId)
}

// 加载组织基础信息
const loadGroupDetail = async () => {
  try {
    loading.value = true

    // 调用API获取勒索软件组织基础信息
    const response: any = await ransomwareGroupApi.getBasicInfo(props.slug)
    if (response) {
      const { prev_id, next_id } = response
      document.title = `${response.name} - 勒索软件组织详情 - 威胁情报中心`
      group.value = response

      // 加载上一篇组织数据
      if (prev_id) {
        previousGroup.value = {
          name: prev_id.name || '上一个组织',
          slug: prev_id.slug,
          threat_level: prev_id.threat_level,
          status: prev_id.status
        }
      } else {
        previousGroup.value = null // 如果没有上一篇，设置为null
      }

      // 加载下一篇组织数据
      if (next_id) {
        nextGroup.value = {
          name: next_id.name || '下一个组织',
          slug: next_id.slug,
          threat_level: next_id.threat_level,
          status: next_id.status
        }
      } else {
        nextGroup.value = null // 如果没有下一篇，设置为null
      }

      // 标记基础信息相关的tab为已加载
      tabData.value.overview.data = response
      tabData.value.overview.loaded = true
      tabData.value.methods.data = response
      tabData.value.methods.loaded = true
      tabData.value.locations.data = response
      tabData.value.locations.loaded = true

    }
  } catch (error: any) {
    console.warn('API调用失败:', error)

    // 检查是否是401错误（需要登录）
    if (error?.code === 401 || error?.status === 401) {
      // 重定向到登录页面，并保存当前页面URL用于登录后返回
      const currentPath = window.location.pathname
      window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`
      return
    }

    showError(error || '加载数据失败，请稍后再试')
    group.value = null
  } finally {
    loading.value = false
  }
}

// 监听浏览器前进后退
if (typeof window !== 'undefined') {
  window.addEventListener('popstate', () => {
    activeTab.value = getTabFromUrl()
  })
}

// 组件挂载时加载数据
onMounted(async () => {
  // 确保初始化时从URL获取正确的标签页
  activeTab.value = getTabFromUrl()
  await loadGroupDetail()

  // 加载当前激活tab的数据
  if (activeTab.value) {
    await loadTabData(activeTab.value)
  }
})


</script>
