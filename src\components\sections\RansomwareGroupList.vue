<template>
  <section class="py-16 bg-base-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 内容区域 -->
      <div>
        <!-- 工具栏 -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h2 class="text-2xl font-bold text-base-content mb-2">勒索软件组织</h2>
            <p class="text-base-content/70">
              共找到 {{ totalItems }} 个勒索软件组织
            </p>
          </div>

          <!-- 排序选项 -->
          <div class="flex items-center gap-4 mt-4 sm:mt-0">
            <select v-model="sortBy" class="select select-bordered select-sm" @change="handleSort">
              <option value="threat_level_desc">威胁等级（高到低）</option>
              <option value="threat_level_asc">威胁等级（低到高）</option>
              <option value="last_activity_desc">最近活动</option>
              <option value="last_activity_asc">最早活动</option>
              <option value="attacks_desc">攻击次数（多到少）</option>
              <option value="attacks_asc">攻击次数（少到多）</option>
              <option value="name_asc">名称（A-Z）</option>
              <option value="name_desc">名称（Z-A）</option>
            </select>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="flex justify-center py-12">
          <span class="loading loading-spinner loading-lg text-primary"></span>
        </div>

        <!-- 勒索软件组织列表 -->
        <div v-else-if="paginatedGroups.length > 0">
          <div class="grid gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
            <RansomwareGroupCard v-for="group in paginatedGroups" :key="group.id" :group="group"
              @click="handleGroupClick(group)" />
          </div>

          <!-- 分页 -->
          <Pagination :current-page="currentPage" :total-pages="totalPages" :total-items="totalItems" :show-info="true"
            :use-events="true" @page-change="handlePageChange" />
        </div>

        <!-- 空状态 -->
        <div v-else class="text-center py-16">
          <div class="text-base-content/50 mb-6">
            <Search class="h-16 w-16 mx-auto mb-4 opacity-50" />
            <h3 class="text-xl font-semibold mb-2">未找到匹配的勒索软件组织</h3>
            <p class="text-base-content/70">
              暂无勒索软件组织数据
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Search
} from 'lucide-vue-next'

import RansomwareGroupCard from '@/components/ui/RansomwareGroupCard.vue'
import Pagination from '@/components/ui/Pagination.vue'
import type { RansomwareGroup } from '@/types/api'
import { ransomwareGroupApi } from '@/lib/api'
import { useToast } from '@/composables/useToast'

// Toast 实例
const { showError } = useToast()

// 获取URL参数
const getUrlParams = () => {
  if (typeof window === 'undefined') {
    return {
      page: 1,
      pageSize: 6,
      sortBy: 'threat_level_desc'
    }
  }

  const urlParams = new URLSearchParams(window.location.search)
  return {
    page: parseInt(urlParams.get('page') || '1'),
    pageSize: parseInt(urlParams.get('page_size') || '6'),
    sortBy: urlParams.get('sortBy') || 'threat_level_desc'
  }
}

// 更新URL参数
const updateUrl = () => {
  if (typeof window === 'undefined') return

  const params = new URLSearchParams()

  if (currentPage.value > 1) params.set('page', currentPage.value.toString())
  if (pageSize.value !== 6) params.set('page_size', pageSize.value.toString())
  if (sortBy.value !== 'threat_level_desc') params.set('sortBy', sortBy.value)

  const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`
  window.history.pushState({}, '', newUrl)
}

// 初始化URL参数
const urlParams = getUrlParams()

// 响应式数据
const loading = ref(true)
const groups = ref<RansomwareGroup[]>([])
const currentPage = ref(urlParams.page)
const pageSize = ref(urlParams.pageSize)
const sortBy = ref(urlParams.sortBy)
const totalItems = ref(0)
const serverTotalPages = ref(0)

// 分页相关计算属性
const totalPages = computed(() => serverTotalPages.value)
const paginatedGroups = computed(() => groups.value)

// 事件处理函数
const handleSort = () => {
  currentPage.value = 1
  updateUrl()
  loadGroups()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  updateUrl()
  // 重新加载数据以获取新页面的内容
  loadGroups()
}

const handleGroupClick = (group: RansomwareGroup) => {
  if (typeof window !== 'undefined') {
    window.location.href = `/ransomware/${group.slug}`
  }
}

// 加载勒索软件组织数据
const loadGroups = async () => {
  try {
    loading.value = true

    // 调用API获取数据
    const response: any = await ransomwareGroupApi.getList({
      page: currentPage.value,
      page_size: pageSize.value
    })

    if (response.list) {
      groups.value = response.list

      // 更新分页信息（API已经转换了格式）
      if (response.total) {
        totalItems.value = response.total
        // 计算总页数
        serverTotalPages.value = Math.ceil(response.total / pageSize.value)
      }
    } else {
      console.warn('API返回的数据格式不正确:', response)
      groups.value = []
      totalItems.value = 0
      serverTotalPages.value = 0
    }

  } catch (error: any) {
    showError(error || '加载数据失败，请稍后再试')
    console.error('加载勒索软件组织失败:', error)
  } finally {
    loading.value = false
  }
}



// 初始化参数
const initializeParams = () => {
  const params = getUrlParams()
  currentPage.value = params.page
  pageSize.value = params.pageSize
  sortBy.value = params.sortBy
}

// 监听浏览器前进后退
if (typeof window !== 'undefined') {
  window.addEventListener('popstate', () => {
    initializeParams()
    loadGroups()
  })
}

// 组件挂载时加载数据
onMounted(() => {
  if (typeof window !== 'undefined') {
    initializeParams()
  }
  loadGroups()
})
</script>
