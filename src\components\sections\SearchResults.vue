<template>
  <div class="bg-base-100 py-16">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <!-- 搜索结果标题和筛选器 -->
      <div class="mb-8">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
          <div>
            <h2 class="text-2xl font-semibold text-base-content mb-2">
              搜索结果
            </h2>
            <p class="text-base-content/70" v-if="!loading && searchResults">
              找到 {{ searchResults.pagination.total_count }} 条关于 "<span class="text-primary font-medium">{{ searchQuery }}</span>" 的相关内容
            </p>
          </div>

          <!-- 内容类型筛选器 -->
          <div class="flex flex-wrap gap-2" v-if="!loading && searchResults">
            <button
              class="btn btn-sm"
              :class="selectedContentTypes.length === 0 ? 'btn-primary' : 'btn-outline'"
              @click="clearContentTypeFilter"
            >
              全部 ({{ searchResults.pagination.total_count }})
            </button>
            <button
              v-for="(count, type) in searchResults.content_types"
              :key="type"
              class="btn btn-sm"
              :class="selectedContentTypes.includes(type) ? 'btn-primary' : 'btn-outline'"
              @click="toggleContentType(type)"
            >
              {{ getContentTypeLabel(type) }} ({{ count }})
            </button>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="loading loading-spinner loading-lg text-primary"></div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-error mb-4">
          <AlertTriangle class="h-12 w-12 mx-auto mb-4" />
          <p class="text-lg font-medium">搜索出错了</p>
          <p class="text-sm text-base-content/70">{{ error }}</p>
        </div>
        <button class="btn btn-outline" @click="performSearch">
          重试
        </button>
      </div>

      <!-- 搜索结果内容 -->
      <div v-else-if="searchResults && searchResults.results.length > 0" class="space-y-4">
        <div
          v-for="item in searchResults.results"
          :key="`${item.content_type}-${item.object_id || item.id}`"
          class="card bg-base-100 shadow-md hover:shadow-xl border border-base-300/20 hover:border-primary/30 hover:-translate-y-1 transition-all duration-300 group"
        >
          <div class="card-body p-4 md:p-6">
            <div class="flex flex-col sm:flex-row items-start gap-2 sm:gap-4">
              <!-- 左侧图标/封面 -->
              <div class="w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0 overflow-hidden rounded-xl shadow-sm">
                <!-- 博客文章优先显示封面图片 -->
                <div v-if="item.content_type === 'blog_post' && item.data?.cover_image"
                     class="w-full h-full">
                  <img
                    :src="item.data.cover_image"
                    :alt="item.title"
                    class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    @error="handleImageError"
                  />
                  <!-- 图片加载失败时的备用图标 -->
                  <div class="w-full h-full bg-gradient-to-br from-base-200 to-base-300 flex items-center justify-center rounded-xl" style="display: none;">
                    <component
                      :is="getItemIcon(item.content_type)"
                      class="h-8 w-8 text-base-content/60 group-hover:text-primary transition-colors duration-300"
                    />
                  </div>
                </div>
                <!-- 其他类型或无封面图片时显示图标 -->
                <div v-else class="w-full h-full bg-gradient-to-br from-base-200 to-base-300 flex items-center justify-center rounded-xl">
                  <component
                    :is="getItemIcon(item.content_type)"
                    class="h-8 w-8 text-base-content/60 group-hover:text-primary transition-colors duration-300"
                  />
                </div>
              </div>

              <!-- 右侧内容 -->
              <div class="flex-1">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <!-- 头部信息 -->
                    <div class="flex items-center gap-2 mb-3">
                      <span class="badge badge-secondary badge-sm shadow-sm transition-all duration-200 hover:scale-105">
                        {{ getContentTypeLabel(item.content_type) }}
                      </span>
                      <span class="text-sm text-base-content/70 font-medium">
                        相关性: {{ Math.round(item.rank * 100) }}%
                      </span>
                    </div>

                    <!-- 标题 -->
                    <h3 class="text-base sm:text-lg font-semibold text-base-content mb-3 group-hover:text-primary transition-colors duration-300 leading-tight">
                      <a :href="getItemUrl(item)" class="hover:underline">
                        {{ item.title }}
                      </a>
                    </h3>

                    <!-- 描述 -->
                    <p class="text-sm text-base-content/70 mb-4 leading-relaxed line-clamp-2">
                      {{ item.description }}
                    </p>

                    <!-- 高亮片段 -->
                    <div v-if="item.highlight" class="text-xs text-base-content/60 bg-base-200 p-2 rounded mb-4">
                      <span v-html="item.highlight"></span>
                    </div>
                  </div>

                  <!-- 箭头图标 -->
                  <a :href="getItemUrl(item)" class="btn btn-ghost btn-sm btn-circle hover:btn-primary group-hover:scale-110 transition-all duration-300">
                    <ChevronRight class="h-4 w-4" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="searchResults.pagination.total_pages > 1" class="flex justify-center mt-8">
          <div class="join">
            <button
              class="join-item btn btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="!searchResults.pagination.has_previous"
              @click="goToPage(searchResults.pagination.page - 1)"
            >
              上一页
            </button>
            <button
              v-for="page in visiblePages"
              :key="page"
              class="join-item btn btn-sm"
              :class="{ 'btn-active': page === searchResults.pagination.page, 'btn-disabled': typeof page !== 'number' }"
              @click="typeof page === 'number' ? goToPage(page) : undefined"
            >
              {{ page }}
            </button>
            <button
              class="join-item btn btn-sm disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="!searchResults.pagination.has_next"
              @click="goToPage(searchResults.pagination.page + 1)"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 无搜索结果 -->
      <div v-else class="text-center py-12">
        <div class="text-base-content/70 mb-4">
          <Search class="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p class="text-lg">未找到匹配的结果</p>
          <p class="text-sm">请尝试调整搜索关键词</p>
        </div>
        <a href="/search" class="btn btn-outline mt-4">
          返回搜索
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  Search,
  Clock,
  ChevronRight,
  ShieldAlert,
  AlertTriangle,
  Skull,
  BookOpen,
  Target,
  Users,
  FileText,
  Wrench,
  Eye
} from 'lucide-vue-next'
import { searchApi } from '@/lib/api/system'
import type { SearchResponse, SearchRequestParams } from '@/lib/api/types'

// Props
interface Props {
  query?: string
}

const props = withDefaults(defineProps<Props>(), {
  query: ''
})

// 响应式数据
const searchQuery = ref(props.query)
const searchResults = ref<SearchResponse | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)
const selectedContentTypes = ref<string[]>([])

// 内容类型映射
const contentTypeLabels: Record<string, string> = {
  'ransomware_group': '勒索组织',
  'negotiation_record': '谈判记录',
  'intel_post': '威胁情报',
  'tools': '应急工具',
  'ransom_note': '勒索信',
  'ioc_indicator': 'IOC指标',
  'victim': '受害者',
  'blog_post': '安全博客'
}

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim()) return

  loading.value = true
  error.value = null

  try {
    const params: SearchRequestParams = {
      q: searchQuery.value.trim(),
      page: 1,
      page_size: 20
    }

    // 如果有选中的内容类型，添加到参数中
    if (selectedContentTypes.value.length > 0) {
      params.content_types = selectedContentTypes.value.join(',')
    }

    const response = await searchApi.search(params)
    searchResults.value = response
  } catch (err) {
    error.value = err instanceof Error ? err.message : '搜索失败，请稍后重试'
    console.error('Search error:', err)
  } finally {
    loading.value = false
  }
}

// 分页跳转
const goToPage = async (page: number) => {
  if (!searchQuery.value.trim() || !searchResults.value) return

  loading.value = true
  error.value = null

  try {
    const params: SearchRequestParams = {
      q: searchQuery.value.trim(),
      page,
      page_size: 20
    }

    if (selectedContentTypes.value.length > 0) {
      params.content_types = selectedContentTypes.value.join(',')
    }

    const response = await searchApi.search(params)
    searchResults.value = response
  } catch (err) {
    error.value = err instanceof Error ? err.message : '搜索失败，请稍后重试'
    console.error('Search error:', err)
  } finally {
    loading.value = false
  }
}

// 内容类型筛选
const toggleContentType = (contentType: string) => {
  const index = selectedContentTypes.value.indexOf(contentType)
  if (index > -1) {
    selectedContentTypes.value.splice(index, 1)
  } else {
    selectedContentTypes.value.push(contentType)
  }
  performSearch() // 重新搜索
}

const clearContentTypeFilter = () => {
  selectedContentTypes.value = []
  performSearch() // 重新搜索
}

// 获取内容类型标签
const getContentTypeLabel = (contentType: string): string => {
  return contentTypeLabels[contentType] || contentType
}

// 分页计算
const visiblePages = computed(() => {
  if (!searchResults.value) return []

  const pages: (number | string)[] = []
  const total = searchResults.value.pagination.total_pages
  const current = searchResults.value.pagination.page

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    } else if (current >= total - 3) {
      pages.push(1, '...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1, '...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    }
  }

  return pages.filter(page => page !== '...' || pages.indexOf(page) === pages.lastIndexOf(page))
})

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  if (target) {
    target.style.display = 'none'
    const fallbackDiv = target.nextElementSibling as HTMLElement
    if (fallbackDiv) {
      fallbackDiv.style.display = 'flex'
    }
  }
}

// 获取项目图标
const getItemIcon = (contentType: string) => {
  switch (contentType) {
    case 'ransomware_group':
      return Skull
    case 'negotiation_record':
      return Users
    case 'intel_post':
      return ShieldAlert
    case 'tools':
      return Wrench
    case 'ransom_note':
      return FileText
    case 'ioc_indicator':
      return Target
    case 'victim':
      return AlertTriangle
    case 'blog_post':
      return Eye
    default:
      return BookOpen
  }
}

// 获取项目URL
const getItemUrl = (item: any) => {
  switch (item.content_type) {
    case 'ransomware_group':
      // 使用slug而不是id
      return `/ransomware/${item.data?.slug || item.object_id}`
    case 'negotiation_record':
      return `/negotiation/${item.object_id}`
    case 'intel_post':
      return `/intell/${item.object_id}`
    case 'tools':
      return `/tools/${item.object_id}`
    case 'ransom_note':
      return `/ransom-note/${item.object_id}`
    case 'ioc_indicator':
      return `/ioc/${item.object_id}`
    case 'victim':
      return `/victim/${item.object_id}`
    case 'blog_post':
      return `/blog/${item.object_id}`
    default:
      return '#'
  }
}

// 监听查询参数变化和执行搜索
watch(() => props.query, (newQuery) => {
  searchQuery.value = newQuery
  if (newQuery) {
    performSearch()
  }
}, { immediate: true })

// 组件挂载时执行搜索
onMounted(() => {
  if (props.query) {
    performSearch()
  }
})
</script>
