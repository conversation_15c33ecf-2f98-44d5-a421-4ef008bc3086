<template>
  <div class="bg-base-100 py-8 sm:py-16 lg:py-24">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">

      <!-- 博客分类导航 -->
      <div class="mb-12 border-b border-base-300 pb-8">
        <div class="flex flex-wrap justify-center gap-4">
          <!-- 全部分类按钮 -->
          <a
            href="/blog"
            :class="[
              'btn btn-sm transition-colors',
              selectedCategory === null || selectedCategory === '' ? 'btn-primary' : 'btn-outline hover:btn-primary'
            ]"
          >
            <BookOpen class="h-4 w-4 mr-2" />
            全部
            <span class="badge badge-secondary badge-sm ml-2">{{ allPostsCount }}</span>
          </a>

          <!-- 分类按钮 -->
          <a
            v-for="category in categories"
            :key="category.id"
            :href="`/blog?category=${category.slug}`"
            :class="[
              'btn btn-sm transition-colors',
              selectedCategory === category.slug ? 'btn-primary' : 'btn-outline hover:btn-primary'
            ]"
          >
            <component :is="getCategoryIcon(category.name)" class="h-4 w-4 mr-2" />
            {{ category.name }}
            <span class="badge badge-secondary badge-sm ml-2">{{ category.post_count }}</span>
          </a>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="loading loading-spinner loading-lg text-primary"></div>
        <span class="ml-3 text-base-content/70">正在加载文章...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <div class="alert alert-warning max-w-md mx-auto">
          <TriangleAlert class="stroke-current shrink-0 h-6 w-6" />
          <div>
            <div class="font-bold">加载失败</div>
            <div class="text-xs">{{ error }}</div>
          </div>
        </div>
      </div>

      <!-- Blog Grid -->
      <div v-else class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <!-- Blog Card -->
        <div
          v-for="article in paginatedArticles"
          :key="article.id"
          class="card card-border shadow-none hover:shadow-lg transition-all duration-300 border border-gray-50/10 hover:border-gray-100/30"
        >
          <figure>
            <a :href="`/blog/${article.id}`" class="block">
              <img
                :src="article.cover_image"
                :alt="article.title"
                class="w-full h-48 object-cover hover:scale-105 transition-transform duration-300 cursor-pointer"
                loading="lazy"
              />
            </a>
          </figure>
          <div class="card-body gap-3">
            <!-- 文章分类和发布时间 -->
            <div class="flex items-center justify-between mb-2">
              <span :class="getCategoryBadgeClass(article.category)">
                {{ article.category }}
              </span>
              <div class="flex items-center gap-1 text-xs text-base-content/60">
                <Clock class="h-3 w-3" />
                {{ formatDate(article.created_at) }}
              </div>
            </div>

            <!-- 文章标题 -->
            <h5 class="card-title text-xl leading-tight">
              <a
                :href="`/blog/${article.id}`"
                class="hover:text-primary transition-colors duration-200 cursor-pointer"
              >
                {{ article.title }}
              </a>
            </h5>

            <!-- 文章摘要 -->
            <p class="mb-5 text-base-content/70 line-clamp-3">
              {{ article.excerpt }}
            </p>

            <!-- 文章标签 -->
            <div class="flex flex-wrap gap-2 mb-4">
              <span
                v-for="tag in article.tags.slice(0, 3)"
                :key="tag"
                class="badge badge-outline badge-sm"
              >
                {{ tag }}
              </span>
            </div>

            <!-- 作者信息和阅读按钮 -->
            <div class="flex items-center justify-between">


              <div class="card-actions">
                <a
                  :href="`/blog/${article.id}`"
                  class="btn btn-primary btn-gradient btn-sm"
                >
                  阅读全文
                  <span class="icon-[tabler--arrow-right] size-4 rtl:rotate-180"></span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <Pagination
        v-if="!isLoading && !error && totalPages > 1"
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="totalCount"
        :show-info="true"
      />


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Clock, Shield, Bug, AlertTriangle, Users, BookOpen, Zap, TriangleAlert } from 'lucide-vue-next'
import Pagination from '@/components/ui/Pagination.vue'
import { blogApi } from '@/lib/api'
import type { BlogPost, BlogCategory } from '@/types/api'
import { formatDate } from '@/lib/utils'

// 获取URL参数中的页码和分类
const getPageFromUrl = () => {
  if (typeof window === 'undefined') return 1
  const urlParams = new URLSearchParams(window.location.search)
  const page = parseInt(urlParams.get('page') || '1')
  return page > 0 ? page : 1
}

const getCategoryFromUrl = () => {
  if (typeof window === 'undefined') return null
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('category') || null
}

// 响应式数据
const isLoading = ref(false)
const currentPage = ref(getPageFromUrl())
const pageSize = ref(6) // 每页显示6篇文章
const totalCount = ref(0)
const error = ref<string | null>(null)
const selectedCategory = ref<string | null>(null) // 当前选中的分类

// 博客文章数据
const blogArticles = ref<BlogPost[]>([])
const categories = ref<BlogCategory[]>([])
const allPostsCount = ref(0) // 所有文章总数

// 分类图标映射
const categoryIcons = {
  '官网WP': BookOpen,
  '病毒分析': Bug,
  '文章转载': BookOpen,
  '应急响应工具教程': Users,
  '紧急预警': AlertTriangle,
  '漏洞与预防': Shield,
  '攻击手法分析': Zap,
  '病毒家族历史': Bug,
  '成功案例': Users
}

// 获取分类图标
const getCategoryIcon = (categoryName: string) => {
  return categoryIcons[categoryName as keyof typeof categoryIcons] || Shield
}

// 获取分类徽章样式
const getCategoryBadgeClass = (category: string) => {
  switch (category) {
    case '官网WP': return 'badge badge-primary'
    case '病毒分析': return 'badge badge-error'
    case '文章转载': return 'badge badge-info'
    case '应急响应工具教程': return 'badge badge-success'
    case '紧急预警': return 'badge badge-warning'
    case '漏洞与预防': return 'badge badge-info'
    case '攻击手法分析': return 'badge badge-error'
    case '病毒家族历史': return 'badge badge-secondary'
    case '成功案例': return 'badge badge-success'
    default: return 'badge badge-outline'
  }
}

// 获取博客文章数据
const fetchPosts = async () => {
  try {
    isLoading.value = true
    error.value = null

    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value
    }

    // 如果选择了分类，添加分类参数
    if (selectedCategory.value) {
      params.category = selectedCategory.value
    }

    const response = await blogApi.getPosts(params)

    // 处理完整的API响应
    if (response.success && response.data) {
      blogArticles.value = response.data || []
      if (response.pagination) {
        totalCount.value = response.pagination.count || 0
      } else {
        totalCount.value = blogArticles.value.length
      }
    } else {
      throw new Error(response.message || '获取文章列表失败')
    }
  } catch (err) {
    console.error('获取博客文章失败:', err)
    error.value = err instanceof Error ? err.message : '获取文章列表失败'
  } finally {
    isLoading.value = false
  }
}

// 获取分类数据
const fetchCategories = async () => {
  try {
    const response = await blogApi.getCategories()
    // 处理完整的API响应
    if (response.success && response.data) {
      categories.value = response.data || []
      // 计算所有文章总数（各分类文章数量之和）
      allPostsCount.value = categories.value.reduce((total, category) => total + (category.post_count || 0), 0)
    }
  } catch (err) {
    console.error('获取分类失败:', err)
  }
}



// 分页计算
const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))
const paginatedArticles = computed(() => blogArticles.value)

// 组件挂载时的初始化
onMounted(async () => {
  console.log('安全博客列表组件已加载')
  // 在挂载后设置当前选中的分类
  selectedCategory.value = getCategoryFromUrl()
  console.log('当前URL:', window.location.href)
  console.log('选中的分类:', selectedCategory.value)
  await Promise.all([fetchPosts(), fetchCategories()])
})
</script>


