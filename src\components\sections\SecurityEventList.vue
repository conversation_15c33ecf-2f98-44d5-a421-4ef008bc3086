<template>
  <section class="py-16 bg-base-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 内容区域 -->
      <div>
          <!-- 工具栏 -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h2 class="text-2xl font-bold text-base-content mb-2">安全事件列表</h2>
              <p class="text-base-content/70">
                共找到 {{ filteredEvents.length }} 条安全事件
              </p>
            </div>

            <!-- 视图切换和排序 -->
            <div class="flex items-center gap-4 mt-4 sm:mt-0">
              <!-- 排序选择 -->
              <select v-model="sortBy" class="select select-bordered select-sm" @change="handleSort">
                <option value="detection_time_desc">最新检测</option>
                <option value="detection_time_asc">最早检测</option>
                <option value="risk_score_desc">风险评分高</option>
                <option value="risk_score_asc">风险评分低</option>
                <option value="event_count_desc">事件数量多</option>
                <option value="event_count_asc">事件数量少</option>
              </select>

              <!-- 视图模式切换 -->
              <div class="flex items-center gap-1">
                <button
                  :class="['btn btn-sm', viewMode === 'list' ? 'btn-primary' : 'btn-outline']"
                  @click="updateViewMode('list')"
                >
                  <List class="h-4 w-4" />
                </button>
                <button
                  :class="['btn btn-sm', viewMode === 'grid' ? 'btn-primary' : 'btn-outline']"
                  @click="updateViewMode('grid')"
                >
                  <Grid class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="flex justify-center items-center py-16">
            <div class="loading loading-spinner loading-lg text-primary"></div>
          </div>

          <!-- 安全事件列表 -->
          <div v-else-if="filteredEvents.length > 0">
            <div :class="[
              'grid gap-6',
              viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' : 'grid-cols-1'
            ]">
              <SecurityEventCard v-for="event in paginatedEvents" :key="event.id" :event="event" />
            </div>

            <!-- 分页 -->
            <Pagination
              :current-page="currentPage"
              :total-pages="totalPages"
              :total-items="filteredEvents.length"
              :show-info="true"
            />
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-16">
            <div class="text-6xl mb-4">🔍</div>
            <h3 class="text-xl font-semibold text-base-content mb-2">未找到安全事件</h3>
            <p class="text-base-content/70 mb-6">
              暂无安全事件数据
            </p>
          </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

import SecurityEventCard from '../../components/ui/SecurityEventCard.vue'
import Pagination from '@/components/ui/Pagination.vue'
import {
  Search,
  Grid,
  List
} from 'lucide-vue-next'
import type { SecurityEvent } from '@/types/api'
import { securityEventApi } from '@/lib/api'

// 获取URL参数
const getUrlParams = () => {
  // 检查是否在客户端环境
  if (typeof window === 'undefined') {
    return {
      page: 1,
      pageSize: 12,
      sortBy: 'detection_time_desc',
      viewMode: 'list' as 'grid' | 'list',
      search: '',
      severity: '',
      type: '',
      status: '',
      riskScoreMin: null,
      riskScoreMax: null,
      timeRange: ''
    }
  }

  const urlParams = new URLSearchParams(window.location.search)
  return {
    page: parseInt(urlParams.get('page') || '1'),
    pageSize: parseInt(urlParams.get('pageSize') || '12'),
    sortBy: urlParams.get('sortBy') || 'detection_time_desc',
    viewMode: (urlParams.get('viewMode') || 'list') as 'grid' | 'list',
    search: urlParams.get('search') || '',
    severity: urlParams.get('severity') || '',
    type: urlParams.get('type') || '',
    status: urlParams.get('status') || '',
    riskScoreMin: urlParams.get('riskScoreMin') ? parseInt(urlParams.get('riskScoreMin')!) : null,
    riskScoreMax: urlParams.get('riskScoreMax') ? parseInt(urlParams.get('riskScoreMax')!) : null,
    timeRange: urlParams.get('timeRange') || ''
  }
}

// 响应式数据
const events = ref<SecurityEvent[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(12)
const sortBy = ref('detection_time_desc')
const viewMode = ref<'grid' | 'list'>('list')

// 筛选器状态
const filters = ref({
  search: '',
  severity: '',
  type: '',
  status: '',
  riskScoreMin: null as number | null,
  riskScoreMax: null as number | null,
  timeRange: ''
})

// 初始化URL参数
const initFromUrl = () => {
  const params = getUrlParams()
  currentPage.value = params.page
  pageSize.value = params.pageSize
  sortBy.value = params.sortBy
  viewMode.value = params.viewMode
  filters.value = {
    search: params.search,
    severity: params.severity,
    type: params.type,
    status: params.status,
    riskScoreMin: params.riskScoreMin,
    riskScoreMax: params.riskScoreMax,
    timeRange: params.timeRange
  }
}

// 更新URL
const updateUrl = () => {
  if (typeof window === 'undefined') return

  const params = new URLSearchParams()
  if (currentPage.value > 1) params.set('page', currentPage.value.toString())
  if (pageSize.value !== 12) params.set('pageSize', pageSize.value.toString())
  if (sortBy.value !== 'detection_time_desc') params.set('sortBy', sortBy.value)
  if (viewMode.value !== 'list') params.set('viewMode', viewMode.value)
  if (filters.value.search) params.set('search', filters.value.search)
  if (filters.value.severity) params.set('severity', filters.value.severity)
  if (filters.value.type) params.set('type', filters.value.type)
  if (filters.value.status) params.set('status', filters.value.status)
  if (filters.value.riskScoreMin !== null) params.set('riskScoreMin', filters.value.riskScoreMin.toString())
  if (filters.value.riskScoreMax !== null) params.set('riskScoreMax', filters.value.riskScoreMax.toString())
  if (filters.value.timeRange) params.set('timeRange', filters.value.timeRange)

  const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`
  window.history.pushState({}, '', newUrl)
}



// 筛选后的安全事件
const filteredEvents = computed(() => {
  let result = [...events.value]

  // 搜索筛选
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase()
    result = result.filter(event =>
      event.title.toLowerCase().includes(searchTerm) ||
      event.description.toLowerCase().includes(searchTerm) ||
      event.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
      event.source_ip.includes(searchTerm) ||
      event.target_ip.includes(searchTerm)
    )
  }

  // 严重程度筛选
  if (filters.value.severity) {
    result = result.filter(event => event.severity === filters.value.severity)
  }

  // 类型筛选
  if (filters.value.type) {
    result = result.filter(event => event.type === filters.value.type)
  }

  // 状态筛选
  if (filters.value.status) {
    result = result.filter(event => event.status === filters.value.status)
  }

  // 风险评分筛选
  if (filters.value.riskScoreMin !== null) {
    result = result.filter(event => event.risk_score >= filters.value.riskScoreMin!)
  }
  if (filters.value.riskScoreMax !== null) {
    result = result.filter(event => event.risk_score <= filters.value.riskScoreMax!)
  }

  // 时间范围筛选
  if (filters.value.timeRange) {
    const now = new Date()
    let startDate: Date

    switch (filters.value.timeRange) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(0)
    }

    result = result.filter(event => new Date(event.detection_time) >= startDate)
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'detection_time_desc':
        return new Date(b.detection_time).getTime() - new Date(a.detection_time).getTime()
      case 'detection_time_asc':
        return new Date(a.detection_time).getTime() - new Date(b.detection_time).getTime()
      case 'risk_score_desc':
        return b.risk_score - a.risk_score
      case 'risk_score_asc':
        return a.risk_score - b.risk_score
      case 'event_count_desc':
        return b.event_count - a.event_count
      case 'event_count_asc':
        return a.event_count - b.event_count
      default:
        return 0
    }
  })

  return result
})

// 分页计算
const totalPages = computed(() => Math.ceil(filteredEvents.value.length / pageSize.value))
const paginatedEvents = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredEvents.value.slice(start, end)
})







// 处理排序变化
const handleSort = () => {
  currentPage.value = 1 // 重置到第一页
  updateUrl()
}

// 更新视图模式
const updateViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode
  updateUrl()
}



// 监听筛选器变化
watch(filters, () => {
  currentPage.value = 1
  updateUrl()
}, { deep: true })

// 监听状态变化，同步URL
watch([currentPage, sortBy, viewMode], () => {
  updateUrl()
})





// 加载安全事件数据
const loadEvents = async () => {
  try {
    loading.value = true

    // 尝试调用真实API
    try {
      const response = await securityEventApi.getList({
        page: 1,
        pageSize: 50
      })

      if (response.code === 200 && response.data.list) {
        // 为API数据添加封面图片
        events.value = response.data.list.map((event, index) => ({
          ...event,
          coverImage: event.coverImage || [
            'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format',
            'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=400&h=250&fit=crop&auto=format',
            'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=250&fit=crop&auto=format',
            'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop&auto=format',
            'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&auto=format'
          ][index % 5]
        }))
        return
      }
    } catch (apiError) {
      console.warn('API调用失败，使用模拟数据:', apiError)
    }

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据作为fallback
    events.value = Array.from({ length: 50 }, (_, index) => {
      const eventTypes = ['恶意软件感染', 'DDoS攻击', '数据泄露', '异常登录', '网络入侵', '系统漏洞', 'APT攻击', '勒索软件', '钓鱼攻击', '内网横移']
      const targets = ['Web服务器', '数据库服务器', '邮件服务器', '文件服务器', '域控制器', '办公终端', '生产系统', '测试环境']
      const severities = ['严重', '高', '中', '低'] as const
      const severity = severities[index % 4]
      const statuses = ['新事件', '处理中', '已处理', '已关闭', '误报'] as const

      // 根据严重程度生成合理的数据
      const severityConfig = {
        '严重': { riskRange: [80, 100], eventRange: [50, 200] },
        '高': { riskRange: [60, 85], eventRange: [20, 100] },
        '中': { riskRange: [30, 65], eventRange: [5, 50] },
        '低': { riskRange: [1, 35], eventRange: [1, 20] }
      }

      const config = severityConfig[severity]
      const riskScore = Math.floor(Math.random() * (config.riskRange[1] - config.riskRange[0] + 1)) + config.riskRange[0]
      const eventCount = Math.floor(Math.random() * (config.eventRange[1] - config.eventRange[0] + 1)) + config.eventRange[0]

      const detectionTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      const firstSeenTime = new Date(detectionTime.getTime() - Math.random() * 48 * 60 * 60 * 1000)
      const lastSeenTime = new Date(detectionTime.getTime() + Math.random() * 24 * 60 * 60 * 1000)

      return {
        id: index + 1,
        event_id: `SEC${String(index + 1).padStart(6, '0')}`,
        title: `${eventTypes[index % eventTypes.length]}事件 - ${targets[index % targets.length]}${eventTypes[index % eventTypes.length]}检测`,
        type: ['入侵检测', '恶意软件', '异常行为', '数据泄露', '网络攻击', '系统异常'][index % 6],
        severity,
        status: statuses[index % 5],
        source_ip: (() => {
          const ranges = ['192.168.1.', '10.0.0.', '172.16.1.', '203.0.113.', '198.51.100.']
          const range = ranges[index % ranges.length]
          return range + (index % 254 + 1)
        })(),
        target_ip: (() => {
          const ranges = ['10.0.1.', '192.168.100.', '172.16.10.', '10.10.10.']
          const range = ranges[index % ranges.length]
          return range + (index % 254 + 1)
        })(),
        source_system: ['外部网络', 'Web服务器', '邮件服务器', '文件服务器', '未知系统', '移动设备', 'VPN连接'][index % 7],
        target_system: ['内网主机', '办公电脑', '数据库服务器', '域控制器', '文件服务器', '生产服务器', '测试环境'][index % 7],
        description: [
          '检测到恶意软件在目标系统上执行，具有数据窃取和远程控制能力，需要立即处理。',
          '发现大规模DDoS攻击流量，攻击者试图通过流量洪水使服务不可用。',
          '监测到敏感数据异常外传，可能存在数据泄露风险，需要紧急调查。',
          '发现多次异常登录尝试，疑似暴力破解攻击，建议加强账户安全。',
          '检测到网络入侵行为，攻击者已获得初始访问权限，正在进行横向移动。',
          '发现系统存在高危漏洞，攻击者可能利用该漏洞获取系统权限。',
          '监测到APT攻击活动，攻击者使用高级持续威胁技术进行长期潜伏。',
          '检测到勒索软件活动，恶意软件正在加密文件并要求赎金。',
          '发现钓鱼攻击邮件，攻击者试图窃取用户凭据和敏感信息。',
          '检测到内网横向移动行为，攻击者正在扩大攻击范围。'
        ][index % 10],
        detection_time: detectionTime.toISOString(),
        first_seen: firstSeenTime.toISOString(),
        last_seen: lastSeenTime.toISOString(),
        event_count: eventCount,
        risk_score: riskScore,
        analyst: ['张安全', '李防护', '王监控', '赵分析', '刘响应', '陈检测', '杨调查', '周处理'][index % 8],
        tags: [
          ['恶意软件', '木马', '远程控制', '数据窃取'],
          ['DDoS', '流量攻击', '服务中断', '可用性'],
          ['数据泄露', '敏感信息', '隐私泄露', '合规风险'],
          ['异常登录', '暴力破解', '账户安全', '身份验证'],
          ['网络入侵', 'APT攻击', '横向移动', '权限提升'],
          ['系统漏洞', '代码执行', '权限提升', '补丁管理'],
          ['APT攻击', '高级威胁', '持续威胁', '定向攻击'],
          ['勒索软件', '文件加密', '赎金要求', '业务中断'],
          ['钓鱼攻击', '社会工程', '凭据窃取', '邮件安全'],
          ['内网横移', '权限扩展', '网络渗透', '资产发现']
        ][index % 10],
        coverImage: [
          'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop&auto=format',
          'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&auto=format'
        ][index % 5]
      }
    })
  } catch (error) {
    console.error('加载安全事件失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听浏览器前进后退 - 只在客户端执行
if (typeof window !== 'undefined') {
  window.addEventListener('popstate', () => {
    initFromUrl()
  })
}

// 组件挂载时初始化
onMounted(() => {
  initFromUrl()
  loadEvents()
})
</script>
