<template>
  <section class="py-16 bg-base-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 内容区域 -->
      <div>
          <!-- 工具栏 -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
            <div>
              <h2 class="text-2xl font-bold text-base-content mb-2">威胁情报列表</h2>
              <p class="text-base-content/70">
                共找到 {{ filteredThreats.length }} 条威胁情报
              </p>
            </div>

            <!-- 排序和视图选项 -->
            <div class="flex items-center gap-4 mt-4 sm:mt-0">
              <select v-model="sortBy" class="select select-bordered select-sm" @change="handleSort">
                <option value="created_at_desc">最新发布</option>
                <option value="created_at_asc">最早发布</option>
                <option value="severity_desc">严重程度（高到低）</option>
                <option value="severity_asc">严重程度（低到高）</option>
                <option value="confidence_desc">置信度（高到低）</option>
                <option value="confidence_asc">置信度（低到高）</option>
              </select>

              <div class="flex items-center gap-1">
                <button :class="['btn btn-sm', viewMode === 'list' ? 'btn-primary' : 'btn-outline']"
                  @click="viewMode = 'list'">
                  <List class="h-4 w-4" />
                </button>
                <button :class="['btn btn-sm', viewMode === 'grid' ? 'btn-primary' : 'btn-outline']"
                  @click="viewMode = 'grid'">
                  <Grid class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="flex justify-center py-12">
            <span class="loading loading-spinner loading-lg text-primary"></span>
          </div>

          <!-- 威胁情报列表 -->
          <div v-else-if="filteredThreats.length > 0">
            <div :class="[
              'grid gap-6',
              viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' : 'grid-cols-1'
            ]">
              <ThreatIntelligenceCard v-for="threat in paginatedThreats" :key="threat.id" :threat="threat" />
            </div>

            <!-- 分页 -->
            <Pagination :current-page="currentPage" :total-pages="totalPages" :total-items="filteredThreats.length"
              :show-info="true" />
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-16">
            <div class="text-base-content/50 mb-6">
              <Search class="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 class="text-xl font-semibold mb-2">未找到匹配的威胁情报</h3>
              <p class="text-base-content/70">
                暂无威胁情报数据
              </p>
            </div>
          </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

import ThreatIntelligenceCard from '@/components/ui/ThreatIntelligenceCard.vue'
import Pagination from '@/components/ui/Pagination.vue'
import {
  Search,
  Grid,
  List
} from 'lucide-vue-next'
import type { ThreatIntelligence } from '@/types/api'
import { threatIntelligenceApi } from '@/lib/api'

// 定义props
interface Props {
  category?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  category: null
})

// 获取URL参数
const getUrlParams = () => {
  // 检查是否在客户端环境
  if (typeof window === 'undefined') {
    return {
      page: 1,
      pageSize: 12,
      sortBy: 'created_at_desc',
      viewMode: 'list' as 'grid' | 'list',
      search: '',
      severity: '',
      type: '',
      source: '',
      status: '',
      confidenceMin: null,
      confidenceMax: null,
      timeRange: '',
      category: props.category || ''
    }
  }

  const urlParams = new URLSearchParams(window.location.search)
  return {
    page: parseInt(urlParams.get('page') || '1'),
    pageSize: parseInt(urlParams.get('pageSize') || '12'),
    sortBy: urlParams.get('sortBy') || 'created_at_desc',
    viewMode: (urlParams.get('viewMode') as 'grid' | 'list') || 'list',
    search: urlParams.get('search') || '',
    severity: urlParams.get('severity') || '',
    type: urlParams.get('type') || '',
    source: urlParams.get('source') || '',
    status: urlParams.get('status') || '',
    confidenceMin: urlParams.get('confidenceMin') ? parseInt(urlParams.get('confidenceMin')!) : null,
    confidenceMax: urlParams.get('confidenceMax') ? parseInt(urlParams.get('confidenceMax')!) : null,
    timeRange: urlParams.get('timeRange') || '',
    category: urlParams.get('category') || props.category || ''
  }
}

// 更新URL参数
const updateUrl = () => {
  // 检查是否在客户端环境
  if (typeof window === 'undefined') return

  const params = new URLSearchParams()

  if (currentPage.value > 1) params.set('page', currentPage.value.toString())
  if (pageSize.value !== 12) params.set('pageSize', pageSize.value.toString())
  if (sortBy.value !== 'created_at_desc') params.set('sortBy', sortBy.value)
  if (viewMode.value !== 'list') params.set('viewMode', viewMode.value)
  if (filters.value.search) params.set('search', filters.value.search)
  if (filters.value.severity) params.set('severity', filters.value.severity)
  if (filters.value.type) params.set('type', filters.value.type)
  if (filters.value.source) params.set('source', filters.value.source)
  if (filters.value.status) params.set('status', filters.value.status)
  if (filters.value.confidenceMin !== null) params.set('confidenceMin', filters.value.confidenceMin.toString())
  if (filters.value.confidenceMax !== null) params.set('confidenceMax', filters.value.confidenceMax.toString())
  if (filters.value.timeRange) params.set('timeRange', filters.value.timeRange)

  const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`
  window.history.pushState({}, '', newUrl)
}

// 初始化URL参数
const urlParams = getUrlParams()

// 响应式数据
const loading = ref(true)
const threats = ref<(ThreatIntelligence & { coverImage?: string })[]>([])
const currentPage = ref(urlParams.page)
const pageSize = ref(urlParams.pageSize)
const viewMode = ref<'grid' | 'list'>(urlParams.viewMode)
const sortBy = ref(urlParams.sortBy)

// 筛选器状态 - 从URL参数初始化
const filters = ref({
  search: urlParams.search,
  severity: urlParams.severity,
  type: urlParams.type,
  source: urlParams.source,
  status: urlParams.status,
  confidenceMin: urlParams.confidenceMin,
  confidenceMax: urlParams.confidenceMax,
  timeRange: urlParams.timeRange,
  category: urlParams.category
})



// 筛选后的威胁情报
const filteredThreats = computed(() => {
  let result = [...threats.value]

  // 搜索筛选
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase()
    result = result.filter(threat =>
      threat.title.toLowerCase().includes(searchTerm) ||
      threat.description.toLowerCase().includes(searchTerm) ||
      threat.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  // 严重程度筛选
  if (filters.value.severity) {
    result = result.filter(threat => threat.severity === filters.value.severity)
  }

  // 类型筛选
  if (filters.value.type) {
    result = result.filter(threat => threat.type === filters.value.type)
  }

  // 来源筛选
  if (filters.value.source) {
    result = result.filter(threat => threat.source === filters.value.source)
  }

  // 状态筛选
  if (filters.value.status) {
    result = result.filter(threat => threat.status === filters.value.status)
  }

  // 置信度筛选
  if (filters.value.confidenceMin !== null) {
    result = result.filter(threat => threat.confidence >= filters.value.confidenceMin!)
  }
  if (filters.value.confidenceMax !== null) {
    result = result.filter(threat => threat.confidence <= filters.value.confidenceMax!)
  }

  // 时间范围筛选
  if (filters.value.timeRange) {
    const now = new Date()
    const filterDate = new Date()

    switch (filters.value.timeRange) {
      case 'today':
        filterDate.setHours(0, 0, 0, 0)
        break
      case 'week':
        filterDate.setDate(now.getDate() - 7)
        break
      case 'month':
        filterDate.setMonth(now.getMonth() - 1)
        break
      case 'quarter':
        filterDate.setMonth(now.getMonth() - 3)
        break
    }

    result = result.filter(threat => new Date(threat.created_at) >= filterDate)
  }

  // 排序
  result.sort((a, b) => {
    switch (sortBy.value) {
      case 'created_at_desc':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      case 'created_at_asc':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'severity_desc':
        return getSeverityWeight(b.severity) - getSeverityWeight(a.severity)
      case 'severity_asc':
        return getSeverityWeight(a.severity) - getSeverityWeight(b.severity)
      case 'confidence_desc':
        return b.confidence - a.confidence
      case 'confidence_asc':
        return a.confidence - b.confidence
      default:
        return 0
    }
  })

  return result
})

// 分页相关计算属性
const totalPages = computed(() => Math.ceil(filteredThreats.value.length / pageSize.value))

const paginatedThreats = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredThreats.value.slice(start, end)
})



// 获取严重程度权重
const getSeverityWeight = (severity: string) => {
  switch (severity) {
    case '严重': return 4
    case '高': return 3
    case '中': return 2
    case '低': return 1
    default: return 0
  }
}

// 方法



const handleSort = () => {
  currentPage.value = 1 // 重置到第一页
  updateUrl() // 更新URL
}





// 加载威胁情报数据
const loadThreats = async () => {
  try {
    loading.value = true

    // 构建API参数
    const apiParams: any = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      ...filters.value
    }

    // 如果有分类参数，添加到API调用中
    if (filters.value.category) {
      apiParams.category = filters.value.category
    }

    // 调用真实API
    const response = await threatIntelligenceApi.getList(apiParams)



    // 检查响应数据结构
    let threatList: any[] = []

    // 根据后端API响应格式处理数据
    if (response && (response as any).success && (response as any).data) {
      // 后端返回格式: {success: true, message: string, data: [...], pagination: {...}}
      const data = (response as any).data
      if (Array.isArray(data)) {
        threatList = data
      } else if (data.list && Array.isArray(data.list)) {
        threatList = data.list
      }
    } else if (response && (response as any).list && Array.isArray((response as any).list)) {
      // 直接分页响应格式: {list: [], total: number, ...}
      threatList = (response as any).list
    } else if (response && Array.isArray(response)) {
      // 直接数组格式
      threatList = response as any[]
    } else {
      console.warn('API响应格式不符合预期:', response)
      threatList = []
    }

    // 为API数据添加封面图片和字段映射
    threats.value = threatList.map((threat: any, index: number) => ({
      ...threat,
      // 添加前端需要的字段映射
      status: threat.status || '已处理',
      indicators: threat.indicators || {
        ip: [],
        domain: [],
        hash: [],
        url: []
      },
      analyst: threat.analyst || '系统管理员',
      coverImage: threat.coverImage || threat.cover_image || [
        'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format',
        'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=400&h=250&fit=crop&auto=format',
        'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=250&fit=crop&auto=format',
        'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop&auto=format',
        'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&auto=format'
      ][index % 5]
    }))
  } catch (error) {
    console.error('加载威胁情报失败:', error)
    // 如果API调用失败，显示空列表
    threats.value = []
  } finally {
    loading.value = false
  }
}

// 监听状态变化，同步URL
watch([viewMode, sortBy, currentPage], () => {
  updateUrl()
})

// 监听浏览器前进后退 - 只在客户端执行
if (typeof window !== 'undefined') {
  window.addEventListener('popstate', () => {
    const params = getUrlParams()
    currentPage.value = params.page
    pageSize.value = params.pageSize
    viewMode.value = params.viewMode
    sortBy.value = params.sortBy
    filters.value = {
      search: params.search,
      severity: params.severity,
      type: params.type,
      source: params.source,
      status: params.status,
      confidenceMin: params.confidenceMin,
      confidenceMax: params.confidenceMax,
      timeRange: params.timeRange,
      category: params.category
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  // 在客户端重新初始化URL参数
  if (typeof window !== 'undefined') {
    const params = getUrlParams()
    currentPage.value = params.page
    pageSize.value = params.pageSize
    viewMode.value = params.viewMode
    sortBy.value = params.sortBy
    filters.value = {
      search: params.search,
      severity: params.severity,
      type: params.type,
      source: params.source,
      status: params.status,
      confidenceMin: params.confidenceMin,
      confidenceMax: params.confidenceMax,
      timeRange: params.timeRange,
      category: params.category
    }
  }

  loadThreats()
})
</script>
