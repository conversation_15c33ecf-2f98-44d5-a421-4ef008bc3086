<template>
  <div v-if="loading" class="flex justify-center py-12">
    <span class="loading loading-spinner loading-lg"></span>
  </div>

  <div v-else-if="tool" class="space-y-6">
    <!-- 工具头部信息 -->
    <div class="card bg-base-100 border border-gray-200/20">
      <div class="card-body p-6">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
          <!-- 工具基本信息 -->
          <div class="flex-1">
            <div class="flex items-center gap-4 mb-4">
              <div class="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center">
                <Wrench class="h-8 w-8 text-primary" />
              </div>
              <div>
                <h1 class="text-2xl lg:text-3xl font-bold text-base-content">{{ tool.name }}</h1>
                <div class="flex items-center gap-3 mt-2">
                  <span v-if="tool.file" class="badge badge-success">可下载</span>
                  <span class="text-sm text-base-content/60">
                    ID: #{{ tool.id }}
                  </span>
                  <span class="text-sm text-base-content/60">
                    更新时间：{{ formatDate(tool.updated_at) }}
                  </span>
                </div>
              </div>
            </div>

            <p v-if="tool.description" class="text-base-content/70 leading-relaxed mb-4">
              {{ tool.description }}
            </p>

            <!-- 工具详细信息 -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <div class="text-base-content/60 mb-1">浏览量</div>
                <div class="flex items-center gap-2">
                  <Eye class="h-4 w-4 text-base-content/60" />
                  <span>{{ tool.view_count.toLocaleString() }}</span>
                </div>
              </div>

              <div>
                <div class="text-base-content/60 mb-1">创建时间</div>
                <div>{{ formatDate(tool.created_at) }}</div>
              </div>

              <div>
                <div class="text-base-content/60 mb-1">最后更新</div>
                <div>{{ formatDate(tool.updated_at) }}</div>
              </div>

              <div v-if="tool.file">
                <div class="text-base-content/60 mb-1">文件状态</div>
                <div class="flex items-center gap-2">
                  <CheckCircle class="h-4 w-4 text-success" />
                  <span class="text-success">可下载</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col gap-3">
            <button
              v-if="tool.file"
              @click="downloadTool"
              class="btn btn-primary"
            >
              <Download class="h-5 w-5 mr-2" />
              下载工具
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具详细信息 -->
    <div :class="ransomwareGroup ? 'grid grid-cols-1 lg:grid-cols-3 gap-6' : ''">
      <!-- 工具内容 -->
      <div :class="ransomwareGroup ? 'lg:col-span-2' : ''">
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-6">
            <h2 class="text-xl font-semibold text-base-content mb-4">工具详情</h2>
            <div v-if="parsedContent" class="prose prose-sm max-w-none" v-html="parsedContent"></div>
            <div v-else class="text-base-content/50 text-center py-8">
              暂无详细内容
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边栏信息 -->
      <div v-if="ransomwareGroup" class="space-y-6">
        <!-- 相关组织信息 -->
        <div class="card bg-base-100 border border-gray-200/20">
          <div class="card-body p-6">
            <h3 class="text-lg font-semibold text-base-content mb-4">相关勒索组织</h3>
            <div class="space-y-3">
              <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-lg bg-warning/10 flex items-center justify-center">
                  <Shield class="h-5 w-5 text-warning" />
                </div>
                <div>
                  <div class="font-medium text-base-content">{{ ransomwareGroup.name }}</div>
                  <div class="text-sm text-base-content/60">
                    威胁等级：{{ getThreatLevelText(ransomwareGroup.threat_level) }}
                  </div>
                </div>
              </div>
              
              <a 
                :href="`/ransomware/${ransomwareGroup.id}`"
                class="btn btn-outline btn-sm w-full"
              >
                查看组织详情
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 错误状态 -->
  <div v-else class="text-center py-12">
    <AlertCircle class="h-16 w-16 mx-auto text-base-content/30 mb-4" />
    <h3 class="text-lg font-medium text-base-content/70 mb-2">工具不存在</h3>
    <p class="text-base-content/50 mb-4">
      请检查工具ID是否正确，或者该工具可能已被删除
    </p>
    <a href="/tools" class="btn btn-primary">
      返回工具列表
    </a>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Wrench,
  Download,
  Shield,
  CheckCircle,
  AlertCircle,
  Eye
} from 'lucide-vue-next'
import type { Tool, RansomwareGroup } from '@/types/api'
import { toolsApi, ransomwareGroupApi } from '@/lib/api'
import { useToast } from '@/composables/useToast'
import dayjs from 'dayjs'
import { useMarkdown } from '@/lib/markdown'

interface Props {
  toolId: number
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const tool = ref<Tool | null>(null)
const ransomwareGroup = ref<RansomwareGroup | null>(null)

// Toast功能
const { showSuccess, showError } = useToast()

// 初始化markdown解析器
const { smartParse } = useMarkdown()

// 计算属性：解析工具内容的markdown
const parsedContent = computed(() => {
  if (!tool.value?.content) return ''
  return smartParse(tool.value.content)
})

// 方法
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const getThreatLevelText = (level: string) => {
  const levelMap: Record<string, string> = {
    'critical': '极高',
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return levelMap[level] || level
}

const loadTool = async () => {
  loading.value = true
  try {
    tool.value = await toolsApi.getDetail(props.toolId)

    // 直接使用工具中包含的勒索组织信息
    if (tool.value.ransomware_group) {
      ransomwareGroup.value = tool.value.ransomware_group
    }
  } catch (error) {
    console.error('加载工具详情失败:', error)
    showError('加载工具详情失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

const downloadTool = async () => {
  if (!tool.value?.file) {
    showError('该工具没有可下载的文件')
    return
  }

  try {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = tool.value.file
    link.download = tool.value.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    showSuccess('工具下载已开始')
  } catch (error) {
    console.error('下载失败:', error)
    showError('下载失败，请稍后再试')
  }
}

// 生命周期
onMounted(() => {
  loadTool()
})
</script>

<style scoped>
.prose {
  color: inherit;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: inherit;
}

.prose a {
  color: hsl(var(--p));
}

.prose a:hover {
  color: hsl(var(--pf));
}

/* 代码高亮样式 */
.prose pre {
  background-color: hsl(var(--b2));
  border: 1px solid hsl(var(--bc) / 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.prose code {
  background-color: hsl(var(--b2));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

/* highlight.js 主题适配 */
.hljs {
  background: transparent !important;
  color: hsl(var(--bc));
}

.hljs-comment,
.hljs-quote {
  color: hsl(var(--bc) / 0.6);
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: hsl(var(--p));
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: hsl(var(--s));
}

.hljs-string,
.hljs-doctag {
  color: hsl(var(--a));
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: hsl(var(--in));
  font-weight: bold;
}
</style>
