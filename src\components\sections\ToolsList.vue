<template>
  <div class="bg-base-100 py-8 sm:py-16 lg:py-24">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <!-- 工具统计数据 -->
      <div class="mb-12 sm:mb-16 lg:mb-24">
        <!-- 统计卡片 -->
        <div v-if="!statsLoading && stats" class="grid grid-cols-2 gap-4 md:grid-cols-4">
          <!-- 总工具数 -->
          <div class="card bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
            <div class="card-body p-4 text-center">
              <div class="flex items-center justify-center mb-2">
                <Package class="h-8 w-8 text-primary" />
              </div>
              <div class="text-2xl font-bold text-primary">{{ stats.total.toLocaleString() }}</div>
              <div class="text-sm text-base-content/70 mt-1">总工具数</div>
            </div>
          </div>

          <!-- 可下载工具 -->
          <div class="card bg-gradient-to-br from-success/10 to-success/5 border border-success/20">
            <div class="card-body p-4 text-center">
              <div class="flex items-center justify-center mb-2">
                <Download class="h-8 w-8 text-success" />
              </div>
              <div class="text-2xl font-bold text-success">{{ stats.with_files.toLocaleString() }}</div>
              <div class="text-sm text-base-content/70 mt-1">可下载工具</div>
            </div>
          </div>

          <!-- 文档工具 -->
          <div class="card bg-gradient-to-br from-info/10 to-info/5 border border-info/20">
            <div class="card-body p-4 text-center">
              <div class="flex items-center justify-center mb-2">
                <FileText class="h-8 w-8 text-info" />
              </div>
              <div class="text-2xl font-bold text-info">{{ stats.with_docs.toLocaleString() }}</div>
              <div class="text-sm text-base-content/70 mt-1">文档工具</div>
            </div>
          </div>

          <!-- 最近更新 -->
          <div class="card bg-gradient-to-br from-warning/10 to-warning/5 border border-warning/20">
            <div class="card-body p-4 text-center">
              <div class="flex items-center justify-center mb-2">
                <Clock class="h-8 w-8 text-warning" />
              </div>
              <div class="text-2xl font-bold text-warning">{{ stats.recently_updated.toLocaleString() }}</div>
              <div class="text-sm text-base-content/70 mt-1">最近更新</div>
            </div>
          </div>
        </div>

        <!-- 统计数据加载状态 -->
        <div v-else-if="statsLoading" class="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div v-for="i in 4" :key="i" class="card bg-base-200 animate-pulse">
            <div class="card-body p-4 text-center">
              <div class="h-8 bg-base-300 rounded mb-2"></div>
              <div class="h-4 bg-base-300 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具列表 -->
      <div v-if="loading" class="flex justify-center py-12">
        <span class="loading loading-spinner loading-lg"></span>
      </div>

      <div v-else-if="tools.length > 0" class="space-y-4">
        <!-- 工具卡片 -->
        <div v-for="tool in tools" :key="tool.id"
          class="card bg-base-100 border border-gray-200/20 hover:shadow-md transition-shadow duration-200">
          <div class="card-body p-4 sm:p-6">
            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
              <!-- 工具信息 -->
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-3">
                  <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Wrench class="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-base-content">{{ tool.name }}</h3>
                    <div class="flex items-center gap-2 mt-1">
                      <span v-if="tool.file" class="badge badge-success badge-sm">可下载</span>
                      <span class="text-sm text-base-content/60 flex items-center gap-1">
                        <Eye class="h-3 w-3" />
                        {{ tool.view_count.toLocaleString() }}
                      </span>
                      <span class="text-sm text-base-content/60">
                        {{ formatDate(tool.updated_at) }}
                      </span>
                    </div>
                  </div>
                </div>

                <p class="text-base-content/70 mb-3 line-clamp-3">
                  {{ getToolDescription(tool.description || tool.content) }}
                </p>
              </div>

              <!-- 操作按钮 -->
              <div class="flex items-center gap-2">
                <a :href="`/tools/${tool.id}`" class="btn btn-outline btn-sm">
                  <Eye class="h-4 w-4 mr-1" />
                  查看详情
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <Pagination v-if="!loading && pagination.total > pagination.pageSize" :current-page="pagination.page"
          :total-pages="Math.ceil(pagination.total / pagination.pageSize)" :total-items="pagination.total"
          :show-info="true" :use-events="true" @page-change="changePage" />
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <Wrench class="h-16 w-16 mx-auto text-base-content/30 mb-4" />
        <h3 class="text-lg font-medium text-base-content/70 mb-2">暂无应急工具</h3>
        <p class="text-base-content/50">
          系统中暂无应急工具数据
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Wrench,
  Eye,
  Package,
  Download,
  FileText,
  Clock
} from 'lucide-vue-next'
import Pagination from '@/components/ui/Pagination.vue'
import type { Tool } from '@/types/api'
import { toolsApi } from '@/lib/api'
import { useToast } from '@/composables/useToast'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const tools = ref<Tool[]>([])
const statsLoading = ref(false)
const stats = ref<{
  total: number;
  with_files: number;
  with_docs: number;
  recently_updated: number;
} | null>(null)

const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// Toast功能
const { showError } = useToast()

// 方法
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const getToolDescription = (content: string) => {
  if (!content) return '暂无描述'

  // 移除HTML标签并截取前200个字符
  const plainText = content.replace(/<[^>]*>/g, '')
  return plainText.length > 200 ? plainText.substring(0, 200) + '...' : plainText
}

const loadTools = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.page,
      page_size: pagination.pageSize
    }

    const response = await toolsApi.getList(params)
    tools.value = response.list || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('加载工具列表失败:', error)
    showError('加载工具列表失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

const changePage = (page: number) => {
  pagination.page = page
  loadTools()
}

const loadStats = async () => {
  statsLoading.value = true
  try {
    const statsData = await toolsApi.getStats()
    stats.value = statsData
    console.log('工具统计数据:', statsData)
  } catch (error) {
    console.error('加载统计数据失败:', error)
    showError('加载统计数据失败，请稍后再试')
  } finally {
    statsLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadTools()
  loadStats()
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
