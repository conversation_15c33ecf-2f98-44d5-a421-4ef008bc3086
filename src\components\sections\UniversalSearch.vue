<template>
  <section id="search-center"
    class="py-16 bg-gradient-to-br from-base-200/30 to-base-300/30 scroll-mt-20 transition-all duration-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 搜索区域标题 -->
      <div class="text-center mb-12">
        <div class="flex items-center justify-center gap-3 mb-4">
          <h2 class="text-2xl sm:text-3xl font-bold text-base-content">智能搜索中心</h2>
        </div>
        <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
          搜索威胁情报、安全事件、勒索组织和安全博客，获取最新的网络安全信息
        </p>
      </div>

      <!-- 搜索框 -->
      <div class="max-w-7xl mx-auto mb-8">
        <div class="card bg-base-100 shadow-xl border border-base-300/20">
          <div class="card-body p-6">
            <div class="relative">
              <div
                class="input input-lg flex items-center border-2 border-gray-300 focus-within:border-primary focus-within:shadow-lg focus-within:shadow-primary/20 focus-within:-translate-y-0.5 transition-all duration-200">
                <Search class="h-5 w-5 text-base-content/60 mr-3" />
                <input v-model="searchQuery" type="text" placeholder="搜索威胁情报、安全事件、勒索组织、安全博客..."
                  class="grow bg-transparent border-none outline-none text-base placeholder:text-base-content/50 focus:outline-none"
                  @keyup.enter="performSearch"
                  @focus="showSuggestions = true"
                  @blur="hideSuggestions" />
                <button v-if="searchQuery"
                  class="btn btn-ghost btn-sm btn-circle ml-2 hover:bg-error/10 hover:text-error" @click="clearSearch">
                  <X class="h-4 w-4" />
                </button>
                <button class="btn btn-primary btn-sm ml-2 shadow-md hover:shadow-lg transition-shadow"
                  @click="performSearch">
                  搜索
                </button>
              </div>

              <!-- 搜索建议 -->
              <SearchSuggestions
                :query="searchQuery"
                :show="showSuggestions"
                @select="selectSuggestion"
                @close="showSuggestions = false"
              />
            </div>

            <!-- 快捷搜索标签 -->
            <div class="flex flex-wrap gap-2 justify-center mt-6">
              <span class="text-sm text-base-content/70 mr-2 font-medium">热门搜索:</span>
              <span v-for="tag in quickSearchTags" :key="tag"
                class="badge badge-outline cursor-pointer hover:badge-primary hover:scale-105 hover:bg-primary hover:text-primary-content hover:border-primary transition-all duration-200 text-xs"
                @click="quickSearch(tag)">
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>

    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Search,
  X
} from 'lucide-vue-next'
import SearchSuggestions from '@/components/ui/SearchSuggestions.vue'

// Props
interface Props {
  initialQuery?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialQuery: ''
})

// 搜索相关的响应式数据
const searchQuery = ref(props.initialQuery)
const showSuggestions = ref(false)

// 快捷搜索标签
const quickSearchTags = ref([
  'LockBit',
  'APT29',
  'Conti',
  '勒索软件',
  '数据泄露',
  '网络钓鱼',
  '制造业',
  '医疗',
  '金融',
  '政府'
])



// 执行搜索 - 跳转到搜索页面
const performSearch = () => {
  if (searchQuery.value.trim()) {
    // 跳转到搜索页面，传递查询参数
    window.location.href = `/search?q=${encodeURIComponent(searchQuery.value.trim())}`
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
}

// 选择搜索建议
const selectSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  showSuggestions.value = false
  performSearch()
}

// 隐藏搜索建议（延迟以允许点击建议）
const hideSuggestions = () => {
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

// 快捷搜索
const quickSearch = (tag: string) => {
  searchQuery.value = tag
  showSuggestions.value = false
  performSearch()
}
</script>
