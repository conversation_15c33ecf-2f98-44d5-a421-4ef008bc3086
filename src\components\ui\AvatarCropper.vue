<template>
  <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
    <div class="bg-base-100 rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="p-6 border-b border-base-content/10">
        <h3 class="text-lg font-semibold text-base-content">裁剪头像</h3>
        <p class="text-sm text-base-content/60 mt-1">调整图片大小和位置</p>
      </div>
      
      <div class="p-6">
        <div class="relative bg-base-200 rounded-lg overflow-hidden mb-4" style="height: 300px;">
          <img 
            v-if="imageSrc"
            ref="imageRef"
            :src="imageSrc" 
            alt="待裁剪图片"
            class="max-w-full max-h-full object-contain mx-auto"
            @load="initCropper"
          />
        </div>
        
        <!-- 裁剪预览 -->
        <div class="flex items-center justify-center mb-4">
          <div class="text-center">
            <p class="text-sm text-base-content/60 mb-2">预览</p>
            <div class="size-20 rounded-full overflow-hidden bg-base-200 mx-auto">
              <canvas 
                ref="previewCanvas"
                width="80" 
                height="80"
                class="w-full h-full"
              ></canvas>
            </div>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="flex gap-2 mb-4">
          <button 
            @click="rotate(-90)"
            class="btn btn-sm btn-outline flex-1"
            title="逆时针旋转"
          >
            <span class="icon-[tabler--rotate-2] size-4"></span>
          </button>
          <button 
            @click="rotate(90)"
            class="btn btn-sm btn-outline flex-1"
            title="顺时针旋转"
          >
            <span class="icon-[tabler--rotate-clockwise-2] size-4"></span>
          </button>
          <button 
            @click="flipX"
            class="btn btn-sm btn-outline flex-1"
            title="水平翻转"
          >
            <span class="icon-[tabler--flip-horizontal] size-4"></span>
          </button>
          <button 
            @click="flipY"
            class="btn btn-sm btn-outline flex-1"
            title="垂直翻转"
          >
            <span class="icon-[tabler--flip-vertical] size-4"></span>
          </button>
        </div>

        <!-- 缩放控制 -->
        <div class="mb-6">
          <label class="label-text text-sm mb-2 block">缩放</label>
          <input
            type="range"
            min="0.1"
            max="3"
            step="0.1"
            v-model.number="scale"
            @input="updateScale"
            @change="updateScale"
            class="range range-primary w-full"
          />
          <div class="flex justify-between text-xs text-base-content/60 mt-1">
            <span>10%</span>
            <span>{{ Math.round(scale * 100) }}%</span>
            <span>300%</span>
          </div>
        </div>
      </div>

      <div class="p-6 border-t border-base-content/10 flex gap-3 justify-end">
        <button 
          @click="cancel"
          class="btn btn-outline"
        >
          取消
        </button>
        <button 
          @click="confirm"
          class="btn btn-primary"
          :disabled="!imageSrc"
        >
          <span class="icon-[tabler--check] size-4"></span>
          确认
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

interface Props {
  show: boolean
  imageSrc: string
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'confirm', croppedImage: string): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const imageRef = ref<HTMLImageElement>()
const previewCanvas = ref<HTMLCanvasElement>()
const scale = ref(1)
const rotation = ref(0)
const flipHorizontal = ref(false)
const flipVertical = ref(false)

// 图片变换参数
const transform = ref({
  x: 0,
  y: 0,
  scale: 1,
  rotation: 0,
  flipX: false,
  flipY: false
})

const initCropper = async () => {
  await nextTick()
  // 确保图片已加载完成
  if (imageRef.value?.complete) {
    updatePreview()
  }
}

const updateScale = () => {
  // 确保缩放值是数字类型
  const scaleValue = typeof scale.value === 'string' ? parseFloat(scale.value) : scale.value
  transform.value.scale = scaleValue
  updatePreview()
}

const rotate = (angle: number) => {
  rotation.value += angle
  transform.value.rotation = rotation.value
  updatePreview()
}

const flipX = () => {
  flipHorizontal.value = !flipHorizontal.value
  transform.value.flipX = flipHorizontal.value
  updatePreview()
}

const flipY = () => {
  flipVertical.value = !flipVertical.value
  transform.value.flipY = flipVertical.value
  updatePreview()
}

const updatePreview = () => {
  if (!imageRef.value || !previewCanvas.value) return

  const canvas = previewCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  const img = imageRef.value
  const size = 80

  // 清除画布
  ctx.clearRect(0, 0, size, size)

  // 保存当前状态
  ctx.save()

  // 移动到中心点
  ctx.translate(size / 2, size / 2)

  // 应用旋转
  ctx.rotate((transform.value.rotation * Math.PI) / 180)

  // 应用翻转
  if (transform.value.flipX) ctx.scale(-1, 1)
  if (transform.value.flipY) ctx.scale(1, -1)

  // 计算图片绘制尺寸（应用缩放）
  const imgSize = Math.min(img.naturalWidth, img.naturalHeight)
  const drawSize = size * transform.value.scale

  // 绘制图片
  ctx.drawImage(
    img,
    (img.naturalWidth - imgSize) / 2,
    (img.naturalHeight - imgSize) / 2,
    imgSize,
    imgSize,
    -drawSize / 2,
    -drawSize / 2,
    drawSize,
    drawSize
  )

  // 恢复状态
  ctx.restore()
}

const getCroppedImage = (): string => {
  if (!imageRef.value || !previewCanvas.value) return ''

  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) return ''

  const size = 200 // 输出尺寸
  canvas.width = size
  canvas.height = size

  const img = imageRef.value

  // 保存当前状态
  ctx.save()

  // 移动到中心点
  ctx.translate(size / 2, size / 2)

  // 应用旋转
  ctx.rotate((transform.value.rotation * Math.PI) / 180)

  // 应用翻转
  if (transform.value.flipX) ctx.scale(-1, 1)
  if (transform.value.flipY) ctx.scale(1, -1)

  // 计算图片绘制尺寸（应用缩放）
  const imgSize = Math.min(img.naturalWidth, img.naturalHeight)
  const drawSize = size * transform.value.scale

  // 绘制图片
  ctx.drawImage(
    img,
    (img.naturalWidth - imgSize) / 2,
    (img.naturalHeight - imgSize) / 2,
    imgSize,
    imgSize,
    -drawSize / 2,
    -drawSize / 2,
    drawSize,
    drawSize
  )

  // 恢复状态
  ctx.restore()

  return canvas.toDataURL('image/png', 0.9)
}

const confirm = () => {
  const croppedImage = getCroppedImage()
  emit('confirm', croppedImage)
  resetTransform()
}

const cancel = () => {
  emit('cancel')
  resetTransform()
}

const resetTransform = () => {
  scale.value = 1
  rotation.value = 0
  flipHorizontal.value = false
  flipVertical.value = false
  transform.value = {
    x: 0,
    y: 0,
    scale: 1,
    rotation: 0,
    flipX: false,
    flipY: false
  }
}

// 监听显示状态变化
watch(() => props.show, (newVal) => {
  if (newVal && props.imageSrc) {
    nextTick(() => {
      resetTransform()
      // 延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        updatePreview()
      }, 100)
    })
  }
})

// 监听图片变化
watch(() => props.imageSrc, () => {
  if (props.imageSrc) {
    nextTick(() => {
      resetTransform()
    })
  }
})

// 监听缩放变化
watch(scale, (newScale) => {
  transform.value.scale = newScale
  updatePreview()
})
</script>
