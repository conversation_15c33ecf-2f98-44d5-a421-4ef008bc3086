<template>
  <nav :class="`mb-6 ${className}`">
    <div class="breadcrumbs text-sm">
      <ul>
        <template v-for="(item, index) in items" :key="index">
          <li>
            <span
              v-if="item.current"
              aria-current="page"
              class="text-base-content/70 truncate max-w-xs"
            >
              {{ item.label }}
            </span>
            <a
              v-else
              :href="item.href"
              class="text-primary hover:text-primary/80"
            >
              {{ item.label }}
            </a>
          </li>
          <li
            v-if="index < items.length - 1"
            class="breadcrumbs-separator rtl:-rotate-[40deg]"
          >
            /
          </li>
        </template>
      </ul>
    </div>
  </nav>
</template>

<script setup lang="ts">
export interface BreadcrumbItem {
  label: string
  href?: string
  current?: boolean
}

interface Props {
  items: BreadcrumbItem[]
  className?: string
}

withDefaults(defineProps<Props>(), {
  className: ''
})
</script>
