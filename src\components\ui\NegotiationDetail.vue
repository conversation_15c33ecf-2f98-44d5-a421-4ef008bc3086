<template>
  <div ref="negotiationsContainer" class="space-y-6">
    <!-- 谈判记录概览 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-3">
        <!-- 返回按钮 -->
        <button v-if="selectedRecord" @click="selectedRecord = null"
          class="btn btn-sm btn-circle btn-ghost">
          <ArrowLeft class="h-4 w-4" />
        </button>
        <h3 class="text-lg font-semibold">
          {{ selectedRecord ? `谈判记录 #${selectedRecord.id}` : '谈判记录' }}
        </h3>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg text-primary"></span>
    </div>

    <!-- 谈判记录列表 -->
    <div v-else-if="!selectedRecord && negotiationRecords.length > 0" class="space-y-4">
      <div v-for="record in negotiationRecords" :key="record.id"
        class="card bg-base-100 border border-gray-200/20 hover:border-gray-300/30 transition-colors cursor-pointer"
        @click="selectRecord(record)">
        <!-- 谈判记录头部 -->
        <div class="card-body p-6">
          <div class="flex items-start gap-4">
            <!-- 状态图标 -->
            <div class="w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0"
              :class="getStatusIconClass(record.paid)">
              <MessageSquare class="h-6 w-6" />
            </div>

            <!-- 谈判信息 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-2">
                <h4 class="font-semibold text-base truncate">谈判记录 #{{ record.id }}</h4>
                <span class="badge badge-sm" :class="getStatusBadgeClass(record.paid)">
                  {{ record.paid ? '已支付' : '未支付' }}
                </span>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-base-content/70">
                <div class="space-y-1">
                  <div class="flex items-center gap-2">
                    <Building class="h-4 w-4" />
                    <span>勒索组织: {{ record.group_name || '未知' }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <DollarSign class="h-4 w-4" />
                    <span>初始赎金: {{ record.initialransom || '未知' }}</span>
                  </div>
                </div>
                <div class="space-y-1">
                  <div class="flex items-center gap-2">
                    <MessageCircle class="h-4 w-4" />
                    <span>消息数量: {{ record.message_count }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <DollarSign class="h-4 w-4" />
                    <span>协商赎金: {{ record.negotiatedransom || '未知' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 进入图标 -->
            <div class="flex items-center">
              <ChevronRight class="h-5 w-5 text-base-content/50" />
            </div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <Pagination
        v-if="pagination.total_count > pageSize"
        :current-page="currentPage"
        :total-pages="totalPages"
        :total-items="pagination.total_count"
        :show-info="true"
        :use-events="true"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="!selectedRecord && !loading && negotiationRecords.length === 0" class="text-center py-12">
      <MessageSquare class="h-16 w-16 mx-auto text-base-content/30 mb-4" />
      <h3 class="text-lg font-medium text-base-content/70 mb-2">暂无谈判记录</h3>
      <p class="text-base-content/50">
        该组织暂无已知的谈判记录
      </p>
    </div>

    <!-- 简化谈判记录详情视图 -->
    <div v-if="selectedRecord" class="space-y-6">
      <!-- 谈判摘要 -->
      <div class="p-4 bg-base-200/30 rounded-lg border border-gray-200/20">
        <h5 class="font-medium mb-2 text-sm text-base-content/80">谈判概览</h5>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div class="flex items-center gap-2">
            <Shield class="h-4 w-4 text-primary" />
            <span class="text-base-content/60">组织:</span>
            <span class="font-medium">{{ selectedRecord.group_name || '未知' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <MessageCircle class="h-4 w-4 text-info" />
            <span class="text-base-content/60">聊天ID:</span>
            <span class="font-medium">{{ selectedRecord?.id }}</span>
          </div>
          <div class="flex items-center gap-2">
            <DollarSign class="h-4 w-4 text-warning" />
            <span class="text-base-content/60">初始赎金:</span>
            <span class="font-medium">{{ selectedRecord.initialransom || '未知' }}</span>
          </div>
          <div class="flex items-center gap-2">
            <DollarSign class="h-4 w-4 text-success" />
            <span class="text-base-content/60">协商赎金:</span>
            <span class="font-medium">{{ selectedRecord.negotiatedransom || '未知' }}</span>
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm mt-3">
          <div class="flex items-center gap-2">
            <HandCoins class="h-4 w-4 text-purple-500" />
            <span class="text-base-content/60">支付状态:</span>
            <span class="badge" :class="selectedRecord.paid ? 'badge-success' : 'badge-error'">
              {{ selectedRecord.paid ? '已支付' : '未支付' }}
            </span>
          </div>
          <div class="flex items-center gap-2">
            <MessageSquare class="h-4 w-4 text-base-content/50" />
            <span class="text-base-content/60">消息总数:</span>
            <span class="font-medium">{{ selectedRecord.message_count }}</span>
          </div>
        </div>
      </div>

      <!-- 聊天对话界面 -->
      <div class="bg-base-100 border border-gray-200/20 rounded-lg">
        <!-- 聊天头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200/20 bg-base-200/30">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-warning/10 rounded-full flex items-center justify-center">
              <Shield class="h-4 w-4 text-warning" />
            </div>
            <div>
              <div class="font-medium text-sm">{{ selectedRecord.group_name || '未知组织' }} 谈判记录</div>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="p-3 sm:p-4 space-y-3 sm:space-y-4">
          <div v-for="(message, index) in selectedRecord.messages" :key="index" class="flex gap-2 sm:gap-3"
            :class="{ 'flex-row-reverse': message.party === 'Victim' }">
            <!-- 头像 -->
            <div class="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center flex-shrink-0"
              :class="message.party === 'Victim' ? 'bg-info/10' : 'bg-error/10'">
              <User class="h-3 w-3 sm:h-4 sm:w-4" :class="message.party === 'Victim' ? 'text-info' : 'text-error'" />
            </div>

            <!-- 消息气泡 -->
            <div class="flex-1 max-w-[280px] sm:max-w-xs md:max-w-md">
              <!-- 发送者信息 -->
              <div class="flex items-center gap-1 sm:gap-2 mb-1" :class="{ 'justify-end': message.party === 'Victim' }">
                <span class="text-xs font-medium text-base-content/70 truncate">
                  {{ message.party }}
                </span>
                <span v-if="message.timestamp" class="text-xs text-base-content/50 flex-shrink-0">
                  {{ message.timestamp || '时间未知' }}
                </span>
              </div>

              <!-- 消息内容 -->
              <div class="p-2 sm:p-3 rounded-lg border border-gray-200/20" :class="message.party === 'Victim'
                ? 'bg-info/5 border-info/20'
                : 'bg-error/5 border-error/20'">

                <!-- 文本内容 -->
                <div class="text-xs sm:text-sm text-base-content/80 leading-relaxed whitespace-pre-wrap break-words">
                  {{ message.content }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 谈判结果 -->
        <div class="p-4 border-t border-gray-200/20 bg-base-200/20">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium text-base-content/80">谈判结果:</span>
              <span class="badge" :class="selectedRecord.paid ? 'badge-success' : 'badge-error'">
                {{ selectedRecord.paid ? '支付成功' : '拒绝支付' }}
              </span>
            </div>
            <div class="text-sm text-base-content/70">
              最终金额: {{ selectedRecord.paid ? selectedRecord.negotiatedransom : '' }}
            </div>
          </div>
        </div>
      </div>
    </div>



  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  MessageSquare,
  Building,
  DollarSign,
  MessageCircle,
  Shield,
  User,
  ArrowLeft,
  ChevronRight,
  HandCoins
} from 'lucide-vue-next'
import type { NegotiationRecord } from '@/types/api'
import { ransomwareGroupApi } from '@/lib/api/security'
import { useToast } from '@/composables/useToast'
import Pagination from '@/components/ui/Pagination.vue'
import { onMounted } from 'vue'

interface Props {
  groupSlug: string
}

const props = defineProps<Props>()

// Toast功能
const { showError } = useToast()

// 模板引用
const negotiationsContainer = ref<HTMLElement>()

// 选中的谈判记录
const selectedRecord = ref<NegotiationRecord | null>(null)

// 分页相关状态
const currentPage = ref(1)
const pageSize = ref(10) // 每页显示10个谈判记录
const loading = ref(false)

// 谈判记录数据状态
const negotiationRecords = ref<NegotiationRecord[]>([])
const pagination = ref({
  current_page: 1,
  page_size: 10,
  total_count: 0,
  total_pages: 0,
  has_next: false,
  has_prev: false
})

// 计算属性
const totalPages = computed(() => pagination.value.total_pages)

// 获取谈判记录数据
const loadNegotiations = async (page: number = 1) => {
  try {
    loading.value = true
    const response = await ransomwareGroupApi.getNegotiations(props.groupSlug, {
      page,
      page_size: pageSize.value
    })

    negotiationRecords.value = response.negotiation_records
    pagination.value = response.pagination
    currentPage.value = page
  } catch (error: any) {
    showError(error || '加载谈判记录失败')
    console.error('加载谈判记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 滚动到谈判记录列表顶部
const scrollToTop = () => {
  if (negotiationsContainer.value) {
    negotiationsContainer.value.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 分页控制方法
const handlePageChange = async (page: number) => {
  await loadNegotiations(page)
  // 翻页后滚动到列表顶部
  setTimeout(() => {
    scrollToTop()
  }, 100) // 稍微延迟确保数据已渲染
}

// 选择谈判记录
const selectRecord = (record: NegotiationRecord) => {
  selectedRecord.value = record
}

// 获取状态图标样式
const getStatusIconClass = (paid: boolean) => {
  return paid ? 'bg-success/10 text-success' : 'bg-warning/10 text-warning'
}

// 获取状态徽章样式
const getStatusBadgeClass = (paid: boolean) => {
  return paid ? 'badge-success' : 'badge-warning'
}

// 组件挂载时加载数据
onMounted(() => {
  loadNegotiations(1)
})


</script>
