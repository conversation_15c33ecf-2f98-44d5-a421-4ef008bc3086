<template>
  <div v-if="totalPages > 1" class="flex justify-center mt-12">
    <nav class="flex items-center gap-x-1">
      <!-- 上一页按钮 -->
      <component
        :is="useEvents ? 'button' : 'a'"
        v-if="currentPage > 1"
        :href="useEvents ? undefined : getPageUrl(currentPage - 1)"
        :class="[
          'btn btn-soft max-sm:btn-square transition-all duration-200 hover:scale-105'
        ]"
        @click="useEvents ? handlePageClick(currentPage - 1) : undefined"
      >
        <ChevronLeft class="h-5 w-5 sm:hidden" />
        <span class="hidden sm:inline">{{ prevText }}</span>
      </component>
      <span
        v-else
        :class="[
          'btn btn-soft max-sm:btn-square btn-disabled transition-all duration-200'
        ]"
      >
        <ChevronLeft class="h-5 w-5 sm:hidden" />
        <span class="hidden sm:inline">{{ prevText }}</span>
      </span>

      <!-- 页码按钮 -->
      <div class="flex items-center gap-x-1">
        <template v-for="page in visiblePages" :key="page">
          <!-- 省略号 -->
          <span v-if="page === '...'" class="px-3 py-2 text-base-content/50">
            ...
          </span>
          <!-- 当前页码（不可点击） -->
          <span
            v-else-if="page === currentPage"
            :class="[
              'btn btn-soft btn-square btn-primary transition-all duration-200'
            ]"
            :aria-current="'page'"
          >
            {{ page }}
          </span>
          <!-- 其他页码（可点击链接或按钮） -->
          <component
            v-else
            :is="useEvents ? 'button' : 'a'"
            :href="useEvents ? undefined : getPageUrl(page as number)"
            :class="[
              'btn btn-soft btn-square transition-all duration-200 hover:scale-105'
            ]"
            @click="useEvents ? handlePageClick(page as number) : undefined"
          >
            {{ page }}
          </component>
        </template>
      </div>

      <!-- 下一页按钮 -->
      <component
        :is="useEvents ? 'button' : 'a'"
        v-if="currentPage < totalPages"
        :href="useEvents ? undefined : getPageUrl(currentPage + 1)"
        :class="[
          'btn btn-soft max-sm:btn-square transition-all duration-200 hover:scale-105'
        ]"
        @click="useEvents ? handlePageClick(currentPage + 1) : undefined"
      >
        <span class="hidden sm:inline">{{ nextText }}</span>
        <ChevronRight class="h-5 w-5 sm:hidden" />
      </component>
      <span
        v-else
        :class="[
          'btn btn-soft max-sm:btn-square btn-disabled transition-all duration-200'
        ]"
      >
        <span class="hidden sm:inline">{{ nextText }}</span>
        <ChevronRight class="h-5 w-5 sm:hidden" />
      </span>
    </nav>
  </div>

  <!-- 分页信息 -->
  <div v-if="showInfo && totalPages > 1" class="flex justify-center mt-4">
    <p class="text-sm text-base-content/70">
      第 {{ currentPage }} 页，共 {{ totalPages }} 页
      <span v-if="totalItems">（共 {{ totalItems }} 条记录）</span>
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'

// Props 定义
interface Props {
  /** 当前页码 */
  currentPage: number
  /** 总页数 */
  totalPages: number
  /** 总记录数（可选，用于显示信息） */
  totalItems?: number
  /** 是否显示分页信息 */
  showInfo?: boolean
  /** 上一页按钮文本 */
  prevText?: string
  /** 下一页按钮文本 */
  nextText?: string
  /** 最大显示页码数量 */
  maxVisiblePages?: number
  /** 基础URL路径（不包含查询参数） */
  basePath?: string
  /** 是否使用事件模式而不是URL导航 */
  useEvents?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showInfo: false,
  prevText: '上一页',
  nextText: '下一页',
  maxVisiblePages: 7,
  basePath: '',
  useEvents: false
})

// 事件定义
const emit = defineEmits<{
  'page-change': [page: number]
}>()

// 处理页面点击事件
const handlePageClick = (page: number) => {
  if (page !== props.currentPage) {
    emit('page-change', page)
  }
}



// 计算可见页码
const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const total = props.totalPages
  const current = props.currentPage
  const maxVisible = props.maxVisiblePages

  if (total <= maxVisible) {
    // 总页数小于等于最大显示数，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总页数大于最大显示数，需要省略
    if (current <= Math.floor(maxVisible / 2) + 1) {
      // 当前页在前半部分
      for (let i = 1; i <= maxVisible - 2; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    } else if (current >= total - Math.floor(maxVisible / 2)) {
      // 当前页在后半部分
      pages.push(1, '...')
      for (let i = total - (maxVisible - 3); i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间部分
      pages.push(1, '...')
      const halfVisible = Math.floor((maxVisible - 4) / 2)
      for (let i = current - halfVisible; i <= current + halfVisible; i++) {
        pages.push(i)
      }
      pages.push('...', total)
    }
  }

  return pages
})

// 获取当前URL的查询参数
const getCurrentUrlParams = () => {
  if (typeof window === 'undefined') return new URLSearchParams()
  return new URLSearchParams(window.location.search)
}

// 生成页面URL
const getPageUrl = (page: number) => {
  const params = getCurrentUrlParams()

  if (page === 1) {
    // 第一页时移除page参数
    params.delete('page')
  } else {
    params.set('page', page.toString())
  }

  const basePath = props.basePath || (typeof window !== 'undefined' ? window.location.pathname : '')
  const queryString = params.toString()

  return queryString ? `${basePath}?${queryString}` : basePath
}
</script>
