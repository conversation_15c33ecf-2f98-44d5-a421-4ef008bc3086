<template>
  <div class="bg-base-100 py-8">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-6">
        <h3 class="text-lg font-semibold text-base-content mb-2">
          热门搜索
        </h3>
        <p class="text-sm text-base-content/70">
          查看其他用户正在搜索的热门内容
        </p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center">
        <div class="loading loading-spinner loading-md text-primary"></div>
      </div>

      <!-- 热门搜索列表 -->
      <div v-else-if="popularSearches.length > 0" class="flex flex-wrap justify-center gap-2">
        <button
          v-for="(search, index) in popularSearches"
          :key="index"
          class="badge badge-outline hover:badge-primary transition-colors duration-200 cursor-pointer text-sm py-2 px-3"
          @click="selectSearch(search.query)"
        >
          <span>{{ search.query }}</span>
          <span class="ml-2 text-xs opacity-70">({{ search.search_count }})</span>
        </button>
      </div>

      <!-- 无数据 -->
      <div v-else class="text-center text-sm text-base-content/70">
        暂无热门搜索数据
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { searchApi } from '@/lib/api/system'
import type { PopularSearch } from '@/lib/api/types'

// Emits
const emit = defineEmits<{
  select: [query: string]
}>()

// 响应式数据
const popularSearches = ref<PopularSearch[]>([])
const loading = ref(false)

// 获取热门搜索
const fetchPopularSearches = async () => {
  loading.value = true
  try {
    const response = await searchApi.popular(10)
    popularSearches.value = response
  } catch (error) {
    console.error('Failed to fetch popular searches:', error)
    popularSearches.value = []
  } finally {
    loading.value = false
  }
}

// 选择搜索
const selectSearch = (query: string) => {
  emit('select', query)

  // 发出自定义DOM事件供页面监听
  if (typeof window !== 'undefined') {
    const event = new CustomEvent('popular-search-select', { detail: query })
    document.dispatchEvent(event)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchPopularSearches()
})
</script>
