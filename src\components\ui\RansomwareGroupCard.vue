<template>
  <a :href="getGroupUrl(group)"
    class="card group relative overflow-hidden border border-gray-200/20 hover:border-gray-300/40 bg-gradient-to-br from-base-200 via-base-200 to-base-200/80 hover:from-base-100 hover:via-base-100 hover:to-primary/5 transition-all duration-500 cursor-pointer hover:scale-[1.02] hover:shadow-xl hover:shadow-primary/10 block"
    @click="handleClick">
    <!-- 背景装饰 -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
    </div>
    <div
      class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
    </div>

    <div class="card-body relative p-6">
      <!-- 头部信息 -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-3 flex-1">
          <div class="relative">
            <!-- 如果有logo则显示logo，否则显示图标 -->
            <div v-if="group.logo"
              class="w-12 h-12 rounded-xl overflow-hidden bg-gradient-to-br from-primary/10 to-primary/5 group-hover:from-primary/20 group-hover:to-primary/10 transition-all duration-300 flex items-center justify-center">
              <img :src="group.logo" :alt="group.name"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300" />
            </div>
            <div v-else
              class="p-3 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 group-hover:from-primary/20 group-hover:to-primary/10 transition-all duration-300">
              <component :is="getActivityIcon(group.name)"
                class="h-6 w-6 group-hover:scale-110 transition-transform duration-300"
                :class="getActivityIconColor(group.threat_level)" />
            </div>
          </div>
          <h4
            class="font-bold text-lg text-base-content group-hover:text-primary transition-colors duration-300 flex-1">
            {{ group.name }}
          </h4>
        </div>
        <span :class="[
          'badge text-xs group-hover:scale-105 transition-transform duration-300 ml-3',
          getThreatLevelBadgeClass(group.threat_level)
        ]">
          {{ getThreatLevelBadgeText(group.threat_level) }}
        </span>
      </div>

      <!-- 描述 -->
      <div class="h-10 mb-4">
        <p class="text-sm text-base-content/70 leading-relaxed line-clamp-2">
          {{ group.description }}
        </p>
      </div>

      <!-- 统计信息 -->
      <div class="space-y-3">
        <!-- 攻击目标 -->
        <div
          class="flex items-center justify-between p-3 rounded-lg bg-base-100/50 group-hover:bg-base-200/60 transition-colors duration-300">
          <div class="flex items-center gap-2">
            <div class="p-1.5 rounded-md bg-error/10">
              <Target class="h-4 w-4 text-error" />
            </div>
            <span class="text-sm font-medium text-base-content">攻击目标</span>
          </div>
          <div class="flex items-center gap-1">
            <span class="text-lg font-bold text-base-content">{{ group.victim_count || 0 }}</span>
            <span class="text-xs text-base-content/60">个</span>
          </div>
        </div>

        <!-- 最后活动 -->
        <div
          class="flex items-center justify-between p-3 rounded-lg bg-base-100/50 group-hover:bg-base-200/60 transition-colors duration-300">
          <div class="flex items-center gap-2">
            <div class="p-1.5 rounded-md bg-info/10">
              <Clock class="h-4 w-4 text-info" />
            </div>
            <span class="text-sm font-medium text-base-content">最后活动</span>
          </div>
          <span class="text-sm font-medium text-base-content/70">
            {{ formatLastActivity(group.last_activity) }}
          </span>
        </div>

        <!-- 威胁评分 -->
        <div
          class="flex items-center justify-between p-3 rounded-lg bg-base-100/50 group-hover:bg-base-200/60 transition-colors duration-300">
          <div class="flex items-center gap-2">
            <div class="p-1.5 rounded-md bg-warning/10">
              <TrendingUp class="h-4 w-4 text-warning" />
            </div>
            <span class="text-sm font-medium text-base-content">威胁评分</span>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-16 h-2 bg-base-300/60 rounded-full overflow-hidden">
              <div class="h-full rounded-full transition-all duration-500"
                :class="getThreatScoreColor(group.threat_level)"
                :style="{ width: getThreatScore(group.threat_level) + '%' }"></div>
            </div>
            <span class="text-sm font-bold text-base-content">{{ getThreatScore(group.threat_level) }}</span>
          </div>
        </div>
      </div>

      <!-- 标签 -->
      <div class="flex flex-wrap gap-2 mt-4">
        <span v-for="tag in getGroupTags(group)" :key="tag"
          class="badge badge-outline badge-sm group-hover:badge-primary transition-colors duration-300">
          {{ tag }}
        </span>
      </div>

      <!-- 底部装饰线 -->
      <div
        class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-primary/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">

import {
  Skull,
  Target,
  Clock,
  TrendingUp,
  Lock,
  Cat,
  Zap,
  Crown,
  Eye,
  Play
} from 'lucide-vue-next'
import type { RansomwareGroup } from '@/types/api'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn';
import relativeTime from 'dayjs/plugin/relativeTime'
dayjs.extend(relativeTime); // 相对时间
dayjs.locale('zh-cn')

const props = defineProps<{
  group: RansomwareGroup
}>()

const emit = defineEmits<{
  click: [group: RansomwareGroup]
}>()

// 获取组织详情页面URL
const getGroupUrl = (group: RansomwareGroup) => {
  return `/ransomware/${group.slug}`
}

// 处理点击事件（保留用于向父组件发出事件）
const handleClick = () => {
  // 发出点击事件给父组件
  emit('click', props.group)
}

// 格式化最后活动时间
const formatLastActivity = (lastActivity: string | null) => {
  if (!lastActivity) return '未知'
  return dayjs(lastActivity).fromNow()
}

// 获取组织图标（根据组织名称）
const getActivityIcon = (groupName: string) => {
  switch (groupName) {
    case 'LockBit': return Lock
    case 'BlackCat (ALPHV)': return Cat
    case 'Cl0p': return Zap
    case 'Royal': return Crown
    case 'BianLian': return Eye
    case 'Play': return Play
    default: return Skull
  }
}

// 获取活动图标颜色
const getActivityIconColor = (level: string) => {
  switch (level) {
    case 'critical': return 'text-red-500'
    case 'high': return 'text-orange-500'
    case 'medium': return 'text-yellow-500'
    default: return 'text-green-500'
  }
}

// 获取威胁等级Badge类
const getThreatLevelBadgeClass = (level: string) => {
  switch (level) {
    case 'critical': return 'badge-error'
    case 'high': return 'badge-warning'
    case 'medium': return 'badge-info'
    case 'low': return 'badge-success'
    default: return 'badge-secondary'
  }
}

// 获取威胁等级Badge文本
const getThreatLevelBadgeText = (level: string) => {
  switch (level) {
    case 'critical': return '极高'
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

// 获取威胁评分
const getThreatScore = (level: string) => {
  switch (level) {
    case 'critical': return 95
    case 'high': return 75
    case 'medium': return 55
    case 'low': return 35
    default: return 15
  }
}

// 获取威胁评分颜色
const getThreatScoreColor = (level: string) => {
  switch (level) {
    case 'critical': return 'bg-red-500'
    case 'high': return 'bg-orange-500'
    case 'medium': return 'bg-yellow-500'
    default: return 'bg-green-500'
  }
}

// 获取组织标签
const getGroupTags = (group: RansomwareGroup) => {
  const tags = []

  // 检查是否为新的API数据结构
  const apiGroup = group as RansomwareGroup

  // 基于状态添加标签
  if (apiGroup.status === 'active') tags.push('活跃')
  if (apiGroup.status === 'inactive') tags.push('不活跃')
  if (apiGroup.status === 'disbanded') tags.push('已解散')
  if (apiGroup.status === 'unknown') tags.push('未知')

  // 基于威胁等级添加标签
  if (apiGroup.threat_level === 'critical') tags.push('极高威胁')
  if (apiGroup.threat_level === 'high') tags.push('高威胁')
  if (apiGroup.threat_level === 'medium') tags.push('中威胁')
  if (apiGroup.threat_level === 'low') tags.push('低威胁')

  // 基于攻击统计添加标签
  if (apiGroup.victim_count > 100) {
    tags.push('高频攻击')
  }

  // 通用标签
  if (apiGroup.name.includes('Lock')) tags.push('勒索软件')
  if (apiGroup.description.includes('RaaS')) tags.push('RaaS')

  return tags.slice(0, 3) // 最多显示3个标签
}
</script>

<style scoped>
/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 2.5rem;
  /* 固定高度，确保两行文字 */
  line-height: 1.25rem;
  /* 每行高度 */
}
</style>
