<template>
  <div class="flex flex-col lg:flex-row gap-4 mt-8">
    <!-- 上一个组织 -->
    <div v-if="previousGroup" class="flex-1">
      <a :href="`/ransomware/${previousGroup.slug}`"
        class="card group cursor-pointer transition-all duration-300 hover:shadow-lg border border-gray-200/20 hover:border-gray-300/40 bg-base-100 block">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <ChevronLeft
                class="h-5 w-5 text-base-content/60 group-hover:text-primary transition-colors duration-200" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-xs text-base-content/60 mb-1">上一个组织</div>
              <h4
                class="font-semibold text-base-content group-hover:text-primary transition-colors duration-200 truncate">
                {{ previousGroup.name }}
              </h4>
              <div class="flex items-center gap-2 mt-1">
                <span :class="getThreatLevelBadgeClass(previousGroup.threat_level)" class="badge-xs">
                  {{ getThreatLevelBadgeText(previousGroup.threat_level) }}
                </span>
                <span :class="getStatusBadgeClass(previousGroup.status)" class="badge-xs">
                  {{ getStatusBadgeText(previousGroup.status) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </a>
    </div>

    <!-- 占位符，当没有上一个组织时 -->
    <div v-else class="flex-1 hidden"></div>

    <!-- 下一个组织 -->
    <div v-if="nextGroup" class="flex-1">
      <a :href="`/ransomware/${nextGroup.slug}`"
        class="card group cursor-pointer transition-all duration-300 hover:shadow-lg border border-gray-200/20 hover:border-gray-300/40 bg-base-100 block">
        <div class="card-body p-4">
          <div class="flex items-center gap-3">
            <div class="flex-1 min-w-0 text-right lg:text-left">
              <div class="text-xs text-base-content/60 mb-1">下一个组织</div>
              <h4
                class="font-semibold text-base-content group-hover:text-primary transition-colors duration-200 truncate">
                {{ nextGroup.name }}
              </h4>
              <div class="flex items-center gap-2 mt-1 justify-end lg:justify-start">
                <span :class="getThreatLevelBadgeClass(nextGroup.threat_level)" class="badge-xs">
                  {{ getThreatLevelBadgeText(nextGroup.threat_level) }}
                </span>
                <span :class="getStatusBadgeClass(nextGroup.status)" class="badge-xs">
                  {{ getStatusBadgeText(nextGroup.status) }}
                </span>
              </div>
            </div>
            <div class="flex-shrink-0">
              <ChevronRight
                class="h-5 w-5 text-base-content/60 group-hover:text-primary transition-colors duration-200" />
            </div>
          </div>
        </div>
      </a>
    </div>

    <!-- 占位符，当没有下一个组织时 -->
    <div v-else class="flex-1 hidden"></div>
  </div>
</template>

<script setup lang="ts">
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'
import type { RansomwareGroup } from '@/types/api'

interface Props {
  previousGroup?: Pick<RansomwareGroup, 'name' | 'slug' | 'threat_level' | 'status'>
  nextGroup?: Pick<RansomwareGroup, 'name' | 'slug' | 'threat_level' | 'status'>
}

defineProps<Props>()

// 获取威胁等级徽章样式
const getThreatLevelBadgeClass = (level: string) => {
  switch (level) {
    case 'critical': return 'badge badge-error'
    case 'high': return 'badge badge-warning'
    case 'medium': return 'badge badge-info'
    case 'low': return 'badge badge-success'
    default: return 'badge badge-secondary'
  }
}

// 获取威胁等级徽章文本
const getThreatLevelBadgeText = (level: string) => {
  switch (level) {
    case 'critical': return '极高'
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return '未知'
  }
}

// 获取状态徽章样式
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'active': return 'badge badge-error badge-outline'
    case 'unknown': return 'badge badge-warning badge-outline'
    case 'inactive': return 'badge badge-info badge-outline'
    case 'disbanded': return 'badge badge-success badge-outline'
    default: return 'badge badge-secondary badge-outline'
  }
}

// 获取状态徽章文本
const getStatusBadgeText = (status: string) => {
  switch (status) {
    case 'active': return '活跃'
    case 'unknown': return '未知'
    case 'inactive': return '不活跃'
    case 'disbanded': return '已解散'
    default: return '状态未知'
  }
}
</script>
