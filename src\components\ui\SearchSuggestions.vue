<template>
  <div class="relative" v-if="showSuggestions">
    <div class="absolute top-full left-0 right-0 z-50 bg-base-100 border border-base-300 rounded-lg shadow-lg mt-1 max-h-64 overflow-y-auto">
      <!-- 加载状态 -->
      <div v-if="loading" class="p-4 text-center">
        <div class="loading loading-spinner loading-sm text-primary"></div>
        <span class="ml-2 text-sm text-base-content/70">获取建议中...</span>
      </div>

      <!-- 搜索建议列表 -->
      <div v-else-if="suggestions.length > 0" class="py-2">
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          class="px-4 py-2 hover:bg-base-200 cursor-pointer transition-colors duration-200 flex items-center justify-between"
          @click="selectSuggestion(suggestion.suggestion)"
        >
          <div class="flex items-center gap-2">
            <Search class="h-4 w-4 text-base-content/60" />
            <span class="text-sm">{{ suggestion.suggestion }}</span>
          </div>
          <div class="flex items-center gap-2">
            <span class="badge badge-sm badge-outline">{{ suggestion.count }} 结果</span>
            <span class="text-xs text-base-content/50">{{ getSuggestionTypeLabel(suggestion.type) }}</span>
          </div>
        </div>
      </div>

      <!-- 无建议 -->
      <div v-else class="p-4 text-center text-sm text-base-content/70">
        暂无搜索建议
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Search } from 'lucide-vue-next'
import { searchApi } from '@/lib/api/system'
import type { SearchSuggestion } from '@/lib/api/types'

// Props
interface Props {
  query: string
  show: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  select: [suggestion: string]
  close: []
}>()

// 响应式数据
const suggestions = ref<SearchSuggestion[]>([])
const loading = ref(false)

// 计算属性
const showSuggestions = computed(() => props.show && props.query.trim().length > 0)

// 获取建议类型标签
const getSuggestionTypeLabel = (type: string): string => {
  switch (type) {
    case 'history':
      return '历史搜索'
    default:
      return type
  }
}

// 获取搜索建议
const fetchSuggestions = async () => {
  if (!props.query.trim()) {
    suggestions.value = []
    return
  }

  loading.value = true
  try {
    const response = await searchApi.suggestions(props.query.trim(), 5)
    suggestions.value = response
  } catch (error) {
    console.error('Failed to fetch suggestions:', error)
    suggestions.value = []
  } finally {
    loading.value = false
  }
}

// 选择建议
const selectSuggestion = (suggestion: string) => {
  emit('select', suggestion)
  emit('close')
}

// 监听查询变化
watch(() => props.query, (newQuery) => {
  if (newQuery.trim().length > 1) {
    // 防抖处理
    setTimeout(() => {
      if (props.query === newQuery) {
        fetchSuggestions()
      }
    }, 300)
  } else {
    suggestions.value = []
  }
})

// 监听显示状态
watch(() => props.show, (show) => {
  if (show && props.query.trim().length > 1) {
    fetchSuggestions()
  }
})
</script>
