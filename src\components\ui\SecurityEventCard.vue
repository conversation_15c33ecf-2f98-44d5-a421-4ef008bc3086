<template>
  <a :href="`/incidents/${event.id}`" class="card group cursor-pointer transition-all duration-300 hover:shadow-lg border border-gray-200/20 hover:border-gray-300/40 bg-base-100 block">
    <!-- 卡片内容 - 左右布局 -->
    <div class="flex items-start p-6">
      <!-- 左侧图片 -->
      <div class="w-32 h-24 flex-shrink-0 overflow-hidden rounded-lg">
        <img
          :src="event.coverImage || getDefaultImage(event.type)"
          :alt="event.title"
          class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
        />
      </div>

      <!-- 右侧内容 -->
      <div class="flex-1 ml-6">
        <div class="flex items-start justify-between h-full">
          <div class="flex-1 space-y-3">
            <!-- 头部标签 -->
            <div class="flex items-center gap-2 flex-wrap">
              <span :class="getSeverityBadgeClass(event.severity)">
                {{ event.severity }}
              </span>
              <span class="badge badge-outline badge-sm">
                {{ event.type }}
              </span>
              <span class="text-sm text-base-content/60">
                {{ event.event_id }}
              </span>
            </div>

            <!-- 标题 -->
            <h3 class="text-lg font-semibold text-base-content leading-tight group-hover:text-primary transition-colors duration-200">
              {{ event.title }}
            </h3>

            <!-- 描述 -->
            <p class="text-sm text-base-content/70 line-clamp-2">
              {{ event.description }}
            </p>

            <!-- IP信息 -->
            <div class="flex items-center gap-4 text-xs text-base-content/60">
              <div class="flex items-center gap-1">
                <span class="font-medium">源IP:</span>
                <span class="font-mono">{{ event.source_ip }}</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="font-medium">目标IP:</span>
                <span class="font-mono">{{ event.target_ip }}</span>
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="flex items-center justify-between text-xs text-base-content/60">
              <div class="flex items-center gap-4">
                <div class="flex items-center gap-1">
                  <Clock class="h-3 w-3" />
                  {{ formatTime(event.detection_time) }}
                </div>
                <div class="flex items-center gap-1">
                  <User class="h-3 w-3" />
                  {{ event.analyst }}
                </div>
                <div class="flex items-center gap-1">
                  <span class="text-xs">事件数:</span>
                  <span class="font-medium">{{ event.event_count }}</span>
                </div>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-xs">风险评分:</span>
                <span class="font-medium" :class="getRiskScoreClass(event.risk_score)">{{ event.risk_score }}</span>
              </div>
            </div>

            <!-- 状态和标签 -->
            <div class="flex items-center justify-between">
              <span :class="getStatusBadgeClass(event.status)">
                {{ event.status }}
              </span>
              
              <!-- 标签 -->
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="tag in event.tags.slice(0, 2)"
                  :key="tag"
                  class="badge badge-ghost badge-xs"
                >
                  {{ tag }}
                </span>
                <span
                  v-if="event.tags.length > 2"
                  class="badge badge-ghost badge-xs"
                >
                  +{{ event.tags.length - 2 }}
                </span>
              </div>
            </div>
          </div>

          <!-- 右侧箭头 -->
          <div class="ml-4 flex items-center">
            <ChevronRight class="h-5 w-5 text-base-content/40 group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部装饰线 -->
    <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </a>
</template>

<script setup lang="ts">
import { Clock, User, ChevronRight } from 'lucide-vue-next'
import type { SecurityEvent } from '@/types/api'

interface Props {
  event: SecurityEvent & { coverImage?: string }
}

defineProps<Props>()

// 获取默认图片
const getDefaultImage = (type: string) => {
  const imageMap: Record<string, string> = {
    '入侵检测': 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format',
    '恶意软件': 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=400&h=250&fit=crop&auto=format',
    '异常行为': 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=250&fit=crop&auto=format',
    '数据泄露': 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop&auto=format',
    '网络攻击': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&auto=format',
    '系统异常': 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop&auto=format'
  }
  return imageMap[type] || 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format'
}

// 获取严重程度徽章样式
const getSeverityBadgeClass = (severity: string) => {
  switch (severity) {
    case '严重': return 'badge badge-error'
    case '高': return 'badge badge-warning'
    case '中': return 'badge badge-info'
    case '低': return 'badge badge-success'
    default: return 'badge badge-outline'
  }
}

// 获取状态徽章样式
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case '新事件': return 'badge badge-error badge-sm'
    case '处理中': return 'badge badge-warning badge-sm'
    case '已处理': return 'badge badge-success badge-sm'
    case '已关闭': return 'badge badge-neutral badge-sm'
    case '误报': return 'badge badge-ghost badge-sm'
    default: return 'badge badge-outline badge-sm'
  }
}

// 获取风险评分颜色
const getRiskScoreClass = (score: number) => {
  if (score >= 80) return 'text-error'
  if (score >= 60) return 'text-warning'
  if (score >= 40) return 'text-info'
  return 'text-success'
}

// 格式化时间
const formatTime = (timeString: string) => {
  const date = new Date(timeString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    return `${diffInMinutes}分钟前`
  } else if (diffInHours < 24) {
    return `${diffInHours}小时前`
  } else if (diffInHours < 24 * 7) {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
