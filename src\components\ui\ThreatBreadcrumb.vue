<template>
  <nav class="mb-6">
    <div class="breadcrumbs text-sm">
      <ul>
        <li>
          <a href="/" class="text-primary hover:text-primary/80 flex items-center gap-1">
            <span class="icon-[tabler--home] size-4"></span>
            首页
          </a>
        </li>
        <li class="breadcrumbs-separator rtl:-rotate-[40deg]">/</li>
        <li>
          <a href="/intell" class="text-primary hover:text-primary/80 flex items-center gap-1">
            <span class="icon-[tabler--shield-check] size-4"></span>
            威胁情报
          </a>
        </li>
        <li class="breadcrumbs-separator rtl:-rotate-[40deg]">/</li>
        <li aria-current="page" class="text-base-content/70 truncate max-w-xs">
          {{ threatTitle }}
        </li>
      </ul>
    </div>
  </nav>
</template>

<script setup lang="ts">
interface Props {
  threatTitle: string
}

defineProps<Props>()
</script>
