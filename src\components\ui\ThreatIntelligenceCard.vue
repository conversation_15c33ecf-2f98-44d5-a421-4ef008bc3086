<template>
  <a :href="`/intell/${threat.id}`" class="card group cursor-pointer transition-all duration-300 hover:shadow-lg border border-gray-200/20 hover:border-gray-300/40 bg-base-100 block">
    <!-- 卡片内容 - 左右布局 -->
    <div class="flex items-start p-6">
      <!-- 左侧图片 -->
      <div class="w-32 h-24 flex-shrink-0 overflow-hidden rounded-lg">
        <img
          :src="threat.coverImage || getDefaultImage(threat.type)"
          :alt="threat.title"
          class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          loading="lazy"
        />
      </div>

      <!-- 右侧内容 -->
      <div class="flex-1 ml-6">
        <div class="flex items-start justify-between h-full">
          <div class="flex-1 space-y-3">
            <!-- 头部标签 -->
            <div class="flex items-center gap-2 flex-wrap">
              <span :class="getSeverityBadgeClass(threat.severity)">
                {{ threat.severity }}
              </span>
              <span class="badge badge-outline badge-sm">
                {{ threat.type }}
              </span>
              <span class="text-sm text-base-content/60">
                {{ threat.source }}
              </span>
            </div>

            <!-- 标题 -->
            <h3 class="text-lg font-semibold text-base-content leading-tight group-hover:text-primary transition-colors duration-200">
              {{ threat.title }}
            </h3>

            <!-- 描述 -->
            <p class="text-sm text-base-content/70 line-clamp-2">
              {{ threat.description }}
            </p>

            <!-- 底部信息 -->
            <div class="flex items-center justify-between text-xs text-base-content/60">
              <div class="flex items-center gap-4">
                <div class="flex items-center gap-1">
                  <Clock class="h-3 w-3" />
                  {{ formatTime(threat.created_at) }}
                </div>
                <div class="flex items-center gap-1">
                  <User class="h-3 w-3" />
                  {{ threat.analyst }}
                </div>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-xs">置信度:</span>
                <span class="font-medium">{{ threat.confidence }}%</span>
              </div>
            </div>

            <!-- 标签 -->
            <div class="flex flex-wrap gap-1">
              <span
                v-for="tag in threat.tags.slice(0, 3)"
                :key="tag"
                class="badge badge-ghost badge-xs"
              >
                {{ tag }}
              </span>
              <span
                v-if="threat.tags.length > 3"
                class="badge badge-ghost badge-xs"
              >
                +{{ threat.tags.length - 3 }}
              </span>
            </div>
          </div>

          <!-- 右侧箭头 -->
          <div class="ml-4 flex items-center">
            <ChevronRight class="h-5 w-5 text-base-content/40 group-hover:text-primary group-hover:translate-x-1 transition-all duration-200" />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部装饰线 -->
    <div class="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </a>
</template>

<script setup lang="ts">
import { Clock, User, ChevronRight } from 'lucide-vue-next'
import type { ThreatIntelligence } from '@/types/api'

interface Props {
  threat: ThreatIntelligence & { coverImage?: string }
}

defineProps<Props>()

// 获取严重程度徽章样式
const getSeverityBadgeClass = (severity: string) => {
  switch (severity) {
    case '严重': return 'badge badge-error'
    case '高': return 'badge badge-warning'
    case '中': return 'badge badge-info'
    case '低': return 'badge badge-success'
    default: return 'badge badge-secondary'
  }
}

// 获取默认图片
const getDefaultImage = (type: string) => {
  const imageMap: Record<string, string> = {
    '恶意软件': 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=400&h=250&fit=crop&auto=format',
    '钓鱼攻击': 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?w=400&h=250&fit=crop&auto=format',
    'DDoS攻击': 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=250&fit=crop&auto=format',
    '数据泄露': 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop&auto=format',
    '勒索软件': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=250&fit=crop&auto=format',
    'APT攻击': 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop&auto=format'
  }
  return imageMap[type] || 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=250&fit=crop&auto=format'
}

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
