<template>
  <div v-if="prevThreat || nextThreat" class="mt-8 lg:mt-12 pt-6 lg:pt-8 border-t border-base-300">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
      <!-- 上一篇威胁情报 -->
      <a
        v-if="prevThreat"
        :href="`/intell/${prevThreat.id}`"
        class="card shadow-none hover:shadow-lg transition-all duration-300 border border-gray-200/20 hover:border-gray-300/40 group"
      >
        <div class="card-body p-4 lg:p-6">
          <div class="flex items-center gap-2 mb-3">
            <span class="icon-[tabler--chevron-left] size-4 text-base-content/60 group-hover:text-primary transition-colors"></span>
            <span class="text-sm text-base-content/60 group-hover:text-primary transition-colors">上一篇威胁情报</span>
          </div>
          <div class="mb-2">
            <span :class="getSeverityBadgeClass(prevThreat.severity)">
              {{ prevThreat.severity }}
            </span>
          </div>
          <h3 class="text-base lg:text-lg font-semibold text-base-content group-hover:text-primary transition-colors line-clamp-2">
            {{ prevThreat.title }}
          </h3>
          <div class="flex flex-wrap gap-1 mt-2">
            <span
              v-for="tag in (prevThreat.tags || []).slice(0, 2)"
              :key="tag"
              class="badge badge-ghost badge-xs"
            >
              {{ tag }}
            </span>
            <span
              v-if="(prevThreat.tags || []).length > 2"
              class="badge badge-ghost badge-xs"
            >
              +{{ (prevThreat.tags || []).length - 2 }}
            </span>
          </div>
        </div>
      </a>

      <!-- 下一篇威胁情报 -->
      <a
        v-if="nextThreat"
        :href="`/intell/${nextThreat.id}`"
        :class="`card shadow-none hover:shadow-lg transition-all duration-300 border border-gray-200/20 hover:border-gray-300/40 group ${!prevThreat ? 'lg:col-start-2' : ''}`"
      >
        <div class="card-body p-4 lg:p-6">
          <div class="flex items-center justify-end gap-2 mb-3">
            <span class="text-sm text-base-content/60 group-hover:text-primary transition-colors">下一篇威胁情报</span>
            <span class="icon-[tabler--chevron-right] size-4 text-base-content/60 group-hover:text-primary transition-colors"></span>
          </div>
          <div class="mb-2 flex justify-end">
            <span :class="getSeverityBadgeClass(nextThreat.severity)">
              {{ nextThreat.severity }}
            </span>
          </div>
          <h3 class="text-base lg:text-lg font-semibold text-base-content group-hover:text-primary transition-colors line-clamp-2 text-right">
            {{ nextThreat.title }}
          </h3>
          <div class="flex flex-wrap gap-1 mt-2 justify-end">
            <span
              v-for="tag in (nextThreat.tags || []).slice(0, 2)"
              :key="tag"
              class="badge badge-ghost badge-xs"
            >
              {{ tag }}
            </span>
            <span
              v-if="(nextThreat.tags || []).length > 2"
              class="badge badge-ghost badge-xs"
            >
              +{{ (nextThreat.tags || []).length - 2 }}
            </span>
          </div>
        </div>
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ThreatItem {
  id: number
  title: string
  severity: string
  tags: string[]
}

interface Props {
  prevThreat: ThreatItem | null
  nextThreat: ThreatItem | null
  getSeverityBadgeClass: (severity: string) => string
}

defineProps<Props>()
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
