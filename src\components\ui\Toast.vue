<template>
  <Teleport to="body" v-if="isMounted">
    <div
      v-if="visible"
      class="fixed z-50 transition-all duration-300 ease-in-out"
      :class="positionClasses"
    >
      <div class="alert shadow-lg" :class="alertClasses">
        <div class="flex items-center gap-2">
          <component :is="iconComponent" class="h-5 w-5" />
          <span>{{ message }}</span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import {
  CheckCircle,
  AlertCircle,
  Info,
  AlertTriangle,
  X
} from 'lucide-vue-next'

// 明确声明不需要继承属性，避免Vue警告
// 因为使用了Teleport，无法自动继承父组件属性
defineOptions({
  inheritAttrs: false
})

// 确保只在客户端渲染
const isMounted = ref(false)
onMounted(() => {
  isMounted.value = true
})

export interface ToastProps {
  visible: boolean
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right'
}

const props = withDefaults(defineProps<ToastProps>(), {
  type: 'success',
  position: 'bottom-right'
})

// 根据类型选择图标
const iconComponent = computed(() => {
  switch (props.type) {
    case 'success':
      return CheckCircle
    case 'error':
      return AlertCircle
    case 'warning':
      return AlertTriangle
    case 'info':
      return Info
    default:
      return CheckCircle
  }
})

// 根据类型选择alert样式
const alertClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'alert-success'
    case 'error':
      return 'alert-error'
    case 'warning':
      return 'alert-warning'
    case 'info':
      return 'alert-info'
    default:
      return 'alert-success'
  }
})

// 根据位置选择定位样式
const positionClasses = computed(() => {
  switch (props.position) {
    case 'top-center':
      return 'top-4 left-1/2 transform -translate-x-1/2'
    case 'top-right':
      return 'top-4 right-4'
    case 'bottom-center':
      return 'bottom-4 left-1/2 transform -translate-x-1/2'
    case 'bottom-right':
      return 'bottom-4 right-4'
    default:
      return 'bottom-4 right-4'
  }
})
</script>
