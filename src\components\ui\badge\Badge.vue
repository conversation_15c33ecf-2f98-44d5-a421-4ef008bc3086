<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { type BadgeVariants, badgeVariants } from '.'

interface Props {
  variant?: BadgeVariants['variant']
  class?: HTMLAttributes['class']
  as?: string
}

const props = withDefaults(defineProps<Props>(), {
  as: 'span',
  variant: 'default',
})
</script>

<template>
  <component
    :is="props.as"
    :class="cn(badgeVariants({ variant: props.variant }), props.class)"
  >
    <slot />
  </component>
</template>
