'use client';

import React, { useEffect, useRef } from 'react';
import createGlobe from 'cobe';
import { cn } from '@/lib/utils';

interface EarthProps {
  className?: string;
  theta?: number;
  dark?: number;
  scale?: number;
  diffuse?: number;
  mapSamples?: number;
  mapBrightness?: number;
  baseColor?: [number, number, number];
  markerColor?: [number, number, number];
  glowColor?: [number, number, number];
}

const Earth: React.FC<EarthProps> = ({
  className,
  // theta = 0.25, // 移除未使用的参数
  dark = 1,
  scale = 1.0,
  diffuse = 1.1,
  mapSamples = 12000, // 进一步降低采样数以提高性能
  mapBrightness = 5,
  baseColor = [0.0, 0.52, 1.0], // 威胁情报数据中心主色调
  markerColor = [0.0, 0.75, 1.0], // 威胁情报数据中心强调色
  glowColor = [0.0, 0.52, 1.0], // 威胁情报数据中心主色调
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-26 22:15:00 +08:00]
  // Reason: Per P0-REF-001 to replace useState with useRef for rotation to prevent useEffect re-triggering
  // Principle_Applied: KISS - 最小化修改，仅改变状态管理方式避免不必要的重新渲染
  // Optimization: 使用useRef避免rotation变化触发组件重新渲染，消除Globe实例频繁重建
  // Architectural_Note (AR): 分离渲染逻辑和状态管理，符合单一职责原则
  // }}
  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-26 22:20:00 +08:00]
  // Reason: Per P0-REF-005 to convert isDragging to ref for consistency and prevent re-renders
  // Principle_Applied: KISS - 保持状态管理的一致性，全部使用ref
  // Optimization: 进一步减少不必要的重新渲染
  // }}
  const isDraggingRef = useRef(false);
  const rotationRef = useRef({ phi: 0, theta: 0.25 });
  const lastPosition = useRef({ x: 0, y: 0 });
  const rotationSpeed = useRef({ x: 0, y: 0 });
  const animationFrameId = useRef<number | null>(null);

  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-26 21:59:00 +08:00]
  // Reason: 更新代码注释，说明修复后的拖动交互逻辑
  // Documentation_Note (DW): 相关测试指南已更新至 /project_document/地球组件测试指南.md
  // }}
  // 鼠标事件处理 - 修复后支持直觉化的拖动交互
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleMouseDown = (e: MouseEvent) => {
      isDraggingRef.current = true;
      lastPosition.current = { x: e.clientX, y: e.clientY };
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current) return;

      const deltaX = e.clientX - lastPosition.current.x;
      const deltaY = e.clientY - lastPosition.current.y;

      // 同时反转水平和垂直方向
      rotationSpeed.current = {
        x: deltaX * 0.01,   // 不再取反
        y: deltaY * 0.01    // 不再取反
      };

      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-01-26 21:55:00 +08:00]
      // Reason: 修复鼠标拖动方向与地球转动方向相反的问题，符合用户直觉交互习惯
      // Principle_Applied: KISS - 最小化修改，仅改变运算符号
      // Optimization: 提升用户交互体验，使拖动行为自然流畅
      // }}
      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-01-26 22:16:00 +08:00]
      // Reason: Per P0-REF-001 to update rotation access from state to ref
      // Principle_Applied: KISS - 直接修改ref值，避免触发React重新渲染
      // Optimization: 消除setState调用，防止useEffect重新执行
      // }}
      // 修正旋转计算方向，使拖动方向与地球转动方向一致
      rotationRef.current = {
        phi: rotationRef.current.phi + rotationSpeed.current.x,  // 修复：改为加法
        theta: Math.max(0.1, Math.min(1.4, rotationRef.current.theta + rotationSpeed.current.y))  // 修复：改为加法
      };

      lastPosition.current = { x: e.clientX, y: e.clientY };
    };

    const handleMouseUp = () => {
      isDraggingRef.current = false;
      // 应用惯性效果
      applyInertia();
    };

    container.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      container.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, []); // 移除isDragging依赖，使用ref不需要依赖

  // 惯性效果
  const applyInertia = () => {
    const decay = 0.92;

    const animate = () => {
      if (Math.abs(rotationSpeed.current.x) < 0.01 &&
          Math.abs(rotationSpeed.current.y) < 0.01) {
        return;
      }

      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-01-26 21:56:00 +08:00]
      // Reason: 修复惯性效果的旋转方向，确保与拖动方向保持一致
      // Principle_Applied: KISS - 保持与主拖动逻辑的一致性
      // Optimization: 提供自然流畅的惯性旋转体验
      // }}
      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-01-26 22:17:00 +08:00]
      // Reason: Per P0-REF-001 to update inertia effect rotation access from state to ref
      // Principle_Applied: KISS - 保持与主拖动逻辑的一致性，直接操作ref
      // Optimization: 惯性效果也不再触发React重新渲染
      // }}
      rotationRef.current = {
        phi: rotationRef.current.phi + rotationSpeed.current.x,  // 修复：改为加法
        theta: Math.max(0.1, Math.min(1.4, rotationRef.current.theta + rotationSpeed.current.y))  // 修复：改为加法
      };

      rotationSpeed.current = {
        x: rotationSpeed.current.x * decay,
        y: rotationSpeed.current.y * decay
      };

      animationFrameId.current = requestAnimationFrame(animate);
    };

    animationFrameId.current = requestAnimationFrame(animate);
  };

  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-01-26 22:22:00 +08:00]
  // Reason: Per P0-REF-006 to separate resize logic and optimize event binding
  // Principle_Applied: SOLID - 单一职责原则，分离resize逻辑和globe渲染逻辑
  // Optimization: resize监听器只绑定一次，避免重复添加
  // }}
  const widthRef = useRef(0);

  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-26 22:23:00 +08:00]
  // Reason: Per P0-REF-007 to add performance monitoring for verification
  // Principle_Applied: SOLID - 开闭原则，添加监控不影响核心功能
  // Optimization: 提供性能验证数据，确保修复效果
  // }}
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-01-26 22:45:00 +08:00]
  // Reason: 性能优化 - 缩小3D地球模型以提升页面流畅度
  // Principle_Applied: KISS - 通过减少渲染负载提升性能
  // Optimization: 降低mapSamples(20000→12000)、devicePixelRatio(2→1.5)、容器尺寸(350px→280px)、标记数量(6→4)
  // }}
  // 性能监控 (开发环境)
  const globeRebuildCount = useRef(0);
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🌍 Globe组件挂载 - 闪烁修复版本 + 性能优化版本');
    }
  }, []);

  // Resize事件处理独立useEffect
  useEffect(() => {
    const onResize = () => {
      if (canvasRef.current) {
        widthRef.current = canvasRef.current.offsetWidth;
      }
    };

    window.addEventListener('resize', onResize);
    onResize(); // 初始化尺寸

    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []); // 只在组件挂载时绑定一次

  useEffect(() => {

    let animationId: number | null = null;

    const render = () => {
      if (!canvasRef.current) return;

      // 性能监控
      if (process.env.NODE_ENV === 'development') {
        globeRebuildCount.current++;
        console.log(`🌍 Globe实例创建 #${globeRebuildCount.current} (应该只创建一次)`);
      }

      const globe = createGlobe(canvasRef.current, {
        devicePixelRatio: 1.5, // 降低像素比以提升性能
        width: widthRef.current * 1.5, // 降低画布分辨率
        height: widthRef.current * 1.5,
        phi: rotationRef.current.phi,
        theta: rotationRef.current.theta,
        dark: dark,
        scale: scale,
        diffuse: diffuse,
        mapSamples: mapSamples,
        mapBrightness: mapBrightness,
        baseColor: baseColor,
        markerColor: markerColor,
        glowColor: glowColor,
        opacity: 1,
        offset: [0, 0],
        markers: [
          // 威胁情报热点区域标记 - 性能优化：减少标记数量
          { location: [39.9042, 116.4074], size: 0.08 }, // 北京
          { location: [40.7128, -74.0060], size: 0.08 }, // 纽约
          { location: [51.5074, -0.1278], size: 0.08 }, // 伦敦
          { location: [55.7558, 37.6176], size: 0.08 }, // 莫斯科
        ],
        onRender: (state: Record<string, any>) => {
          // {{CHENGQI:
          // Action: Modified
          // Timestamp: [2025-01-26 22:18:00 +08:00]
          // Reason: Per P0-REF-003 to eliminate setState calls in onRender, breaking the circular dependency
          // Principle_Applied: SOLID - 单一职责原则，onRender只负责渲染状态更新
          // Optimization: 直接操作ref，避免触发React状态更新和useEffect循环
          // }}
          // 仅在非拖动状态下自动旋转
          if (!isDraggingRef.current) {
            state.phi = rotationRef.current.phi + 0.003;
            rotationRef.current.phi = state.phi; // 直接更新ref，不触发重新渲染
          } else {
            state.phi = rotationRef.current.phi;
            state.theta = rotationRef.current.theta;
          }
        },
      });

      return () => {
        globe.destroy();
        if (animationId) cancelAnimationFrame(animationId);
      };
    };

    const globeInstance = render();

    return () => {
      if (globeInstance) globeInstance();
    };
    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-01-26 22:19:00 +08:00]
    // Reason: Per P0-REF-002 to remove rotation dependency from useEffect, preventing Globe recreation
    // Principle_Applied: KISS - 移除不必要的依赖项，只在组件挂载时创建Globe实例
    // Optimization: Globe实例只创建一次，彻底消除频繁重建导致的闪烁
    // }}
  }, []); // 移除依赖项，Globe实例只在组件挂载时创建一次

  return (
    <div
      ref={containerRef}
      className={cn(
        'flex items-center justify-center z-[10] w-full max-w-[280px] mx-auto cursor-grab',
        className
      )}
      style={{ cursor: isDraggingRef.current ? 'grabbing' : 'grab' }}
    >
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          maxWidth: '100%',
          aspectRatio: '1',
        }}
      />
    </div>
  );
};

export default Earth;
