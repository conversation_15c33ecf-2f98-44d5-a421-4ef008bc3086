<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { useVModel } from '@vueuse/core'
import { cn } from '@/lib/utils'

interface Props {
  modelValue?: string
  class?: HTMLAttributes['class']
  placeholder?: string
}

const props = defineProps<Props>()
const emits = defineEmits<{
  'update:modelValue': [value: string]
}>()

const modelValue = useVModel(props, 'modelValue', emits)
</script>

<template>
  <select
    v-model="modelValue"
    :class="cn('select', props.class)"
  >
    <option v-if="placeholder" disabled value="">{{ placeholder }}</option>
    <slot />
  </select>
</template>
