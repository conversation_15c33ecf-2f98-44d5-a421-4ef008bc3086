<template>
  <header
    class="fixed left-0 right-0 top-0 z-50 transition-all duration-300"
    :style="{
      backdropFilter: isScrolled ? 'blur(20px)' : 'none',
      backgroundColor: isScrolled
        ? theme === 'dark'
          ? 'rgba(0, 0, 0, 0.8)'
          : 'rgba(255, 255, 255, 0.8)'
        : 'transparent',
      boxShadow: isScrolled ? '0 8px 32px rgba(0, 0, 0, 0.1)' : 'none',
    }"
  >
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex h-16 items-center justify-between lg:h-20">
        <!-- Logo -->
        <div class="flex items-center space-x-2">
          <a href="/" class="flex items-center space-x-2">
            <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-primary/80">
              <Sparkles class="h-5 w-5 text-primary-content" />
            </div>
            <span class="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-xl font-bold text-transparent">
              威胁情报数据中心
            </span>
          </a>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden items-center space-x-8 lg:flex">
          <!-- 首页 -->
          <a
            href="/"
            :class="getNavItemClass('/')"
          >
            首页
          </a>

          <!-- 威胁情报下拉菜单 -->
          <div class="relative group">
            <button
              type="button"
              :class="getThreatIntelButtonClass()"
              aria-haspopup="menu"
              aria-expanded="false"
              aria-label="威胁情报菜单"
            >
              <span>威胁情报</span>
              <ChevronDown class="h-4 w-4 group-hover:rotate-180 transition-transform duration-200" />
            </button>
            <div class="absolute top-full left-0 mt-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <ul class="min-w-80 bg-background/95 backdrop-blur-lg border border-border shadow-xl rounded-xl py-2">
                <li v-for="item in threatIntelDropdownItems" :key="item.name">
                  <a
                    :class="getThreatIntelDropdownItemClass(item.href)"
                    :href="item.href"
                  >
                    <div class="flex-shrink-0 mt-0.5">
                      <component :is="item.icon" :class="getThreatIntelDropdownIconClass(item.href)" />
                    </div>
                    <div class="flex-1 min-w-0">
                      <h6 class="font-semibold text-foreground text-sm">{{ item.name }}</h6>
                      <p class="text-xs text-muted-foreground mt-1 leading-relaxed">{{ item.description }}</p>
                    </div>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- 其他导航项 -->
          <a
            v-for="item in navItems.slice(1)"
            :key="item.name"
            :href="item.href"
            :class="getNavItemClass(item.href)"
          >
            {{ item.name }}
          </a>
        </nav>        <!-- User Menu / Login Buttons -->
        <div class="hidden items-center space-x-4 lg:flex">
          <template v-if="isLoggedIn">
            <!-- 已登录状态 - 显示用户头像下拉菜单 -->
            <div class="relative" ref="userMenuRef">
              <button
                class="flex items-center space-x-2 rounded-full px-3 py-1 transition-colors duration-200 hover:bg-muted"
                @click="isUserMenuOpen = !isUserMenuOpen"
              >
                <div class="relative">
                  <img
                    :src="getAvatarUrl()"
                    :alt="userInfo?.username"
                    class="h-8 w-8 rounded-full object-cover"
                    @error="handleAvatarError"
                  />
                  <div
                    v-if="isRefreshingAvatar"
                    class="absolute inset-0 flex items-center justify-center bg-black/20 rounded-full"
                  >
                    <div class="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                </div>
                <span class="font-medium text-foreground text-sm">
                  {{ getUserDisplayName() }}
                </span>
                <ChevronDown :class="`h-4 w-4 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`" />
              </button>

              <Transition
                enter-active-class="transition duration-200 ease-out"
                enter-from-class="opacity-0 -translate-y-2"
                enter-to-class="opacity-100 translate-y-0"
                leave-active-class="transition duration-200 ease-in"
                leave-from-class="opacity-100 translate-y-0"
                leave-to-class="opacity-0 -translate-y-2"
              >
                <div
                  v-if="isUserMenuOpen"
                  class="absolute right-0 top-full mt-2 w-64 rounded-xl border border-border bg-background/95 py-2 shadow-xl backdrop-blur-lg"
                >
                  <!-- 用户信息头部 -->
                  <div class="border-b border-border px-4 py-3">
                    <div class="flex items-center space-x-3">
                      <div class="relative">
                        <img
                          :src="getAvatarUrl()"
                          :alt="userInfo?.username"
                          class="h-10 w-10 rounded-full object-cover"
                          @error="handleAvatarError"
                        />
                        <div
                          v-if="isRefreshingAvatar"
                          class="absolute inset-0 flex items-center justify-center bg-black/20 rounded-full"
                        >
                          <div class="w-4 h-4 border border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                      </div>
                      <div>
                        <h6 class="font-semibold text-foreground">
                          {{ getUserDisplayName() }}
                        </h6>
                      </div>
                    </div>
                  </div>

                  <!-- 菜单项 -->
                  <div class="py-1">
                    <a
                      href="/profile"
                      class="flex items-center space-x-3 px-4 py-2 text-sm text-foreground transition-colors duration-200 hover:bg-muted"
                      @click="isUserMenuOpen = false"
                    >
                      <User class="h-4 w-4" />
                      <span>个人中心</span>
                    </a>
                    <a
                      href="/profile?tab=security"
                      class="flex items-center space-x-3 px-4 py-2 text-sm text-foreground transition-colors duration-200 hover:bg-muted"
                      @click="isUserMenuOpen = false"
                    >
                      <Settings class="h-4 w-4" />
                      <span>设置</span>
                    </a>
                  </div>

                  <div class="border-t border-border pt-1">
                    <button
                      class="flex w-full items-center space-x-3 px-4 py-2 text-sm text-red-500 transition-colors duration-200 hover:bg-muted"
                      @click="logout()"
                    >
                      <LogOut class="h-4 w-4" />
                      <span>退出登录</span>
                    </button>
                  </div>
                </div>
              </Transition>
            </div>
          </template>
          <template v-else>
            <!-- 未登录状态 - 显示登录/注册按钮 -->
            <a
              href="/login"
              class="font-medium text-foreground transition-colors duration-200 hover:text-primary"
            >
              登录
            </a>
            <a
              href="/register"
              class="inline-flex items-center space-x-2 rounded-full bg-gradient-to-r from-primary to-primary/80 px-6 py-2.5 font-medium text-primary-content transition-all duration-200 hover:shadow-lg hover:from-primary/90 hover:to-primary/70"
            >
              <span>免费试用</span>
              <ArrowRight class="h-4 w-4" />
            </a>
          </template>
        </div>        <!-- Mobile Menu Button -->
        <button
          class="rounded-lg p-2 transition-colors duration-200 hover:bg-muted lg:hidden"
          @click="isMobileMenuOpen = !isMobileMenuOpen"
        >
          <X v-if="isMobileMenuOpen" class="h-6 w-6" />
          <Menu v-else class="h-6 w-6" />
        </button>
      </div>

      <!-- Mobile Menu -->
      <Transition
        enter-active-class="transition duration-300 ease-out"
        enter-from-class="opacity-0 h-0"
        enter-to-class="opacity-100 h-auto"
        leave-active-class="transition duration-300 ease-in"
        leave-from-class="opacity-100 h-auto"
        leave-to-class="opacity-0 h-0"
      >
        <div v-if="isMobileMenuOpen" class="overflow-hidden lg:hidden">
          <div class="mt-4 space-y-2 rounded-xl border border-border bg-background/95 py-4 shadow-xl backdrop-blur-lg">
            <!-- 首页 -->
            <a
              href="/"
              :class="getMobileNavItemClass('/')"
              @click="isMobileMenuOpen = false"
            >
              首页
            </a>

            <!-- 移动端威胁情报下拉菜单 -->
            <div class="relative">
              <details class="group">
                <summary class="w-full text-left px-4 py-3 font-medium text-foreground transition-colors duration-200 hover:bg-muted flex items-center justify-between cursor-pointer list-none">
                  <span>威胁情报</span>
                  <ChevronDown class="h-4 w-4 group-open:rotate-180 transition-transform duration-200" />
                </summary>
                <div class="w-full bg-background/95 backdrop-blur-lg border border-border shadow-xl rounded-lg mt-2">
                  <a
                    v-for="item in threatIntelDropdownItems"
                    :key="item.name"
                    :class="getThreatIntelDropdownItemClass(item.href)"
                    :href="item.href"
                    @click="isMobileMenuOpen = false"
                  >
                    <div class="flex-shrink-0 mt-0.5">
                      <component :is="item.icon" :class="getThreatIntelDropdownIconClass(item.href)" />
                    </div>
                    <div class="flex-1 min-w-0">
                      <h6 class="font-semibold text-foreground text-sm">{{ item.name }}</h6>
                      <p class="text-xs text-muted-foreground mt-1 leading-relaxed">{{ item.description }}</p>
                    </div>
                  </a>
                </div>
              </details>
            </div>

            <!-- 其他导航项 -->
            <a
              v-for="item in navItems.slice(1)"
              :key="item.name"
              :href="item.href"
              :class="getMobileNavItemClass(item.href)"
              @click="isMobileMenuOpen = false"
            >
              {{ item.name }}
            </a>

            <div class="space-y-2 px-4 py-2">
              <a
                href="/login"
                class="block w-full rounded-lg py-2.5 text-center font-medium text-foreground transition-colors duration-200 hover:bg-muted"
                @click="isMobileMenuOpen = false"
              >
                登录
              </a>
              <a
                href="/register"
                class="block w-full rounded-lg bg-gradient-to-r from-primary to-primary/80 py-2.5 text-center font-medium text-primary-content transition-all duration-200 hover:shadow-lg hover:from-primary/90 hover:to-primary/70"
                @click="isMobileMenuOpen = false"
              >
                免费试用
              </a>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Menu, X, ArrowRight, Sparkles, ChevronDown, Shield, AlertTriangle, User, Settings, LogOut } from 'lucide-vue-next'
import { getUser, logout } from '@/lib/auth'
import { avatarManager, isOSSUrlExpired } from '@/lib/avatar-manager'
import type { User as UserType } from '@/types/api'

interface NavItem {
  name: string
  href: string
}

interface ThreatIntelDropdownItem {
  name: string
  href: string
  description: string
  icon: any
}

const navItems: NavItem[] = [
  { name: '首页', href: '/' },
  { name: '勒索组织', href: '/ransomware' },
  { name: '应急工具', href: '/tools' },
  { name: '安全博客', href: '/blog' },
  { name: '威胁统计', href: '/statistics' },
]

const threatIntelDropdownItems: ThreatIntelDropdownItem[] = [
  {
    name: '威胁情报',
    href: '/intell?category=1',
    description: '实时更新的全球威胁情报数据',
    icon: Shield
  },
  {
    name: '安全事件',
    href: '/intell?category=2',
    description: '安全事件监测与分析',
    icon: AlertTriangle
  },
]

// 响应式数据
const userInfo = ref<UserType | null>(getUser())
const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)
const isUserMenuOpen = ref(false)
const isLoggedIn = ref(false)
const avatarError = ref(false)
const isRefreshingAvatar = ref(false)
const theme = 'dark' // 固定为暗色模式
const userMenuRef = ref<HTMLDivElement>()
const currentPath = ref('')

// 获取当前路径
const getCurrentPath = () => {
  if (typeof window === 'undefined') return ''
  return window.location.pathname
}

// 检查导航项是否激活
const isNavItemActive = (href: string) => {
  if (href === '/') {
    return currentPath.value === '/'
  }
  return currentPath.value.startsWith(href)
}

// 检查威胁情报下拉菜单是否激活
const isThreatIntelActive = () => {
  return currentPath.value.startsWith('/intell')
}

// 检查威胁情报下拉菜单项是否激活
const isThreatIntelDropdownItemActive = (href: string) => {
  if (typeof window === 'undefined') return false

  // 提取href中的路径和查询参数
  const url = new URL(href, window.location.origin)
  const hrefPath = url.pathname
  const hrefCategory = url.searchParams.get('category')

  // 获取当前页面的查询参数
  const currentCategory = new URLSearchParams(window.location.search).get('category')

  // 检查路径和分类参数是否匹配
  return currentPath.value === hrefPath && currentCategory === hrefCategory
}

// 获取导航项样式类
const getNavItemClass = (href: string) => {
  const baseClass = 'font-medium transition-colors duration-200'
  const activeClass = 'text-primary font-semibold'
  const inactiveClass = 'text-foreground hover:text-primary'

  return `${baseClass} ${isNavItemActive(href) ? activeClass : inactiveClass}`
}

// 获取威胁情报按钮样式类
const getThreatIntelButtonClass = () => {
  const baseClass = 'font-medium transition-colors duration-200 flex items-center space-x-1'
  const activeClass = 'text-primary font-semibold'
  const inactiveClass = 'text-foreground hover:text-primary'

  return `${baseClass} ${isThreatIntelActive() ? activeClass : inactiveClass}`
}

// 获取移动端导航项样式类
const getMobileNavItemClass = (href: string) => {
  const baseClass = 'block px-4 py-3 font-medium transition-colors duration-200'
  const activeClass = 'text-primary bg-primary/10 font-semibold'
  const inactiveClass = 'text-foreground hover:bg-muted'

  return `${baseClass} ${isNavItemActive(href) ? activeClass : inactiveClass}`
}

// 获取威胁情报下拉菜单项样式类
const getThreatIntelDropdownItemClass = (href: string) => {
  const baseClass = 'flex items-start space-x-3 p-4 transition-colors duration-200'
  const activeClass = 'bg-primary/10 text-primary'
  const inactiveClass = 'hover:bg-muted/50'

  return `${baseClass} ${isThreatIntelDropdownItemActive(href) ? activeClass : inactiveClass}`
}

// 获取威胁情报下拉菜单图标样式类
const getThreatIntelDropdownIconClass = (href: string) => {
  const baseClass = 'h-5 w-5 transition-colors duration-200'
  const activeClass = 'text-primary'
  const inactiveClass = 'text-primary'

  return `${baseClass} ${isThreatIntelDropdownItemActive(href) ? activeClass : inactiveClass}`
}

// 获取用户显示名称：优先显示昵称，如果没有昵称再显示用户名
const getUserDisplayName = () => {
  if (!userInfo.value) return ''
  return userInfo.value.nickname || userInfo.value.username
}

// 检查并刷新头像
const checkAndRefreshAvatar = async () => {
  if (!userInfo.value || isRefreshingAvatar.value) return

  // 检查头像是否需要刷新
  if (avatarManager.needsRefresh(userInfo.value)) {
    isRefreshingAvatar.value = true
    try {
      const updatedUser = await avatarManager.refreshUserAvatar()
      if (updatedUser) {
        userInfo.value = updatedUser
        avatarError.value = false
      }
    } catch (error) {
      console.error('刷新头像失败:', error)
    } finally {
      isRefreshingAvatar.value = false
    }
  }
}

// 处理头像加载错误
const handleAvatarError = async () => {
  if (avatarError.value || isRefreshingAvatar.value) return

  avatarError.value = true

  // 如果是OSS链接且已过期，尝试刷新
  if (userInfo.value?.avatar && isOSSUrlExpired(userInfo.value.avatar)) {
    isRefreshingAvatar.value = true
    try {
      const updatedUser = await avatarManager.refreshUserAvatar()
      if (updatedUser) {
        userInfo.value = updatedUser
        avatarError.value = false
      }
    } catch (error) {
      console.error('刷新头像失败:', error)
    } finally {
      isRefreshingAvatar.value = false
    }
  }
}

// 获取头像URL（带错误处理）
const getAvatarUrl = () => {
  if (avatarError.value || !userInfo.value?.avatar) {
    return 'https://cdn.flyonui.com/fy-assets/avatar/avatar-1.png'
  }
  return userInfo.value.avatar
}// 事件处理函数
const handleScroll = () => {
  isScrolled.value = window.scrollY > 20
}

const checkLoginStatus = () => {
  const user = localStorage.getItem('user')
  isLoggedIn.value = !!user
}

const handleClickOutside = (event: MouseEvent) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    isUserMenuOpen.value = false
  }
}

const handleAvatarUpdate = (event: Event) => {
  const customEvent = event as CustomEvent<UserType>
  userInfo.value = customEvent.detail
  avatarError.value = false
}

const handleVisibilityChange = () => {
  if (!document.hidden) {
    checkAndRefreshAvatar()
  }
}

const handlePopState = () => {
  currentPath.value = getCurrentPath()
}

// 生命周期钩子
onMounted(() => {
  // 滚动事件监听
  window.addEventListener('scroll', handleScroll)

  // 检查登录状态
  checkLoginStatus()

  // 监听用户头像更新事件
  window.addEventListener('userAvatarUpdated', handleAvatarUpdate)

  // 页面可见性变化时检查头像
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 监听浏览器前进后退
  window.addEventListener('popstate', handlePopState)

  // 初始化当前路径
  currentPath.value = getCurrentPath()

  // 初始检查头像状态
  checkAndRefreshAvatar()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  document.removeEventListener('mousedown', handleClickOutside)
  window.removeEventListener('userAvatarUpdated', handleAvatarUpdate)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('popstate', handlePopState)
})

// 监听用户菜单状态变化，添加/移除点击外部关闭事件
watch(isUserMenuOpen, (newValue) => {
  if (newValue) {
    document.addEventListener('mousedown', handleClickOutside)
  } else {
    document.removeEventListener('mousedown', handleClickOutside)
  }
})
</script>