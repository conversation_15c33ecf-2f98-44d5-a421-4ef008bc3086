import { reactive, computed } from 'vue'
import { isAuthenticated, getUser, setUser, setAuthToken, removeAuthToken, removeUser, setRefreshToken, removeRefreshToken } from '@/lib/auth'
import { authApi } from '@/lib/api'
import { tokenManager } from '@/lib/token-manager'
import type { User } from '@/types/api'

// 全局认证状态
const authState = reactive({
  isAuthenticated: false,
  user: null as User | null,
  isLoading: false,
  isInitialized: false
})

// Storage 事件监听器（用于多标签页同步）
let storageListener: ((e: StorageEvent) => void) | null = null

// Token检查定时器
let tokenCheckTimer: NodeJS.Timeout | null = null

export function useAuth() {
  // 检查认证状态
  const checkAuth = () => {
    if (typeof window === 'undefined') return false
    
    const authenticated = isAuthenticated()
    const user = getUser()
    
    authState.isAuthenticated = authenticated
    authState.user = user
    authState.isInitialized = true
    
    return authenticated
  }

  // 登录
  const login = (user: User, token: string, refreshToken?: string) => {
    setAuthToken(token)
    setUser(user)
    if (refreshToken) {
      setRefreshToken(refreshToken)
    }
    authState.isAuthenticated = true
    authState.user = user
  }

  // 登出
  const logout = async () => {
    authState.isLoading = true
    
    try {
      // 调用后端登出接口
      await authApi.logout()
    } catch (error) {
      console.warn('后端登出失败，继续清理本地状态:', error)
    } finally {
      // 清理本地状态
      removeAuthToken()
      removeRefreshToken()
      removeUser()
      localStorage.removeItem('isLoggedIn') // 保持兼容性
      
      authState.isAuthenticated = false
      authState.user = null
      authState.isLoading = false
      
      // 重定向到登录页面
      if (typeof window !== 'undefined') {
        window.location.href = '/login'
      }
    }
  }

  // 刷新用户信息
  const refreshUserInfo = async () => {
    if (!authState.isAuthenticated) return

    authState.isLoading = true

    try {
      const response = await authApi.getProfile()
      // 从响应中提取用户数据
      const userData = (response as any).data || response
      setUser(userData)
      authState.user = userData
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，执行登出
      await logout()
    } finally {
      authState.isLoading = false
    }
  }

  // 重定向到登录页面
  const redirectToLogin = (returnUrl?: string) => {
    if (typeof window === 'undefined') return
    
    const loginUrl = returnUrl 
      ? `/login?redirect=${encodeURIComponent(returnUrl)}`
      : '/login'
    
    window.location.href = loginUrl
  }

  // 初始化认证状态
  const initAuth = () => {
    if (typeof window === 'undefined') return

    checkAuth()

    // 监听 localStorage 变化（多标签页同步）
    if (!storageListener) {
      storageListener = (e: StorageEvent) => {
        if (e.key === 'auth_token' || e.key === 'user') {
          checkAuth()
        }
      }
      window.addEventListener('storage', storageListener)
    }

    // 启动token检查定时器（每5分钟检查一次）
    if (!tokenCheckTimer && authState.isAuthenticated) {
      tokenCheckTimer = setInterval(() => {
        tokenManager.checkAndRefreshToken()
      }, 5 * 60 * 1000) // 5分钟
    }
  }

  // 清理监听器
  const cleanup = () => {
    if (storageListener && typeof window !== 'undefined') {
      window.removeEventListener('storage', storageListener)
      storageListener = null
    }

    if (tokenCheckTimer) {
      clearInterval(tokenCheckTimer)
      tokenCheckTimer = null
    }
  }

  // 计算属性
  const isLoggedIn = computed(() => authState.isAuthenticated)
  const currentUser = computed(() => authState.user)
  const isLoading = computed(() => authState.isLoading)
  const isInitialized = computed(() => authState.isInitialized)

  return {
    // 状态
    isLoggedIn,
    currentUser,
    isLoading,
    isInitialized,
    
    // 方法
    checkAuth,
    login,
    logout,
    refreshUserInfo,
    redirectToLogin,
    initAuth,
    cleanup
  }
}
