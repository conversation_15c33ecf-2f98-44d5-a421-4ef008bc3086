import { reactive } from 'vue'

export interface ToastOptions {
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  position?: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right'
  duration?: number // 显示时长，单位毫秒
}

export interface ToastState {
  visible: boolean
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  position: 'top-center' | 'top-right' | 'bottom-center' | 'bottom-right'
}

// 全局Toast状态
const toastState = reactive<ToastState>({
  visible: false,
  message: '',
  type: 'success',
  position: 'bottom-right'
})

let hideTimer: NodeJS.Timeout | null = null

export function useToast() {
  
  // 显示Toast
  const showToast = (options: ToastOptions) => {
    // 清除之前的定时器
    if (hideTimer) {
      clearTimeout(hideTimer)
      hideTimer = null
    }

    // 更新Toast状态
    toastState.visible = true
    toastState.message = options.message
    toastState.type = options.type || 'success'
    toastState.position = options.position || 'bottom-right'

    // 设置自动隐藏
    const duration = options.duration || 2000
    hideTimer = setTimeout(() => {
      hideToast()
    }, duration)
  }

  // 隐藏Toast
  const hideToast = () => {
    toastState.visible = false
    if (hideTimer) {
      clearTimeout(hideTimer)
      hideTimer = null
    }
  }

  // 便捷方法
  const showSuccess = (message: string, options?: Omit<ToastOptions, 'message' | 'type'>) => {
    showToast({ ...options, message, type: 'success' })
  }

  const showError = (message: string, options?: Omit<ToastOptions, 'message' | 'type'>) => {
    showToast({ ...options, message, type: 'error' })
  }

  const showWarning = (message: string, options?: Omit<ToastOptions, 'message' | 'type'>) => {
    showToast({ ...options, message, type: 'warning' })
  }

  const showInfo = (message: string, options?: Omit<ToastOptions, 'message' | 'type'>) => {
    showToast({ ...options, message, type: 'info' })
  }

  return {
    // 状态
    toastState,
    
    // 方法
    showToast,
    hideToast,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}
