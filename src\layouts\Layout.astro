---
import "../styles/global.css";
import ToastContainer from '@/components/ui/ToastContainer.vue'

export interface Props {
title: string;
description?: string;
}

const { title, description = "专业的网络安全威胁情报平台，整合暗网、Telegram和人工情报，提供全面的勒索组织、攻击事件等威胁情报信息。" } = Astro.props;
---

<!doctype html>
<html lang="zh-CN" data-theme="black">

<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<meta name="generator" content={Astro.generator} />
	<meta name="description" content={description} />
	<meta name="keywords" content="威胁情报,网络安全,勒索软件,安全事件,暗网监控,APT,恶意软件分析" />
	<title>{title}</title>
</head>

<body>
	<slot />
	<ToastContainer client:load />
</body>

</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>