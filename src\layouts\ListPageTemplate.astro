---
import Layout from '@/layouts/Layout.astro'
import Header from '@/components/vue/Header.vue'
import Footer4Col from '@/components/react/Footer4Col.tsx'
import UniversalSearch from '@/components/sections/UniversalSearch.vue'

interface PageConfig {
  title: string
  pageTitle: string
  description: string
  stats: any[]
  gridCols?: string
}

interface Props {
  config: PageConfig
}

const { config } = Astro.props
---

<Layout title={config.title}>
  <Header client:load />
  <main class="min-h-screen bg-base-100 pt-16 lg:pt-20">
    <!-- 通用搜索组件 -->
    <UniversalSearch client:load />

    <!-- 内容列表 -->
    <slot />
  </main>
  <Footer4Col client:load />
</Layout>
