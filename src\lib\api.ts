/**
 * API模块统一入口 - 向后兼容版本
 * 
 * 此文件保持与原有API接口完全兼容，现有代码无需修改
 * 新的模块化API位于 src/lib/api/ 目录下
 */

// 重新导出所有API，保持向后兼容
export {
  authApi,
  threatIntelligenceApi,
  vulnerabilityApi,
  securityEventApi,
  ransomwareGroupApi,
  negotiationRecordApi,
  victimApi,
  dashboardApi,
  blogApi,
  searchApi,
  systemApi,
  toolsApi,
  // 导出HTTP客户端函数，供高级用户使用
  request,
  requestWithoutAuth,
  buildQueryParams
} from './api/index';
