/**
 * 数据分析和仪表板API
 */

import type { DashboardOverview } from '@/types/api';
import { get } from './http-client';

/**
 * 仪表板API接口
 */
export interface DashboardApi {
  // 获取概览数据
  getOverview(): Promise<DashboardOverview>;
  
  // 获取威胁趋势
  getThreatTrends(): Promise<any>;
  
  // 获取漏洞趋势
  getVulnerabilityTrends(): Promise<any>;
  
  // 获取安全事件趋势
  getSecurityEventTrends(): Promise<any>;
  
  // 获取系统性能数据
  getSystemPerformance(): Promise<any>;
}

/**
 * 仪表板API实现
 */
export const dashboardApi: DashboardApi = {
  // 获取概览数据
  getOverview: () =>
    get<DashboardOverview>('/dashboard/overview'),

  // 获取威胁趋势
  getThreatTrends: () =>
    get<any>('/dashboard/threat-trends'),

  // 获取漏洞趋势
  getVulnerabilityTrends: () =>
    get<any>('/dashboard/vulnerability-trends'),

  // 获取安全事件趋势
  getSecurityEventTrends: () =>
    get<any>('/dashboard/security-event-trends'),

  // 获取系统性能数据
  getSystemPerformance: () =>
    get<any>('/dashboard/system-performance'),
};
