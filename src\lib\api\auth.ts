/**
 * 认证与用户管理API
 */

import type { User } from '@/types/api';
import { request, requestWithoutAuth, post, patch } from './http-client';

/**
 * 认证API接口
 */
export interface AuthApi {
  // 用户名密码登录
  login(credentials: { username: string; password: string }): Promise<{ auth_token: string }>;

  // 手机验证码登录
  phoneLogin(data: { phone: string; verification_code: string }): Promise<{ auth_token: string; user: User }>;

  // 微信登录
  wechatLogin(data: { code: string }): Promise<{ auth_token: string; user: User }>;

  // 发送验证码
  sendVerificationCode(data: { phone: string; code_type: 'register' | 'login' }): Promise<{ success: boolean; message: string }>;

  // 用户注册
  register(data: {
    username: string;
    company_name: string;
    phone: string;
    password: string;
    confirm_password: string;
    verification_code: string;
    agree_terms: boolean;
    agree_newsletter?: boolean;
  }): Promise<User>;

  // 退出登录
  logout(): Promise<void>;

  // 刷新Token
  refreshToken(refreshToken: string): Promise<{
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  }>;

  // 获取当前用户信息
  getProfile(): Promise<User>;

  // 更新用户信息
  updateProfile(data: Partial<User>): Promise<User>;

  // 更新用户密码
  updatePassword(data: { old_password: string; new_password: string; confirm_password: string }): Promise<void>;

  // 上传用户头像
  uploadAvatar(file: File): Promise<User>;

  // 检查用户名可用性
  checkUsername(username: string): Promise<{ available: boolean }>;

  // 检查手机号可用性
  checkPhone(phone: string): Promise<{ available: boolean }>;
}

/**
 * 认证API实现
 */
export const authApi: AuthApi = {
  // 用户名密码登录
  login: (credentials: { username: string; password: string }) =>
    requestWithoutAuth<{ auth_token: string }>('/auth/token/login/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    }),

  // 手机验证码登录
  phoneLogin: (data: { phone: string; verification_code: string }) =>
    requestWithoutAuth<{ auth_token: string; user: User }>('/auth/phone-login/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // 微信登录
  wechatLogin: (data: { code: string }) =>
    requestWithoutAuth<{ auth_token: string; user: User }>('/auth/wechat-login/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // 发送验证码
  sendVerificationCode: (data: { phone: string; code_type: 'register' | 'login' }) =>
    requestWithoutAuth<{ success: boolean; message: string }>('/auth/send-verification-code/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // 用户注册
  register: (data: {
    username: string;
    company_name: string;
    phone: string;
    password: string;
    confirm_password: string;
    verification_code: string;
    agree_terms: boolean;
    agree_newsletter?: boolean;
  }) =>
    requestWithoutAuth<User>('/auth/register/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // 退出登录
  logout: () =>
    post<void>('/auth/token/logout/'),

  // 刷新Token
  refreshToken: (refreshToken: string) =>
    requestWithoutAuth<{
      access_token: string;
      refresh_token: string;
      token_type: string;
      expires_in: number;
    }>('/auth/token/refresh/', {
      method: 'POST',
      body: JSON.stringify({ refresh_token: refreshToken }),
    }),

  // 获取当前用户信息
  getProfile: () =>
    request<User>('/auth/users/me/'),

  // 更新用户信息
  updateProfile: (data: Partial<User>) =>
    patch<User>('/auth/users/me/', data),

  // 更新用户密码
  updatePassword: (data: { old_password: string; new_password: string; confirm_password: string }) =>
    post<void>('/auth/users/set_password/', data),

  // 上传用户头像
  uploadAvatar: async (file: File): Promise<User> => {
    // 创建FormData对象
    const formData = new FormData();
    formData.append('avatar', file);

    // 获取存储的token
    const token = localStorage.getItem('auth_token');
    const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

    // 直接调用文件上传API
    const response = await fetch(`${API_BASE_URL}/auth/users/me/`, {
      method: 'PATCH',
      headers: {
        ...(token && { 'Authorization': `Bearer ${token}` }),
        // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result.data || result;
  },

  // 检查用户名可用性
  checkUsername: (username: string) =>
    request<{ available: boolean }>(`/auth/check-username/?username=${encodeURIComponent(username)}`),

  // 检查手机号可用性
  checkPhone: (phone: string) =>
    request<{ available: boolean }>(`/auth/check-phone/?phone=${encodeURIComponent(phone)}`),
};
