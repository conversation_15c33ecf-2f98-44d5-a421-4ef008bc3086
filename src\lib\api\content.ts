/**
 * 内容管理API（博客等）
 */

import type { BlogPost, BlogCategory, BlogTag, BlogApiResponse } from '@/types/api';
import type { BlogParams } from './types';
import { get, buildQueryParams } from './http-client';

/**
 * 博客API接口
 */
export interface BlogApi {
  // 获取文章列表
  getPosts(params?: BlogParams): Promise<BlogApiResponse<BlogPost[]>>;
  
  // 获取文章详情
  getPost(id: number): Promise<BlogApiResponse<BlogPost>>;
  
  // 搜索文章
  searchPosts(query: string, params?: BlogParams): Promise<BlogApiResponse<BlogPost[]>>;
  
  // 获取分类列表
  getCategories(): Promise<BlogApiResponse<BlogCategory[]>>;
  
  // 获取分类详情
  getCategory(slug: string): Promise<BlogApiResponse<BlogCategory>>;
  
  // 获取分类下的文章
  getCategoryPosts(slug: string, params?: BlogParams): Promise<BlogApiResponse<BlogPost[]>>;
  
  // 获取标签列表
  getTags(): Promise<BlogApiResponse<BlogTag[]>>;
  
  // 获取标签详情
  getTag(slug: string): Promise<BlogApiResponse<BlogTag>>;
  
  // 获取标签下的文章
  getTagPosts(slug: string, params?: BlogParams): Promise<BlogApiResponse<BlogPost[]>>;
}

/**
 * 博客API实现
 */
export const blogApi: BlogApi = {
  // 获取文章列表
  getPosts: (params?: BlogParams) => {
    const queryString = params ? buildQueryParams(params) : '';
    const url = queryString ? `/blog/posts/?${queryString}` : '/blog/posts/';
    return get<BlogApiResponse<BlogPost[]>>(url);
  },

  // 获取文章详情
  getPost: (id: number) =>
    get<BlogApiResponse<BlogPost>>(`/blog/posts/${id}/`),

  // 搜索文章
  searchPosts: (query: string, params?: BlogParams) => {
    const queryString = params ? buildQueryParams(params) : '';
    const url = `/blog/posts/search/?q=${encodeURIComponent(query)}${queryString ? '&' + queryString : ''}`;
    return get<BlogApiResponse<BlogPost[]>>(url);
  },

  // 获取分类列表
  getCategories: () =>
    get<BlogApiResponse<BlogCategory[]>>('/blog/categories/'),

  // 获取分类详情
  getCategory: (slug: string) =>
    get<BlogApiResponse<BlogCategory>>(`/blog/categories/${slug}/`),

  // 获取分类下的文章
  getCategoryPosts: (slug: string, params?: BlogParams) => {
    const queryString = params ? buildQueryParams(params) : '';
    const url = queryString ? `/blog/categories/${slug}/posts/?${queryString}` : `/blog/categories/${slug}/posts/`;
    return get<BlogApiResponse<BlogPost[]>>(url);
  },

  // 获取标签列表
  getTags: () =>
    get<BlogApiResponse<BlogTag[]>>('/blog/tags/'),

  // 获取标签详情
  getTag: (slug: string) =>
    get<BlogApiResponse<BlogTag>>(`/blog/tags/${slug}/`),

  // 获取标签下的文章
  getTagPosts: (slug: string, params?: BlogParams) => {
    const queryString = params ? buildQueryParams(params) : '';
    const url = queryString ? `/blog/tags/${slug}/posts/?${queryString}` : `/blog/tags/${slug}/posts/`;
    return get<BlogApiResponse<BlogPost[]>>(url);
  },
};
