/**
 * API模块统一入口
 * 
 * 提供两种使用方式：
 * 1. 向后兼容的方式：import { threatIntelligenceApi, authApi } from '@/lib/api'
 * 2. 模块化的方式：import { authApi } from '@/lib/api/auth'
 */

// 导出所有API模块
export { authApi } from './auth';
export { threatIntelligenceApi } from './threat-intelligence';
export {
  vulnerabilityApi,
  securityEventApi,
  ransomwareGroupApi,
  negotiationRecordApi,
  victimApi
} from './security';
export { dashboardApi } from './analytics';
export { blogApi } from './content';
export { searchApi, systemApi } from './system';
export { toolsApi } from './tools';

// 导出HTTP客户端工具函数
export { 
  request, 
  requestWithoutAuth, 
  get, 
  post, 
  put, 
  patch, 
  del,
  buildQueryParams 
} from './http-client';

// 导出类型定义
export type {
  HttpMethod,
  RequestConfig,
  PaginationParams,
  SearchParams,
  FilterParams,
  TimeRangeParams,
  ApiError,
  ThreatIntelligenceParams,
  RansomwareGroupParams,
  BlogParams
} from './types';

// 导出API接口类型
export type { AuthApi } from './auth';
export type { ThreatIntelligenceApi } from './threat-intelligence';
export type {
  VulnerabilityApi,
  SecurityEventApi,
  RansomwareGroupApi,
  VictimApi
} from './security';
export type { DashboardApi } from './analytics';
export type { BlogApi } from './content';
export type { SearchApi, SystemApi } from './system';
export type { ToolsApi } from './tools';
