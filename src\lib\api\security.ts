/**
 * 安全管理API（漏洞、安全事件、勒索软件组织）
 */

import type {
  Vulnerability,
  SecurityEvent,
  RansomwareGroup,
  NegotiationRecord,
  Victim,
  PaginatedResponse
} from '@/types/api';
import type { FilterParams, RansomwareGroupParams } from './types';
import { request, get, put, buildQueryParams } from './http-client';

/**
 * 漏洞管理API接口
 */
export interface VulnerabilityApi {
  // 获取漏洞列表
  getList(params?: FilterParams): Promise<PaginatedResponse<Vulnerability>>;
  
  // 获取漏洞详情
  getDetail(id: number): Promise<Vulnerability>;
  
  // 获取统计数据
  getStats(): Promise<any>;
}

/**
 * 安全事件API接口
 */
export interface SecurityEventApi {
  // 获取安全事件列表
  getList(params?: FilterParams): Promise<PaginatedResponse<SecurityEvent>>;
  
  // 获取安全事件详情
  getDetail(id: number): Promise<SecurityEvent>;
  
  // 更新事件状态
  updateStatus(id: number, status: string, analyst?: string): Promise<any>;
  
  // 获取统计数据
  getStats(): Promise<any>;
}

/**
 * 勒索软件组织API接口
 */
export interface RansomwareGroupApi {
  // 获取勒索软件组织列表
  getList(params?: RansomwareGroupParams): Promise<PaginatedResponse<RansomwareGroup>>;

  // 获取勒索软件组织详情（完整数据，保持向后兼容）
  getDetail(id: string): Promise<RansomwareGroup>;

  // 获取基础信息
  getBasicInfo(id: string): Promise<RansomwareGroup>;

  // 获取工具信息（支持分页）
  getTools(id: string, params?: { page?: number; page_size?: number }): Promise<{
    tools: any[];
    pagination: {
      current_page: number;
      page_size: number;
      total_count: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  }>;

  // 获取谈判记录（支持分页）
  getNegotiations(id: string, params?: { page?: number; page_size?: number }): Promise<{
    negotiation_records: any[];
    pagination: {
      current_page: number;
      page_size: number;
      total_count: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  }>;

  // 获取勒索信（支持分页）
  getRansomNotes(id: string, params?: { page?: number; page_size?: number }): Promise<{
    ransom_notes: any[];
    pagination: {
      current_page: number;
      page_size: number;
      total_count: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  }>;

  // 获取勒索信详情
  getRansomNoteDetail(noteId: number): Promise<any>;

  // 获取IOC指标
  getIOCIndicators(id: string): Promise<{ ioc_indicators: any[] }>;

  // 获取受害者信息（支持分页）
  getVictims(id: string, params?: { page?: number; page_size?: number }): Promise<{
    victims: any[];
    pagination: {
      current_page: number;
      page_size: number;
      total_count: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  }>;

  // 获取统计数据
  getStats(): Promise<any>;
}

/**
 * 谈判记录API接口
 */
export interface NegotiationRecordApi {
  // 获取谈判记录列表
  getList(params?: FilterParams): Promise<PaginatedResponse<NegotiationRecord>>;

  // 获取谈判记录详情
  getDetail(id: number): Promise<NegotiationRecord>;

  // 创建谈判记录
  create(data: Partial<NegotiationRecord>): Promise<NegotiationRecord>;

  // 更新谈判记录
  update(id: number, data: Partial<NegotiationRecord>): Promise<NegotiationRecord>;

  // 删除谈判记录
  delete(id: number): Promise<void>;

  // 获取统计数据
  getStats(): Promise<any>;
}

/**
 * 谈判记录API接口
 */
export interface NegotiationRecordApi {
  // 获取谈判记录列表
  getList(params?: FilterParams): Promise<PaginatedResponse<NegotiationRecord>>;

  // 获取谈判记录详情
  getDetail(id: number): Promise<NegotiationRecord>;

  // 创建谈判记录
  create(data: Partial<NegotiationRecord>): Promise<NegotiationRecord>;

  // 更新谈判记录
  update(id: number, data: Partial<NegotiationRecord>): Promise<NegotiationRecord>;

  // 删除谈判记录
  delete(id: number): Promise<void>;

  // 获取统计数据
  getStats(): Promise<any>;
}

/**
 * 受害者API接口
 */
export interface VictimApi {
  // 获取受害者列表
  getList(params?: FilterParams): Promise<PaginatedResponse<Victim>>;

  // 获取受害者详情
  getDetail(id: number): Promise<Victim>;

  // 获取统计数据
  getStats(): Promise<any>;
}

/**
 * 漏洞管理API实现
 */
export const vulnerabilityApi: VulnerabilityApi = {
  // 获取漏洞列表
  getList: (params?: FilterParams) =>
    get<PaginatedResponse<Vulnerability>>('/vulnerabilities', params),

  // 获取漏洞详情
  getDetail: (id: number) =>
    get<Vulnerability>(`/vulnerabilities/${id}`),

  // 获取统计数据
  getStats: () =>
    get<any>('/vulnerabilities/stats'),
};

/**
 * 安全事件API实现
 */
export const securityEventApi: SecurityEventApi = {
  // 获取安全事件列表
  getList: (params?: FilterParams) =>
    get<PaginatedResponse<SecurityEvent>>('/security-events', params),

  // 获取安全事件详情
  getDetail: (id: number) =>
    get<SecurityEvent>(`/security-events/${id}`),

  // 更新事件状态
  updateStatus: (id: number, status: string, analyst?: string) =>
    put<any>(`/security-events/${id}/status`, { status, analyst }),

  // 获取统计数据
  getStats: () =>
    get<any>('/security-events/stats'),
};

/**
 * 勒索软件组织API实现
 */
export const ransomwareGroupApi: RansomwareGroupApi = {
  // 获取勒索软件组织列表
  getList: async (params?: RansomwareGroupParams) => {
    const queryString = params ? buildQueryParams(params) : '';
    const url = queryString ? `/groups/?${queryString}` : '/groups/';

    const response = await get<{
      success: boolean;
      message: string;
      data: RansomwareGroup[];
      pagination?: {
        count: number;
        page: number;
        page_size: number;
        total_pages: number;
        has_next: boolean;
        has_previous: boolean;
        next_page: number | null;
        previous_page: number | null;
      };
    }>(url);

    // 转换为前端期望的格式
    if (response.pagination) {
      return {
        list: response.data,
        total: response.pagination.count,
        page: response.pagination.page,
        pageSize: response.pagination.page_size
      };
    } else {
      return {
        list: response.data,
        total: response.data.length,
        page: 1,
        pageSize: response.data.length
      };
    }
  },

  // 获取勒索软件组织详情（完整数据，保持向后兼容）
  getDetail: async (id: string) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: RansomwareGroup;
    }>(`/groups/${id}/`);

    return response.data;
  },

  // 获取基础信息
  getBasicInfo: async (id: string) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: RansomwareGroup;
    }>(`/groups/${id}/basic/`);

    return response.data;
  },

  // 获取工具信息（支持分页）
  getTools: async (id: string, params?: { page?: number; page_size?: number }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const url = `/groups/${id}/tools/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    const response = await get<{
      success: boolean;
      message: string;
      data: {
        tools: any[];
        pagination: {
          current_page: number;
          page_size: number;
          total_count: number;
          total_pages: number;
          has_next: boolean;
          has_prev: boolean;
        };
      };
    }>(url);

    return response.data;
  },

  // 获取谈判记录（支持分页）
  getNegotiations: async (id: string, params?: { page?: number; page_size?: number }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const url = `/groups/${id}/negotiations/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    const response = await get<{
      success: boolean;
      message: string;
      data: {
        negotiation_records: any[];
        pagination: {
          current_page: number;
          page_size: number;
          total_count: number;
          total_pages: number;
          has_next: boolean;
          has_prev: boolean;
        };
      };
    }>(url);

    return response.data;
  },

  // 获取勒索信（支持分页）
  getRansomNotes: async (id: string, params?: { page?: number; page_size?: number }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const url = `/groups/${id}/ransom-notes/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    const response = await get<{
      success: boolean;
      message: string;
      data: {
        ransom_notes: any[];
        pagination: {
          current_page: number;
          page_size: number;
          total_count: number;
          total_pages: number;
          has_next: boolean;
          has_prev: boolean;
        };
      };
    }>(url);

    return response.data;
  },

  // 获取IOC指标
  getIOCIndicators: async (id: string) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: { ioc_indicators: any[] };
    }>(`/groups/${id}/ioc-indicators/`);

    return response.data;
  },

  // 获取受害者信息（支持分页）
  getVictims: async (id: string, params?: { page?: number; page_size?: number }) => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const url = `/groups/${id}/victims/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    const response = await get<{
      success: boolean;
      message: string;
      data: {
        victims: any[];
        pagination: {
          current_page: number;
          page_size: number;
          total_count: number;
          total_pages: number;
          has_next: boolean;
          has_prev: boolean;
        };
      };
    }>(url);

    return response.data;
  },

  // 获取勒索信详情
  getRansomNoteDetail: async (noteId: number) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: any;
    }>(`/ransom-notes/${noteId}/`);

    return response.data;
  },

  // 获取统计数据
  getStats: async () => {
    const response = await get<{
      success: boolean;
      message: string;
      data: any;
    }>('/groups/stats/');

    return response.data;
  },
};

/**
 * 谈判记录API实现
 */
export const negotiationRecordApi: NegotiationRecordApi = {
  // 获取谈判记录列表
  getList: async (params?: FilterParams) => {
    const queryString = params ? buildQueryParams(params) : '';
    const url = queryString ? `/negotiation-records/?${queryString}` : '/negotiation-records/';

    const response = await get<{
      success: boolean;
      message: string;
      data: NegotiationRecord[];
      pagination?: {
        count: number;
        page: number;
        page_size: number;
        total_pages: number;
        has_next: boolean;
        has_previous: boolean;
        next_page: number | null;
        previous_page: number | null;
      };
    }>(url);

    // 转换为前端期望的格式
    if (response.pagination) {
      return {
        list: response.data,
        total: response.pagination.count,
        page: response.pagination.page,
        pageSize: response.pagination.page_size
      };
    } else {
      return {
        list: response.data,
        total: response.data.length,
        page: 1,
        pageSize: response.data.length
      };
    }
  },

  // 获取谈判记录详情
  getDetail: async (id: number) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: NegotiationRecord;
    }>(`/negotiation-records/${id}/`);

    return response.data;
  },

  // 创建谈判记录
  create: async (data: Partial<NegotiationRecord>) => {
    const response = await request<{
      success: boolean;
      message: string;
      data: NegotiationRecord;
    }>('/negotiation-records/', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    return response.data;
  },

  // 更新谈判记录
  update: async (id: number, data: Partial<NegotiationRecord>) => {
    const response = await put<{
      success: boolean;
      message: string;
      data: NegotiationRecord;
    }>(`/negotiation-records/${id}/`, data);

    return response.data;
  },

  // 删除谈判记录
  delete: async (id: number) => {
    await request<{
      success: boolean;
      message: string;
    }>(`/negotiation-records/${id}/`, {
      method: 'DELETE',
    });
  },

  // 获取统计数据
  getStats: async () => {
    const response = await get<{
      success: boolean;
      message: string;
      data: any;
    }>('/negotiation-records/stats/');

    return response.data;
  },
};

/**
 * 受害者API实现
 */
export const victimApi: VictimApi = {
  // 获取受害者列表
  getList: async (params?: FilterParams) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: {
        list: Victim[];
        total: number;
        page: number;
        pageSize: number;
      };
    }>('/victims/', params);

    return response.data;
  },

  // 获取受害者详情
  getDetail: async (id: number) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: Victim;
    }>(`/victims/${id}/`);

    return response.data;
  },

  // 获取统计数据
  getStats: async () => {
    const response = await get<{
      success: boolean;
      message: string;
      data: any;
    }>('/victims/stats/');

    return response.data;
  },
};
