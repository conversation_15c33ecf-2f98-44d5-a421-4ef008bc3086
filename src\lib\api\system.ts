/**
 * 搜索和系统功能API
 */

import type { PaginatedResponse } from '@/types/api';
import type {
  SearchParams,
  SearchRequestParams,
  SearchResponse,
  SearchSuggestion,
  PopularSearch
} from './types';
import { get, post, buildQueryParams } from './http-client';

/**
 * 搜索API接口
 */
export interface SearchApi {
  // 统一全文搜索
  search(params: SearchRequestParams): Promise<SearchResponse>;

  // 搜索建议
  suggestions(query: string, limit?: number): Promise<SearchSuggestion[]>;

  // 热门搜索
  popular(limit?: number): Promise<PopularSearch[]>;
}

/**
 * 系统API接口
 */
export interface SystemApi {
  // 健康检查
  health(): Promise<{ status: string; timestamp: string; version: string }>;
}

/**
 * 搜索API实现
 */
export const searchApi: SearchApi = {
  // 统一全文搜索
  search: (params: SearchRequestParams) => {
    const queryString = buildQueryParams(params);
    return get<SearchResponse>(`/search/?${queryString}`);
  },

  // 搜索建议
  suggestions: (query: string, limit = 5) => {
    const params = { q: query, limit };
    const queryString = buildQueryParams(params);
    return get<SearchSuggestion[]>(`/search/suggestions/?${queryString}`);
  },

  // 热门搜索
  popular: (limit = 10) => {
    const params = { limit };
    const queryString = buildQueryParams(params);
    return get<PopularSearch[]>(`/search/popular/?${queryString}`);
  },
};

/**
 * 系统API实现
 */
export const systemApi: SystemApi = {
  // 健康检查
  health: () =>
    get<{ status: string; timestamp: string; version: string }>('/health'),
};
