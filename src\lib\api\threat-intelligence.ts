/**
 * 威胁情报管理API
 */

import type { ThreatIntelligence, PaginatedResponse } from '@/types/api';
import type { ThreatIntelligenceParams } from './types';
import { request, get, post, put, del, buildQueryParams } from './http-client';

/**
 * 威胁情报API接口
 */
export interface ThreatIntelligenceApi {
  // 获取威胁情报列表
  getList(params?: ThreatIntelligenceParams): Promise<PaginatedResponse<ThreatIntelligence>>;
  
  // 获取威胁情报详情
  getDetail(id: number): Promise<ThreatIntelligence>;
  
  // 创建威胁情报
  create(data: Partial<ThreatIntelligence>): Promise<ThreatIntelligence>;
  
  // 更新威胁情报
  update(id: number, data: Partial<ThreatIntelligence>): Promise<ThreatIntelligence>;
  
  // 删除威胁情报
  delete(id: number): Promise<void>;
  
  // 获取统计数据
  getStats(): Promise<any>;
}

/**
 * 威胁情报API实现
 */
export const threatIntelligenceApi: ThreatIntelligenceApi = {
  // 获取威胁情报列表
  getList: (params?: ThreatIntelligenceParams) => {
    if (!params) {
      return get<PaginatedResponse<ThreatIntelligence>>('/intell/posts/');
    }

    const queryString = buildQueryParams(params);
    const url = queryString ? `/intell/posts/?${queryString}` : '/intell/posts/';

    return get<PaginatedResponse<ThreatIntelligence>>(url);
  },

  // 获取威胁情报详情
  getDetail: (id: number) =>
    get<ThreatIntelligence>(`/intell/posts/${id}/`),

  // 创建威胁情报
  create: (data: Partial<ThreatIntelligence>) =>
    post<ThreatIntelligence>('/intell/posts/', data),

  // 更新威胁情报
  update: (id: number, data: Partial<ThreatIntelligence>) =>
    put<ThreatIntelligence>(`/intell/posts/${id}/`, data),

  // 删除威胁情报
  delete: (id: number) =>
    del<void>(`/intell/posts/${id}/`),

  // 获取统计数据
  getStats: () =>
    get<any>('/intell/posts/stats/'),
};
