/**
 * 应急工具管理API
 */

import type { Tool, PaginatedResponse } from '@/types/api';
import type { FilterParams } from './types';
import { get, buildQueryParams } from './http-client';

/**
 * 工具API接口
 */
export interface ToolsApi {
  // 获取工具列表
  getList(params?: FilterParams & {
    ransomware_group?: number;
    search?: string;
    has_file?: boolean;
  }): Promise<PaginatedResponse<Tool>>;
  
  // 获取工具详情
  getDetail(id: number): Promise<Tool>;
  
  // 获取统计数据
  getStats(): Promise<{
    total: number;
    with_files: number;
    with_docs: number;
    recently_updated: number;
  }>;
  
  // 根据勒索组织获取工具列表
  getByRansomwareGroup(groupId: number): Promise<Tool[]>;
}

/**
 * 工具API实现
 */
export const toolsApi: ToolsApi = {
  // 获取工具列表
  getList: async (params?: FilterParams & {
    ransomware_group?: number;
    search?: string;
    has_file?: boolean;
  }) => {
    const queryString = params ? buildQueryParams(params) : '';
    const url = queryString ? `/tools/?${queryString}` : '/tools/';

    const response = await get<{
      success: boolean;
      message: string;
      data: Tool[];
      pagination?: {
        count: number;
        page: number;
        page_size: number;
        total_pages: number;
        has_next: boolean;
        has_previous: boolean;
        next_page: number | null;
        previous_page: number | null;
      };
    }>(url);

    // 转换为前端期望的格式
    if (response.pagination) {
      return {
        list: response.data,
        total: response.pagination.count,
        page: response.pagination.page,
        pageSize: response.pagination.page_size
      };
    } else {
      return {
        list: response.data,
        total: response.data.length,
        page: 1,
        pageSize: response.data.length
      };
    }
  },

  // 获取工具详情
  getDetail: async (id: number) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: Tool;
    }>(`/tools/${id}/`);

    return response.data;
  },

  // 获取统计数据
  getStats: async () => {
    const response = await get<{
      success: boolean;
      message: string;
      data: {
        total: number;
        with_files: number;
        with_docs: number;
        recently_updated: number;
      };
    }>('/tools/stats/');

    return response.data;
  },

  // 根据勒索组织获取工具列表
  getByRansomwareGroup: async (groupId: number) => {
    const response = await get<{
      success: boolean;
      message: string;
      data: Tool[];
    }>(`/groups/${groupId}/tools/`);

    return response.data;
  },
};
