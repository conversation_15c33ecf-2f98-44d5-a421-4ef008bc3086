/**
 * API模块内部使用的通用类型定义
 */

// HTTP方法枚举
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// 请求配置接口
export interface RequestConfig extends RequestInit {
  params?: Record<string, any>;
  timeout?: number;
}

// 重试请求配置接口
export interface RetryRequestConfig {
  url: string;
  method?: string;
  headers?: Record<string, string>;
  body?: string;
  signal?: AbortSignal;
}

// 通用分页参数接口
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

// 通用搜索参数接口
export interface SearchParams extends PaginationParams {
  search?: string;
}

// 通用筛选参数接口
export interface FilterParams extends SearchParams {
  status?: string;
  severity?: string;
  type?: string;
}

// 时间范围参数接口
export interface TimeRangeParams {
  timeRange?: string;
  startDate?: string;
  endDate?: string;
}

// API错误类型
export interface ApiError {
  code: number;
  message: string;
  details?: any;
}

// 查询参数构建选项
export interface QueryParamsOptions {
  excludeEmpty?: boolean;
  excludeNull?: boolean;
  excludeUndefined?: boolean;
}

// 威胁情报查询参数
export interface ThreatIntelligenceParams extends FilterParams, TimeRangeParams {
  source?: string;
  confidenceMin?: number;
  confidenceMax?: number;
  category?: string;
}

// 勒索软件组织查询参数
export interface RansomwareGroupParams extends FilterParams {
  threat_level?: string;
  origin_country?: string;
  target_sector?: string;
  page_size?: number;
}

// 博客查询参数
export interface BlogParams extends PaginationParams {
  category?: string;
  search?: string;
  page_size?: number;
}

// 搜索请求参数
export interface SearchRequestParams {
  q: string;                    // 搜索关键词（必需）
  content_types?: string;       // 内容类型，逗号分隔（可选）
  page?: number;               // 页码（可选，默认1）
  page_size?: number;          // 每页大小（可选，默认20，最大100）
}

// 搜索结果项
export interface SearchResultItem {
  id?: number;                 // 兼容性字段
  content_type: string;        // 内容类型
  object_id: number;           // 对象ID
  title: string;               // 标题
  description: string;         // 描述
  url: string;                 // 详情页URL
  rank: number;                // 相关性排名
  highlight?: string;          // 高亮片段
  metadata?: Record<string, any>; // 额外元数据
  data: Record<string, any>;   // 对象数据
  created_at?: string;         // 创建时间
}

// 搜索分页信息
export interface SearchPagination {
  page: number;                // 当前页码
  page_size: number;           // 每页大小
  total_count: number;         // 总记录数
  total_pages: number;         // 总页数
  has_next: boolean;           // 是否有下一页
  has_previous: boolean;       // 是否有上一页
}

// 搜索响应
export interface SearchResponse {
  query: string;               // 搜索关键词
  results: SearchResultItem[]; // 搜索结果列表
  pagination: SearchPagination; // 分页信息
  content_types: Record<string, number>; // 各内容类型的结果数量
}

// 搜索建议项
export interface SearchSuggestion {
  suggestion: string;          // 建议的搜索词
  type: string;               // 建议类型（history等）
  count: number;              // 相关结果数量
}

// 热门搜索项
export interface PopularSearch {
  query: string;              // 搜索词
  search_count: number;       // 搜索次数
  result_count: number;       // 结果数量
}
