/**
 * 头像管理工具
 * 处理OSS头像链接过期检测、自动刷新和缓存管理
 */

import { authApi } from './api/auth';
import { getUser, setUser } from './auth';
import type { User } from '@/types/api';

// 缓存键名
const AVATAR_CACHE_KEY = 'avatar_cache';
const AVATAR_REFRESH_LOCK_KEY = 'avatar_refresh_lock';

// 缓存接口
interface AvatarCache {
  user: User;
  timestamp: number;
  expiresAt: number;
}

/**
 * 检测OSS URL是否过期
 * @param url OSS URL
 * @returns 是否过期
 */
export function isOSSUrlExpired(url: string): boolean {
  if (!url || !url.includes('Expires=')) {
    return false; // 非OSS链接或无过期参数，认为未过期
  }

  try {
    const urlObj = new URL(url);
    const expiresParam = urlObj.searchParams.get('Expires');
    
    if (!expiresParam) {
      return false;
    }

    const expiresTimestamp = parseInt(expiresParam) * 1000; // 转换为毫秒
    const currentTimestamp = Date.now();
    
    return currentTimestamp >= expiresTimestamp;
  } catch (error) {
    console.warn('解析OSS URL失败:', error);
    return false; // 解析失败时认为未过期，避免误判
  }
}

/**
 * 检测OSS URL是否即将过期
 * @param url OSS URL
 * @param minutesBefore 提前多少分钟判断为即将过期（默认5分钟）
 * @returns 是否即将过期
 */
export function isOSSUrlExpiringSoon(url: string, minutesBefore: number = 5): boolean {
  if (!url || !url.includes('Expires=')) {
    return false;
  }

  try {
    const urlObj = new URL(url);
    const expiresParam = urlObj.searchParams.get('Expires');
    
    if (!expiresParam) {
      return false;
    }

    const expiresTimestamp = parseInt(expiresParam) * 1000;
    const currentTimestamp = Date.now();
    const warningTimestamp = expiresTimestamp - (minutesBefore * 60 * 1000);
    
    return currentTimestamp >= warningTimestamp;
  } catch (error) {
    console.warn('解析OSS URL失败:', error);
    return false;
  }
}

/**
 * 头像管理器类
 */
export class AvatarManager {
  private static instance: AvatarManager;
  private refreshPromise: Promise<User | null> | null = null;
  private eventTarget = new EventTarget();

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): AvatarManager {
    if (!AvatarManager.instance) {
      AvatarManager.instance = new AvatarManager();
    }
    return AvatarManager.instance;
  }

  /**
   * 获取缓存的用户信息
   */
  private getCachedUser(): AvatarCache | null {
    try {
      const cached = sessionStorage.getItem(AVATAR_CACHE_KEY);
      if (!cached) return null;

      const cacheData: AvatarCache = JSON.parse(cached);
      
      // 检查缓存是否过期（30分钟）
      if (Date.now() > cacheData.expiresAt) {
        sessionStorage.removeItem(AVATAR_CACHE_KEY);
        return null;
      }

      return cacheData;
    } catch (error) {
      console.warn('读取头像缓存失败:', error);
      sessionStorage.removeItem(AVATAR_CACHE_KEY);
      return null;
    }
  }

  /**
   * 设置用户信息缓存
   */
  private setCachedUser(user: User): void {
    try {
      const cacheData: AvatarCache = {
        user,
        timestamp: Date.now(),
        expiresAt: Date.now() + (30 * 60 * 1000) // 30分钟后过期
      };
      
      sessionStorage.setItem(AVATAR_CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('设置头像缓存失败:', error);
    }
  }

  /**
   * 检查是否正在刷新中
   */
  private isRefreshing(): boolean {
    const lockTime = localStorage.getItem(AVATAR_REFRESH_LOCK_KEY);
    if (!lockTime) return false;

    const lockTimestamp = parseInt(lockTime);
    const now = Date.now();
    
    // 锁定时间超过30秒则认为已失效
    if (now - lockTimestamp > 30000) {
      localStorage.removeItem(AVATAR_REFRESH_LOCK_KEY);
      return false;
    }

    return true;
  }

  /**
   * 设置刷新锁
   */
  private setRefreshLock(): void {
    localStorage.setItem(AVATAR_REFRESH_LOCK_KEY, Date.now().toString());
  }

  /**
   * 清除刷新锁
   */
  private clearRefreshLock(): void {
    localStorage.removeItem(AVATAR_REFRESH_LOCK_KEY);
  }

  /**
   * 检查头像是否需要刷新
   */
  needsRefresh(user: User | null): boolean {
    if (!user?.avatar) return false;

    // 检查是否为OSS链接且即将过期
    return isOSSUrlExpiringSoon(user.avatar, 5);
  }

  /**
   * 刷新用户头像信息
   */
  async refreshUserAvatar(): Promise<User | null> {
    // 如果已经在刷新中，返回现有的Promise
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    // 检查是否有其他实例在刷新
    if (this.isRefreshing()) {
      console.log('其他实例正在刷新头像，跳过');
      return null;
    }

    this.refreshPromise = this.doRefreshUserAvatar();
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 执行实际的刷新操作
   */
  private async doRefreshUserAvatar(): Promise<User | null> {
    try {
      this.setRefreshLock();

      console.log('开始刷新用户头像信息...');

      // 调用API获取最新用户信息
      const response = await authApi.getProfile();

      // 从响应中提取用户数据
      const userData = (response as any).data || response;

      // 更新本地存储
      setUser(userData);

      // 更新缓存
      this.setCachedUser(userData);

      // 触发更新事件
      this.notifyAvatarUpdated(userData);

      console.log('用户头像信息刷新成功');
      return userData;

    } catch (error) {
      console.error('刷新用户头像信息失败:', error);
      return null;
    } finally {
      this.clearRefreshLock();
    }
  }

  /**
   * 获取有效的用户信息（优先使用缓存）
   */
  async getValidUser(): Promise<User | null> {
    // 1. 尝试从缓存获取
    const cached = this.getCachedUser();
    if (cached && !this.needsRefresh(cached.user)) {
      return cached.user;
    }

    // 2. 从本地存储获取
    const localUser = getUser();
    if (localUser && !this.needsRefresh(localUser)) {
      // 更新缓存
      this.setCachedUser(localUser);
      return localUser;
    }

    // 3. 需要刷新，尝试获取最新信息
    const refreshedUser = await this.refreshUserAvatar();
    return refreshedUser || localUser; // 刷新失败时返回本地用户信息
  }

  /**
   * 监听头像更新事件
   */
  onAvatarUpdated(callback: (user: User) => void): () => void {
    const handler = (event: Event) => {
      const customEvent = event as CustomEvent<User>;
      callback(customEvent.detail);
    };

    this.eventTarget.addEventListener('avatarUpdated', handler);
    
    // 返回清理函数
    return () => {
      this.eventTarget.removeEventListener('avatarUpdated', handler);
    };
  }

  /**
   * 通知头像已更新
   */
  notifyAvatarUpdated(user: User): void {
    const event = new CustomEvent('avatarUpdated', { detail: user });
    this.eventTarget.dispatchEvent(event);
    
    // 同时触发全局事件，供其他组件监听
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('userAvatarUpdated', { detail: user }));
    }
  }

  /**
   * 清理缓存和锁
   */
  cleanup(): void {
    sessionStorage.removeItem(AVATAR_CACHE_KEY);
    localStorage.removeItem(AVATAR_REFRESH_LOCK_KEY);
  }
}

// 导出单例实例
export const avatarManager = AvatarManager.getInstance();
