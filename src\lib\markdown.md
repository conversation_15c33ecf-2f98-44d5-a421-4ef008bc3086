# Markdown 解析工具使用文档

这个模块提供了统一的 Markdown 解析功能，支持代码高亮和多种配置选项。

## 主要功能

- 统一的 Markdown 解析配置
- 自动代码高亮（使用 highlight.js）
- 智能内容检测（自动判断是否需要解析 Markdown）
- Vue 组合式函数支持
- TypeScript 类型支持

## 基本使用

### 1. 在 Vue 组件中使用

```typescript
import { useMarkdown } from '@/lib/markdown'

// 使用默认配置
const { parse, smartParse } = useMarkdown()

// 解析 Markdown 内容
const htmlContent = parse('# 标题\n这是一段文本')

// 智能解析（自动检测是否包含 Markdown 语法）
const smartContent = smartParse('可能包含 Markdown 的内容')
```

### 2. 在 Astro 文件中使用

```typescript
import { createMarkdownParser, parseMarkdown } from '../../lib/markdown'

// 创建解析器实例
const md = createMarkdownParser()

// 解析内容
const htmlContent = md.render(markdownContent)
// 或者使用工具函数
const htmlContent = parseMarkdown(markdownContent)
```

### 3. 自定义配置

```typescript
import { useMarkdown } from '@/lib/markdown'

const { parse } = useMarkdown({
  html: true,           // 启用 HTML 标签
  linkify: true,        // 自动转换链接
  typographer: true,    // 启用排版优化
  highlight: true,      // 启用代码高亮
  customHighlight: (str, lang) => {
    // 自定义高亮函数
    return `<pre><code class="language-${lang}">${str}</code></pre>`
  }
})
```

## API 参考

### `useMarkdown(options?: MarkdownOptions)`

Vue 组合式函数，返回解析器实例和工具函数。

**返回值：**
- `parser`: MarkdownIt 实例
- `parse(content: string)`: 解析 Markdown 为 HTML
- `smartParse(content: string)`: 智能解析内容

### `createMarkdownParser(options?: MarkdownOptions)`

创建 MarkdownIt 解析器实例。

### `parseMarkdown(content: string, parser?: MarkdownIt)`

解析 Markdown 文本为 HTML。

### `smartParseContent(content: string, parser?: MarkdownIt)`

智能解析内容，自动检测是否包含 Markdown 语法。

## 配置选项

```typescript
interface MarkdownOptions {
  html?: boolean              // 是否启用 HTML 标签（默认：true）
  linkify?: boolean          // 是否自动转换链接（默认：true）
  typographer?: boolean      // 是否启用排版优化（默认：true）
  highlight?: boolean        // 是否启用代码高亮（默认：true）
  customHighlight?: (str: string, lang: string) => string  // 自定义高亮函数
}
```

## 样式支持

工具已经配置了与项目主题兼容的代码高亮样式，包括：

- 响应式主题适配
- DaisyUI 主题变量支持
- 语法高亮颜色配置

## 迁移指南

### 从旧的 MarkdownIt 实例迁移

**之前：**
```typescript
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    // 自定义高亮逻辑
  }
})

const result = md.render(content)
```

**现在：**
```typescript
import { useMarkdown } from '@/lib/markdown'

const { parse } = useMarkdown()
const result = parse(content)
```

### 智能解析迁移

**之前：**
```typescript
const hasMarkdownSyntax = /[#*`\[\]]/g.test(content)
if (hasMarkdownSyntax) {
  return md.render(content)
} else {
  return content
}
```

**现在：**
```typescript
const { smartParse } = useMarkdown()
return smartParse(content)
```
