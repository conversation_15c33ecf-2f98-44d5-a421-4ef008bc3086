import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'

/**
 * Markdown 解析器配置选项
 */
export interface MarkdownOptions {
  /** 是否启用 HTML 标签 */
  html?: boolean
  /** 是否自动转换链接 */
  linkify?: boolean
  /** 是否启用排版优化 */
  typographer?: boolean
  /** 是否启用代码高亮 */
  highlight?: boolean
  /** 自定义高亮函数 */
  customHighlight?: (str: string, lang: string) => string
}

/**
 * HTML 转义函数
 * @param str 需要转义的字符串
 * @returns 转义后的字符串
 */
function escapeHtml(str: string): string {
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

/**
 * 默认的代码高亮函数
 * @param str 代码字符串
 * @param lang 语言标识
 * @returns 高亮后的 HTML 字符串
 */
function defaultHighlight(str: string, lang: string): string {
  if (lang && hljs.getLanguage(lang)) {
    try {
      return '<pre class="hljs"><code>' +
             hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
             '</code></pre>'
    } catch (__) {
      // 忽略高亮错误
    }
  }
  // 使用自定义的 HTML 转义函数
  return '<pre class="hljs"><code>' + escapeHtml(str) + '</code></pre>'
}

/**
 * 创建 Markdown 解析器实例
 * @param options 配置选项
 * @returns MarkdownIt 实例
 */
export function createMarkdownParser(options: MarkdownOptions = {}): MarkdownIt {
  const {
    html = true,
    linkify = true,
    typographer = true,
    highlight = true,
    customHighlight
  } = options

  const config: MarkdownIt.Options = {
    html,
    linkify,
    typographer
  }

  // 如果启用代码高亮，添加高亮函数
  if (highlight) {
    config.highlight = customHighlight || defaultHighlight
  }

  return new MarkdownIt(config)
}

/**
 * 默认的 Markdown 解析器实例
 */
export const defaultMarkdownParser = createMarkdownParser()

/**
 * 解析 Markdown 文本为 HTML
 * @param content Markdown 文本
 * @param parser 可选的解析器实例，默认使用 defaultMarkdownParser
 * @returns 解析后的 HTML 字符串
 */
export function parseMarkdown(content: string, parser?: MarkdownIt): string {
  if (!content) return ''
  
  const md = parser || defaultMarkdownParser
  return md.render(content)
}

/**
 * 智能解析内容：检测是否包含 Markdown 语法，如果包含则解析，否则直接返回
 * @param content 内容字符串
 * @param parser 可选的解析器实例
 * @returns 处理后的内容
 */
export function smartParseContent(content: string, parser?: MarkdownIt): string {
  if (!content) return ''

  // 检查内容是否包含 Markdown 语法
  const hasMarkdownSyntax = /[#*`\[\]]/g.test(content)

  if (hasMarkdownSyntax) {
    // 如果包含 Markdown 语法，解析为 HTML
    return parseMarkdown(content, parser)
  } else {
    // 如果是纯文本或 HTML，直接返回
    return content
  }
}

/**
 * 创建用于 Vue 组合式函数的 Markdown 解析器
 * @param options 配置选项
 * @returns 包含解析函数的对象
 */
export function useMarkdown(options: MarkdownOptions = {}) {
  const parser = createMarkdownParser(options)

  return {
    parser,
    parse: (content: string) => parseMarkdown(content, parser),
    smartParse: (content: string) => smartParseContent(content, parser)
  }
}
