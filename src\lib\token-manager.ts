/**
 * Token管理器 - 处理无感刷新token逻辑
 */

import { authApi } from './api/auth';
import type { RetryRequestConfig } from './api/types';
import {
  getAuthToken,
  setAuthToken,
  removeAuthToken,
  getRefreshToken,
  setRefreshToken,
  removeRefreshToken
} from './auth';

// 请求队列接口
interface PendingRequest {
  resolve: (value: any) => void;
  reject: (reason: any) => void;
  config: RetryRequestConfig;
}

class TokenManager {
  private isRefreshing = false;
  private pendingRequests: PendingRequest[] = [];

  /**
   * 检查token是否即将过期
   */
  private isTokenExpiringSoon(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const exp = payload.exp * 1000; // 转换为毫秒
      const now = Date.now();
      const timeUntilExpiry = exp - now;
      
      // 如果token在5分钟内过期，认为即将过期
      return timeUntilExpiry < 5 * 60 * 1000;
    } catch (error) {
      console.error('解析token失败:', error);
      return true; // 解析失败时认为已过期
    }
  }

  /**
   * 刷新token
   */
  private async refreshToken(): Promise<string> {
    const refreshToken = getRefreshToken();
    
    if (!refreshToken) {
      throw new Error('没有refresh token');
    }

    try {
      const response = await authApi.refreshToken(refreshToken);

      // 根据后端返回的数据结构解析
      let tokenData;
      if ((response as any).success && (response as any).data) {
        tokenData = (response as any).data;
      } else if ((response as any).access_token) {
        tokenData = response;
      } else {
        throw new Error('刷新token响应格式错误');
      }

      const { access_token, refresh_token } = tokenData;
      
      // 更新存储的token
      setAuthToken(access_token);
      if (refresh_token) {
        setRefreshToken(refresh_token);
      }
      
      console.log('Token刷新成功');
      return access_token;
    } catch (error) {
      console.error('Token刷新失败:', error);
      // 刷新失败，清理所有token
      this.clearTokens();
      throw error;
    }
  }

  /**
   * 清理所有token
   */
  private clearTokens(): void {
    removeAuthToken();
    removeRefreshToken();
    localStorage.removeItem('isLoggedIn');
  }

  /**
   * 处理401错误，尝试刷新token
   */
  async handleUnauthorized(originalConfig: RetryRequestConfig): Promise<any> {
    if (this.isRefreshing) {
      // 如果正在刷新，将请求加入队列
      return new Promise((resolve, reject) => {
        this.pendingRequests.push({
          resolve,
          reject,
          config: originalConfig
        });
      });
    }

    this.isRefreshing = true;

    try {
      const newToken = await this.refreshToken();
      
      // 处理队列中的请求
      this.pendingRequests.forEach(({ resolve, config }) => {
        resolve(this.retryRequest(config, newToken));
      });
      this.pendingRequests = [];
      
      // 重新发送原始请求
      return this.retryRequest(originalConfig, newToken);
    } catch (error) {
      // 刷新失败，拒绝所有队列中的请求
      this.pendingRequests.forEach(({ reject }) => {
        reject(error);
      });
      this.pendingRequests = [];
      
      // 重定向到登录页面
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      
      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * 重新发送请求
   */
  private async retryRequest(config: RetryRequestConfig, token: string): Promise<any> {
    const { url, method = 'GET', headers = {}, body, signal } = config;
    
    const newHeaders = {
      ...headers,
      'Authorization': `Bearer ${token}`
    };

    const response = await fetch(url, {
      method,
      headers: newHeaders,
      body,
      signal
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  /**
   * 检查并主动刷新即将过期的token
   */
  async checkAndRefreshToken(): Promise<void> {
    const token = getAuthToken();
    
    if (!token) {
      return;
    }

    if (this.isTokenExpiringSoon(token) && !this.isRefreshing) {
      try {
        await this.refreshToken();
      } catch (error) {
        console.error('主动刷新token失败:', error);
      }
    }
  }

  /**
   * 获取当前是否正在刷新token
   */
  get isTokenRefreshing(): boolean {
    return this.isRefreshing;
  }
}

// 导出单例实例
export const tokenManager = new TokenManager();
