import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'

// 设置dayjs插件和语言
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 格式化日期为中文格式
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认为 'YYYY年MM月DD日'
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: string | Date, format: string = 'YYYY年MM月DD日'): string {
  return dayjs(date).format(format)
}

/**
 * 格式化日期为相对时间（如：3天前）
 * @param date 日期字符串或Date对象
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: string | Date): string {
  return dayjs(date).fromNow()
}
