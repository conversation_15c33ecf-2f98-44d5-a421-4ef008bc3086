---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/vue/Header.vue';
import Footer4Col from '../../components/react/Footer4Col.tsx';
import TableOfContents from '../../components/react/TableOfContents.tsx';
import Breadcrumb from '../../components/ui/Breadcrumb.vue';
import { createMarkdownParser } from '../../lib/markdown';
import { Clock, ChevronLeft, ChevronRight, Copy, ArrowLeft, Share2, BookOpen } from 'lucide-react';
import { formatDate } from '../../lib/utils';

// 获取路由参数
const { id } = Astro.params;

// API基础配置
const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

// 初始化markdown解析器
const md = createMarkdownParser();

// 获取文章数据
let article: any = null;
let error: string | null = null;
let needsAuth = false;

try {
  const response = await fetch(`${API_BASE_URL}/blog/posts/${id}/`);
  if (response.ok) {
    const result = await response.json();
    if (result.success) {
      article = result.data;
      // 将markdown内容转换为HTML
      if (article && article.content) {
        article.content = md.render(article.content);
      }
    } else {
      error = result.message || '获取文章失败';
    }
  } else if (response.status === 401) {
    // 需要登录才能访问
    needsAuth = true;
  } else {
    error = `HTTP ${response.status}: ${response.statusText}`;
  }
} catch (err) {
  console.error('获取文章失败:', err);
  error = '网络请求失败';
}

// 如果文章不存在或有错误，返回404
if (!article && !needsAuth && error) {
  return Astro.redirect('/404');
}

// 上一篇和下一篇文章从API数据中获取
const prevArticle = article?.prev_post;
const nextArticle = article?.next_post;

// 获取分类徽章样式
const getCategoryBadgeClass = (category: string) => {
  switch (category) {
    case '威胁分析': return 'badge badge-error'
    case 'APT研究': return 'badge badge-warning'
    case '情报技术': return 'badge badge-info'
    case '安全运营': return 'badge badge-success'
    case '安全架构': return 'badge badge-primary'
    case '云安全': return 'badge badge-secondary'
    default: return 'badge badge-outline'
  }
}

// 面包屑导航数据
const breadcrumbItems = article ? [
  { label: '首页', href: '/' },
  { label: '安全博客', href: '/blog' },
  { label: article.title, current: true }
] : [
  { label: '首页', href: '/' },
  { label: '安全博客', href: '/blog' },
  { label: '文章详情', current: true }
];

// 页面标题和描述
const pageTitle = article ? `${article.title} - 威胁情报数据中心` : '文章详情 - 威胁情报数据中心';
const pageDescription = article ? `${article.title} - 深度解析网络安全威胁，提供专业的威胁情报分析和防护策略。` : '深度解析网络安全威胁，提供专业的威胁情报分析和防护策略。';
---

<Layout
  title={pageTitle}
  description={pageDescription}
>
  <Header client:load />
  <main class="pt-20">
    {needsAuth ? (
      <!-- 需要登录的提示页面 -->
      <div class="min-h-screen flex items-center justify-center bg-base-100">
        <div class="text-center max-w-md mx-auto p-6">
          <div class="mb-6">
            <svg class="w-16 h-16 mx-auto text-warning mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
              </path>
            </svg>
            <h2 class="text-2xl font-bold text-base-content mb-2">需要登录</h2>
            <p class="text-base-content/60 mb-6">此文章需要登录后才能查看完整内容</p>
          </div>
          <div class="space-y-3">
            <a href={`/login?redirect=${encodeURIComponent(Astro.url.pathname)}`} class="btn btn-primary btn-block">
              立即登录
            </a>
            <a href="/blog" class="btn btn-ghost btn-block">
              返回博客列表
            </a>
          </div>
        </div>
      </div>
    ) : article ? (
      <!-- 正常的文章内容 -->
      <>
        <!-- 文章头部 -->
        <div class="bg-base-100 py-8 lg:py-12">
          <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <!-- 面包屑导航 -->
            <Breadcrumb items={breadcrumbItems} client:load />

        <!-- 文章分类 -->
        <div class="mb-4">
          <span class={getCategoryBadgeClass(article.category)}>
            {article.category}
          </span>
        </div>

        <!-- 文章标题 -->
        <h1 class="text-2xl font-bold text-base-content mb-6 md:text-3xl lg:text-4xl leading-tight">
          {article.title}
        </h1>

        <!-- 文章元信息 -->
        <div class="flex flex-wrap items-center gap-4 lg:gap-6 mb-6 text-base-content/70">

          <div class="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span class="text-sm lg:text-base">{formatDate(article.created_at)}</span>
          </div>
          <div class="flex items-center gap-1">
            <BookOpen className="h-4 w-4" />
            <span class="text-sm lg:text-base">约 <span class="text-primary font-mono font-semibold">{article.reading_time}</span> 分钟阅读</span>
          </div>
        </div>

        <!-- 文章标签 -->
        <div class="flex flex-wrap gap-2">
          {article.tags.map((tag: string) => (
            <span class="badge badge-outline badge-sm">
              {tag}
            </span>
          ))}
        </div>
      </div>
    </div>

    <!-- 文章内容 -->
    <div class="bg-base-100">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- 使用网格布局：主内容 + 侧边栏 -->
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
          <!-- 主内容区域 -->
          <div class="xl:col-span-3">
            <!-- 文章正文 -->
            <article class="prose prose-lg max-w-none text-base-content space-y-6 lg:space-y-8 text-base lg:text-lg leading-relaxed">
              <div set:html={article.content} />
            </article>
          </div>

          <!-- 侧边栏：文章目录 -->
          <div class="xl:col-span-1">
            <div class="sticky top-24">
              <TableOfContents content={article.content} client:load />
            </div>
          </div>
        </div>

        <!-- 上一篇/下一篇导航 -->
        {(prevArticle || nextArticle) && (
          <div class="mt-8 lg:mt-12 pt-6 lg:pt-8 border-t border-base-300">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
              <!-- 上一篇文章 -->
              {prevArticle && (
                <a
                  href={`/blog/${prevArticle.id}`}
                  class="card card-border shadow-none hover:shadow-lg transition-all duration-300 border border-gray-50/10 hover:border-gray-100/30 group"
                >
                  <div class="card-body p-4 lg:p-6">
                    <div class="flex items-center gap-2 mb-3">
                      <ChevronLeft className="h-4 w-4 text-base-content/60 group-hover:text-primary transition-colors" />
                      <span class="text-sm text-base-content/60 group-hover:text-primary transition-colors">上一篇</span>
                    </div>
                    <div class="mb-2">
                      <span class={getCategoryBadgeClass(prevArticle.category)}>
                        {prevArticle.category}
                      </span>
                    </div>
                    <h3 class="text-base lg:text-lg font-semibold text-base-content group-hover:text-primary transition-colors line-clamp-2">
                      {prevArticle.title}
                    </h3>
                  </div>
                </a>
              )}

              <!-- 下一篇文章 -->
              {nextArticle && (
                <a
                  href={`/blog/${nextArticle.id}`}
                  class={`card card-border shadow-none hover:shadow-lg transition-all duration-300 border border-gray-50/10 hover:border-gray-100/30 group ${!prevArticle ? 'lg:col-start-2' : ''}`}
                >
                  <div class="card-body p-4 lg:p-6">
                    <div class="flex items-center justify-end gap-2 mb-3">
                      <span class="text-sm text-base-content/60 group-hover:text-primary transition-colors">下一篇</span>
                      <ChevronRight className="h-4 w-4 text-base-content/60 group-hover:text-primary transition-colors" />
                    </div>
                    <div class="mb-2 flex justify-end">
                      <span class={getCategoryBadgeClass(nextArticle.category)}>
                        {nextArticle.category}
                      </span>
                    </div>
                    <h3 class="text-base lg:text-lg font-semibold text-base-content group-hover:text-primary transition-colors line-clamp-2 text-right">
                      {nextArticle.title}
                    </h3>
                  </div>
                </a>
              )}
            </div>
          </div>
        )}

        <!-- 文章底部 -->
        <div class="mt-8 lg:mt-12 pt-6 lg:pt-8 border-t border-base-300">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-4">
              <span class="text-base-content/70 text-sm lg:text-base">分享文章:</span>
              <div class="flex gap-2">
                <button class="btn btn-outline btn-sm" title="分享到微博">
                  <Share2 className="h-4 w-4" />
                </button>
                <button class="btn btn-outline btn-sm" title="分享到微信">
                  <Share2 className="h-4 w-4" />
                </button>
                <button class="btn btn-outline btn-sm" title="复制链接">
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            </div>
            <a href="/blog" class="btn btn-primary btn-outline btn-sm lg:btn-md">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回博客列表
            </a>
          </div>
        </div>
      </div>
    </div>
      </>
    ) : (
      <!-- 错误状态 -->
      <div class="min-h-screen flex items-center justify-center bg-base-100">
        <div class="text-center max-w-md mx-auto p-6">
          <h2 class="text-2xl font-bold text-base-content mb-2">文章不存在</h2>
          <p class="text-base-content/60 mb-6">抱歉，您访问的文章不存在或已被删除</p>
          <a href="/blog" class="btn btn-primary">返回博客列表</a>
        </div>
      </div>
    )}
  </main>
  <Footer4Col client:load />
</Layout>


