---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/vue/Header.vue';
import SecurityBlogList from '../../components/sections/SecurityBlogList.vue';
import Footer4Col from '../../components/react/Footer4Col.tsx';
import UniversalSearch from '@/components/sections/UniversalSearch.vue';
---

<Layout
  title="安全博客 - 威胁情报数据中心"
  description="深度解析网络安全威胁，探索最新的安全趋势和防护技术。提供专业的威胁情报分析、APT研究、安全运营等领域的深度文章。"
>
  <Header client:load />
  <main class="pt-16 lg:pt-20">
    <!-- 通用搜索组件 -->
    <UniversalSearch client:load />
    <SecurityBlogList client:load />
  </main>
  <Footer4Col client:load />
</Layout>
