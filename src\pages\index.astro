---
import Layout from '../layouts/Layout.astro';
import Header from '../components/vue/Header.vue';
import SplineHero from '../components/react/SplineHero.tsx';
import UniversalSearch from '../components/sections/UniversalSearch.vue';
import LatestThreats from '../components/sections/LatestThreats.vue';
import LatestBlogs from '../components/sections/LatestBlogs.astro';
import Footer4Col from '../components/react/Footer4Col.tsx';
---

<Layout title="威胁情报数据中心 - 专业的网络安全威胁情报平台">
	<Header client:load />
	<main>
		<SplineHero client:load />
		<UniversalSearch client:load />
		<LatestThreats client:load />
		<LatestBlogs />
	</main>
	<Footer4Col client:load />
</Layout>
