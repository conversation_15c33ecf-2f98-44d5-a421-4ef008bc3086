---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/vue/Header.vue';
import Footer4Col from '../../components/react/Footer4Col.tsx';
import Breadcrumb from '../../components/ui/Breadcrumb.vue';
import ThreatNavigation from '../../components/ui/ThreatNavigation.vue';
import { Clock, CheckCircle } from 'lucide-react';
import { smartParseContent } from '../../lib/markdown';

// 获取路由参数
const { id } = Astro.params;

// 从API获取威胁情报数据
const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

let threat = null;
let prevThreat = null;
let nextThreat = null;
let needsAuth = false;

try {
  // 获取威胁情报详情
  const response = await fetch(`${API_BASE_URL}/intell/posts/${id}/`);

  if (response.ok) {
    const result = await response.json();
    if (result.success && result.data) {
      threat = result.data;
      // 设置前后导航数据
      if (result.data.prev_id) {
        prevThreat = result.data.prev_id;
      }
      if (result.data.next_id) {
        nextThreat = result.data.next_id;
      }
    }
  } else if (response.status === 401) {
    // 需要登录才能访问
    needsAuth = true;
  }
} catch (error) {
  console.error('获取威胁情报详情失败:', error);
}

// 如果威胁情报不存在且不需要认证，返回404
if (!threat && !needsAuth) {
  return Astro.redirect('/404');
}


// 获取严重程度徽章样式
const getSeverityBadgeClass = (severity: string) => {
  switch (severity) {
    case '严重': return 'badge badge-error'
    case '高': return 'badge badge-warning'
    case '中': return 'badge badge-info'
    case '低': return 'badge badge-success'
    default: return 'badge badge-secondary'
  }
}



// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 面包屑导航数据
const breadcrumbItems = threat ? [
  { label: '首页', href: '/' },
  { label: '威胁情报', href: '/intell' },
  { label: threat.title, current: true }
] : [
  { label: '首页', href: '/' },
  { label: '威胁情报', href: '/intell' },
  { label: '情报详情', current: true }
];

// 页面标题和描述
const pageTitle = threat ? `${threat.title} - 威胁情报数据中心` : '威胁情报详情 - 威胁情报数据中心';
const pageDescription = threat ? `${threat.title} - ${threat.description}` : '威胁情报详情页面';
---

<Layout
  title={pageTitle}
  description={pageDescription}
>
  <Header client:load />
  <main class="pt-20">
    {needsAuth ? (
      <!-- 需要登录的提示页面 -->
      <div class="min-h-screen flex items-center justify-center bg-base-100">
        <div class="text-center max-w-md mx-auto p-6">
          <div class="mb-6">
            <svg class="w-16 h-16 mx-auto text-warning mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
              </path>
            </svg>
            <h2 class="text-2xl font-bold text-base-content mb-2">需要登录</h2>
            <p class="text-base-content/60 mb-6">此威胁情报需要登录后才能查看完整内容</p>
          </div>
          <div class="space-y-3">
            <a href={`/login?redirect=${encodeURIComponent(Astro.url.pathname)}`} class="btn btn-primary btn-block">
              立即登录
            </a>
            <a href="/intell" class="btn btn-ghost btn-block">
              返回情报列表
            </a>
          </div>
        </div>
      </div>
    ) : threat ? (
      <!-- 正常的威胁情报内容 -->
      <>
    <!-- 威胁情报头部 -->
    <div class="bg-base-100 py-8 lg:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- 面包屑导航 -->
        <Breadcrumb items={breadcrumbItems} client:load />

        <!-- 威胁情报基本信息 -->
        <div class="mb-6">
          <div class="flex flex-wrap items-center gap-3 mb-4">
            <span class={getSeverityBadgeClass(threat.severity)}>
              {threat.severity}
            </span>
            <span class="badge badge-outline">
              {threat.type || threat.threat_type}
            </span>
            <span class="text-sm text-base-content/60">
              来源: {threat.source}
            </span>
          </div>
        </div>

        <!-- 威胁情报标题 -->
        <h1 class="text-2xl font-bold text-base-content mb-6 md:text-3xl lg:text-4xl leading-tight">
          {threat.title}
        </h1>

        <!-- 威胁情报元信息 -->
        <div class="flex flex-wrap items-center gap-4 lg:gap-6 mb-6 text-base-content/70">
          <div class="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span class="text-sm lg:text-base">发布时间: {formatTime(threat.created_at)}</span>
          </div>
        </div>

        <!-- 威胁情报标签 -->
        <div class="flex flex-wrap gap-2">
          {threat.tags && threat.tags.map((tag: string) => (
            <span class="badge badge-outline badge-sm">
              {tag}
            </span>
          ))}
        </div>
      </div>
    </div>

    <!-- 威胁情报封面图 -->
    <div class="mb-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="overflow-hidden rounded-xl shadow-lg border border-gray-200/20">
          <img
            src={threat.cover_image || threat.coverImage || 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=800&h=400&fit=crop&auto=format'}
            alt={threat.title}
            class="w-full h-64 md:h-80 lg:h-96 object-cover transition-transform duration-300 hover:scale-105"
          />
        </div>
      </div>
    </div>

    <!-- 威胁情报内容 -->
    <div class="bg-base-100 py-8 lg:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
          <!-- 主要内容区域 -->
          <div class="lg:col-span-2">
            <!-- 威胁描述 -->
            <div class="card bg-base-100 border border-gray-200/20 mb-8">
              <div class="card-body p-6">
                <h2 class="text-xl font-bold text-base-content mb-4">威胁描述</h2>
                <p class="text-base-content/80 leading-relaxed">
                  {threat.description}
                </p>
              </div>
            </div>

            <!-- 详细分析 -->
            {(threat.content) && (
              <div class="card bg-base-100 border border-gray-200/20 mb-8">
                <div class="card-body p-6">
                  <h2 class="text-xl font-bold text-base-content mb-4">详细分析</h2>
                  <article class="max-w-none text-base-content space-y-6 text-base leading-relaxed">
                    <div set:html={smartParseContent(threat.content)} />
                  </article>
                </div>
              </div>
            )}

          </div>

          <!-- 侧边栏 -->
          <div class="lg:col-span-1">
            <div class="sticky top-8 space-y-6">
              <!-- 威胁情报信息卡片 -->
              <div class="card bg-base-100 border border-gray-200/20">
                <div class="card-body p-6">
                  <h3 class="text-lg font-bold text-base-content mb-4">威胁情报信息</h3>
                  <div class="space-y-4">
                    <div class="flex justify-between items-center">
                      <span class="text-base-content/70">威胁类型:</span>
                      <span class="badge badge-outline">{threat.type || threat.threat_type}</span>
                    </div>
                    <div class="flex justify-between items-center">
                      <span class="text-base-content/70">严重程度:</span>
                      <span class={getSeverityBadgeClass(threat.severity)}>{threat.severity}</span>
                    </div>
                    <div class="flex justify-between items-center">
                      <span class="text-base-content/70">情报来源:</span>
                      <span class="text-base-content">{threat.source}</span>
                    </div>
                    <div class="flex justify-between items-center">
                      <span class="text-base-content/70">置信度:</span>
                      <div class="flex items-center gap-2">
                        <div class="w-16 bg-base-200 rounded-full h-2">
                          <div
                            class="bg-primary h-2 rounded-full transition-all duration-300"
                            style={`width: ${threat.confidence}%`}
                          ></div>
                        </div>
                        <span class="text-sm font-medium">{threat.confidence}%</span>
                      </div>
                    </div>
                    <div class="flex justify-between items-start">
                      <span class="text-base-content/70">更新时间:</span>
                      <span class="text-base-content text-sm">{formatTime(threat.updated_at)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 相关事件 -->
              {threat.related_incidents && threat.related_incidents.length > 0 && (
                <div class="card bg-base-100 border border-gray-200/20">
                  <div class="card-body p-6">
                    <h3 class="text-lg font-bold text-base-content mb-4 flex items-center gap-2">
                      <span class="icon-[tabler--link] text-primary size-5"></span>
                      相关事件
                    </h3>
                    <div class="space-y-3">
                      {threat.related_incidents.map((incident: any) => (
                        <a
                          href={`/intell/${incident.id}`}
                          class="block p-3 rounded-lg border border-gray-200/20 hover:border-gray-300/40 hover:bg-base-200/30 transition-all duration-200 group"
                        >
                          <h4 class="text-sm font-medium text-base-content group-hover:text-primary transition-colors line-clamp-2 mb-1">
                            {incident.title}
                          </h4>
                          <p class="text-xs text-base-content/60">{incident.date}</p>
                        </a>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <!-- 威胁情报导航 -->
        <ThreatNavigation
          prevThreat={prevThreat}
          nextThreat={nextThreat}
          getSeverityBadgeClass={getSeverityBadgeClass}
          client:load
        />
      </div>
    </div>
      </>
    ) : (
      <!-- 错误状态 -->
      <div class="min-h-screen flex items-center justify-center bg-base-100">
        <div class="text-center max-w-md mx-auto p-6">
          <h2 class="text-2xl font-bold text-base-content mb-2">威胁情报不存在</h2>
          <p class="text-base-content/60 mb-6">抱歉，您访问的威胁情报不存在或已被删除</p>
          <a href="/intell" class="btn btn-primary">返回情报列表</a>
        </div>
      </div>
    )}
  </main>
  <Footer4Col client:load />
</Layout>
