---
import ListPageTemplate from '@/layouts/ListPageTemplate.astro'
import ThreatIntelligenceList from '@/components/sections/ThreatIntelligenceList.vue'
import type { PageConfig } from '@/types/page'

// 获取URL参数
const { searchParams } = Astro.url
const category = searchParams.get('category')

// 从API获取统计数据
const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1';

let stats = [
  { value: "0", label: "威胁情报总数", color: "primary" as const },
  { value: "0", label: "高危威胁", color: "warning" as const },
  { value: "0", label: "今日新增", color: "success" as const }
];

try {
  // 构建统计API URL，如果有分类参数则添加
  let statsUrl = `${API_BASE_URL}/intell/posts/stats/`;
  if (category) {
    statsUrl += `?category=${category}`;
  }

  const response = await fetch(statsUrl);
  if (response.ok) {
    const result = await response.json();
    if (result.success && result.data) {
      const data = result.data;

      // 计算高危威胁数量（严重 + 高）
      const highRiskCount = data.severity_distribution
        ?.filter((item: any) => item.severity === '严重' || item.severity === '高')
        ?.reduce((sum: number, item: any) => sum + item.count, 0) || 0;

      stats = [
        { value: data.total?.toLocaleString() || "0", label: "威胁情报总数", color: "primary" as const },
        { value: highRiskCount.toLocaleString(), label: "高危威胁", color: "warning" as const },
        { value: data.recent_count?.toLocaleString() || "0", label: "最近新增", color: "success" as const }
      ];
    }
  }
} catch (error) {
  console.error('获取统计数据失败:', error);
}

// 根据分类设置页面配置
let pageTitle = "威胁情报中心";
let description = "实时更新的全球威胁情报数据，帮助您了解最新的网络安全威胁态势";

if (category === '1') {
  pageTitle = "威胁情报列表";
  description = "实时更新的全球威胁情报数据，帮助您了解最新的网络安全威胁态势";
} else if (category === '2') {
  pageTitle = "安全事件";
  description = "安全事件监测与分析，实时跟踪网络安全事件动态";
}

const config: PageConfig = {
  title: `${pageTitle} - 威胁情报数据中心`,
  pageTitle: pageTitle,
  description: description,
  stats: stats,
  gridCols: "grid-cols-1 md:grid-cols-3"
}
---

<ListPageTemplate config={config}>
  <ThreatIntelligenceList client:load category={category} />
</ListPageTemplate>
