---
import Layout from '../layouts/Layout.astro';
import Header from '../components/vue/Header.vue';
import AuthGuard from '../components/auth/AuthGuard.vue';
import ProfileCenter from '../components/auth/ProfileCenter.vue';
import Footer4Col from '../components/react/Footer4Col.tsx';
---

<Layout title="个人中心 - 威胁情报数据中心" description="管理您的个人信息、安全设置和账户偏好">
  <Header client:load />
  <main>
    <AuthGuard client:load>
      <ProfileCenter client:load />
    </AuthGuard>
  </main>
  <Footer4Col client:load />
</Layout>
