---
import Layout from '@/layouts/Layout.astro'
import Header from '@/components/vue/Header.vue'
import Footer4Col from '@/components/react/Footer4Col.tsx'
import RansomwareGroupDetail from '@/components/sections/RansomwareGroupDetail.vue'

// 获取动态路由参数
const { slug } = Astro.params

// 验证slug参数
if (!slug) {
  return Astro.redirect('/ransomware')
}

// 检查API权限 - 尝试获取基础信息来判断是否需要认证
const API_BASE_URL = import.meta.env.PUBLIC_API_BASE_URL || 'http://localhost:8000/api/v1'
let needsAuth = false
let groupName = slug.charAt(0).toUpperCase() + slug.slice(1)

try {
  const response = await fetch(`${API_BASE_URL}/groups/${slug}/basic/`)
  if (response.status === 401) {
    needsAuth = true
  } else if (response.ok) {
    const result = await response.json()
    if (result.success && result.data?.name) {
      groupName = result.data.name
    }
  }
} catch (error) {
  console.error('检查权限失败:', error)
  // 网络错误时假设需要认证，让Vue组件处理具体错误
  needsAuth = true
}

// 生成页面标题
const pageTitle = `${groupName} - 勒索软件组织详情 - 威胁情报数据中心`
---

<Layout title={pageTitle}>
  <Header client:load />
  <main class="min-h-screen bg-base-100 pt-16 lg:pt-20">
    {needsAuth ? (
      <!-- 需要登录的提示页面 -->
      <div class="min-h-screen flex items-center justify-center bg-base-100">
        <div class="text-center max-w-md mx-auto p-6">
          <div class="mb-6">
            <svg class="w-16 h-16 mx-auto text-warning mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
              </path>
            </svg>
            <h2 class="text-2xl font-bold text-base-content mb-2">需要登录</h2>
            <p class="text-base-content/60 mb-6">此勒索组织详情需要登录后才能查看</p>
          </div>
          <div class="space-y-3">
            <a href={`/login?redirect=${encodeURIComponent(Astro.url.pathname)}`} class="btn btn-primary btn-block">
              立即登录
            </a>
            <a href="/ransomware" class="btn btn-ghost btn-block">
              返回勒索组织列表
            </a>
          </div>
        </div>
      </div>
    ) : (
      <!-- 正常的勒索组织详情内容 -->
      <RansomwareGroupDetail slug={slug} client:load />
    )}
  </main>
  <Footer4Col client:load />
</Layout>