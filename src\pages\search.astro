---
import Layout from '../layouts/Layout.astro';
import Header from '../components/vue/Header.vue';
import Footer4Col from '../components/react/Footer4Col.tsx';
import SearchResults from '../components/sections/SearchResults.vue';
import PopularSearches from '../components/ui/PopularSearches.vue';
import UniversalSearch from '../components/sections/UniversalSearch.vue';

// 获取查询参数
const url = new URL(Astro.request.url);
const query = url.searchParams.get('q') || '';
---

<Layout
  title={query ? `搜索: ${query} - 威胁情报数据中心` : '搜索 - 威胁情报数据中心'}
  description="搜索威胁情报、安全事件、勒索组织和安全博客，获取最新的网络安全信息。"
>
  <Header client:load />
  <main class="pt-16 lg:pt-20">
    <!-- 通用搜索组件 -->
    <UniversalSearch client:load initialQuery={query} />

    <!-- 搜索结果 -->
    {query ? (
      <SearchResults client:load query={query} />
    ) : (
      <!-- 热门搜索 -->
      <PopularSearches client:load />
    )}
  </main>
  <Footer4Col client:load />
</Layout>

<script>
  // 热门搜索点击处理
  document.addEventListener('DOMContentLoaded', () => {
    // 监听热门搜索组件的选择事件
    document.addEventListener('popular-search-select', (event) => {
      const query = (event as CustomEvent).detail;
      if (query) {
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
      }
    });
  });
</script>


