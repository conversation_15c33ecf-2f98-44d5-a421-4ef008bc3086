---
import Layout from '../layouts/Layout.astro';
import Header from '../components/vue/Header.vue';
import Footer4Col from '../components/react/Footer4Col.tsx';
import StatisticsCharts from '../components/charts/StatisticsCharts.vue';
import { Info, BarChart3, TrendingUp, Building, Plus, Shield, Eye, RefreshCw } from 'lucide-react';
---

<Layout title="威胁数据统计分析 - 威胁情报数据中心" description="威胁情报数据统计分析，包括地理分布、时间趋势、行业分布等多维度数据可视化展示">
  <Header client:load />
  
  <main class="min-h-screen bg-base-200 pt-16 lg:pt-20">
    <!-- 页面标题区域 -->
    <section class="bg-gradient-to-r from-primary/10 to-secondary/10 py-16">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold text-base-content mb-4">
            威胁数据统计分析
          </h1>
          <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
            基于威胁情报数据库的多维度统计分析，帮助您深入了解全球网络安全威胁态势
          </p>
          <div class="flex flex-wrap justify-center gap-4 mt-8">
            <div class="badge badge-primary badge-lg">地理分布</div>
            <div class="badge badge-secondary badge-lg">时间趋势</div>
            <div class="badge badge-accent badge-lg">行业分析</div>
            <div class="badge badge-success badge-lg">组织活跃度</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计图表区域 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <!-- 数据说明 -->
        <div class="alert alert-info mb-8">
          <Info className="stroke-current shrink-0 w-6 h-6" />
          <div>
            <h3 class="font-bold">数据来源说明</h3>
            <div class="text-sm">
              本页面展示的统计数据基于威胁情报数据库中的受害者信息，包括勒索软件攻击、数据泄露等安全事件。
              数据实时更新，为网络安全研究和防护提供参考依据。
            </div>
          </div>
        </div>

        <!-- 图表组件 -->
        <StatisticsCharts client:only="vue" />
        
        <!-- 数据洞察 -->
        <div class="mt-16">
          <h2 class="text-3xl font-bold text-center mb-8">数据洞察</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-primary">
                  <BarChart3 className="w-6 h-6" />
                  地理分布特征
                </h3>
                <p class="text-base-content/70">
                  美国、中国、德国等发达国家是主要攻击目标，这与其数字化程度和经济价值密切相关。
                  攻击者倾向于选择具有高价值数据和支付能力的目标。
                </p>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-success">
                  <TrendingUp className="w-6 h-6" />
                  时间趋势分析
                </h3>
                <p class="text-base-content/70">
                  威胁事件呈现明显的时间周期性，通常在特定时期出现峰值。
                  这可能与攻击者的活动模式、技术更新周期等因素相关。
                </p>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-warning">
                  <Building className="w-6 h-6" />
                  行业分布模式
                </h3>
                <p class="text-base-content/70">
                  医疗、金融、教育等关键基础设施行业是重点攻击目标。
                  这些行业通常拥有敏感数据且对业务连续性要求极高。
                </p>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-error">
                  <Plus className="w-6 h-6" />
                  组织活跃度
                </h3>
                <p class="text-base-content/70">
                  少数勒索组织占据了大部分攻击活动，显示出明显的集中化趋势。
                  这些活跃组织通常具有更强的技术能力和组织结构。
                </p>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-info">
                  <Shield className="w-6 h-6" />
                  防护建议
                </h3>
                <p class="text-base-content/70">
                  基于数据分析结果，建议重点关注高风险行业和地区的防护，
                  建立完善的备份机制和应急响应计划。
                </p>
              </div>
            </div>

            <div class="card bg-base-100 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-accent">
                  <Eye className="w-6 h-6" />
                  持续监控
                </h3>
                <p class="text-base-content/70">
                  威胁态势持续演变，需要建立动态监控机制，
                  及时发现新的攻击模式和威胁趋势。
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据更新信息 -->
        <div class="mt-16 text-center">
          <div class="inline-flex items-center gap-2 px-4 py-2 bg-base-100 rounded-lg shadow">
            <RefreshCw className="w-5 h-5 text-success" />
            <span class="text-sm text-base-content/70">数据每日更新 | 最后更新时间: <span id="last-update-time"></span></span>
          </div>
        </div>
      </div>
    </section>
  </main>

  <Footer4Col client:load />
</Layout>

<script>
  // 设置最后更新时间
  document.addEventListener('DOMContentLoaded', () => {
    const updateTimeElement = document.getElementById('last-update-time');
    if (updateTimeElement) {
      const now = new Date();
      updateTimeElement.textContent = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  });
</script>

<style>
  /* 确保图表在暗色主题下正确显示 */
  .apexcharts-canvas {
    background: transparent !important;
  }
  
  .apexcharts-text {
    fill: var(--color-base-content) !important;
  }
  
  .apexcharts-gridline {
    stroke: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
  }
</style>
