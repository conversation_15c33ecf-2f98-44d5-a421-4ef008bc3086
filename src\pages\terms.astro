---
import Layout from '@/layouts/Layout.astro'
import Header from '../components/vue/Header.vue';
import Footer4Col from '../components/react/Footer4Col.tsx';
---

<Layout title="服务条款 - 威胁情报数据中心">
  <Header client:load />

  <div class="min-h-screen bg-base-100 pt-20">
    <!-- 头部 -->
    <div class="bg-base-200 py-8">
      <div class="container mx-auto px-4 text-center">
        <h1 class="text-3xl font-bold text-base-content">服务条款</h1>
        <p class="text-base-content/70 mt-2">威胁情报数据中心服务条款</p>
      </div>
    </div>

    <!-- 内容 -->
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <div class="space-y-6">
          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">1. 服务说明</h2>
            <p class="text-base-content/80 leading-relaxed">威胁情报数据中心（以下简称"本平台"）是一个专业的网络安全威胁情报分析平台，为用户提供威胁情报收集、分析、共享等服务。</p>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">2. 用户注册与账户</h2>
            <div class="space-y-2">
              <p class="text-base-content/80 leading-relaxed">2.1 用户注册时必须提供真实、准确、完整的个人信息。</p>
              <p class="text-base-content/80 leading-relaxed">2.2 用户有责任维护账户安全，不得将账户信息泄露给第三方。</p>
              <p class="text-base-content/80 leading-relaxed">2.3 如发现账户被盗用或存在安全风险，应立即通知本平台。</p>
            </div>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">3. 服务内容</h2>
            <div class="space-y-2">
              <p class="text-base-content/80 leading-relaxed">3.1 本平台提供威胁情报数据查询、分析报告、安全事件监控等服务。</p>
              <p class="text-base-content/80 leading-relaxed">3.2 平台数据来源包括但不限于公开情报、合作伙伴共享、用户贡献等。</p>
              <p class="text-base-content/80 leading-relaxed">3.3 本平台保留随时修改、暂停或终止服务的权利。</p>
            </div>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">4. 用户行为规范</h2>
            <div class="space-y-2">
              <p class="text-base-content/80 leading-relaxed">4.1 用户不得利用本平台从事违法违规活动。</p>
              <p class="text-base-content/80 leading-relaxed">4.2 不得恶意攻击平台系统或干扰正常服务。</p>
              <p class="text-base-content/80 leading-relaxed">4.3 不得传播虚假信息或恶意软件。</p>
              <p class="text-base-content/80 leading-relaxed">4.4 尊重知识产权，不得未经授权使用他人数据。</p>
            </div>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">5. 数据使用与保护</h2>
            <div class="space-y-2">
              <p class="text-base-content/80 leading-relaxed">5.1 用户通过本平台获取的数据仅供合法的安全防护目的使用。</p>
              <p class="text-base-content/80 leading-relaxed">5.2 禁止将平台数据用于攻击、入侵或其他恶意行为。</p>
              <p class="text-base-content/80 leading-relaxed">5.3 本平台采用行业标准的安全措施保护用户数据。</p>
            </div>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">6. 免责声明</h2>
            <div class="space-y-2">
              <p class="text-base-content/80 leading-relaxed">6.1 本平台提供的威胁情报数据仅供参考，不保证数据的完整性和准确性。</p>
              <p class="text-base-content/80 leading-relaxed">6.2 用户使用平台服务产生的任何损失，本平台不承担责任。</p>
              <p class="text-base-content/80 leading-relaxed">6.3 因不可抗力导致的服务中断，本平台不承担责任。</p>
            </div>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">7. 服务条款变更</h2>
            <p class="text-base-content/80 leading-relaxed">本平台有权随时修改服务条款，修改后的条款将在平台上公布。用户继续使用服务即表示同意修改后的条款。</p>
          </div>

          <div class="space-y-4">
            <h2 class="text-2xl font-bold text-base-content">8. 联系方式</h2>
            <p class="text-base-content/80 leading-relaxed">如有疑问，请联系我们：<a href="mailto:<EMAIL>" class="link link-primary"><EMAIL></a></p>
          </div>

          <div class="mt-8 p-4 bg-base-200 rounded-lg">
            <p class="text-sm text-base-content/70">
              最后更新时间：2025年06月23日<br>
              生效时间：2025年06月23日
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <Footer4Col client:load />
</Layout>
