---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/vue/Header.vue';
import Footer4Col from '../../components/react/Footer4Col.tsx';
import ToolDetail from '../../components/sections/ToolDetail.vue';
import Breadcrumb from '../../components/ui/Breadcrumb.vue';
import { ArrowLeft } from 'lucide-react';

// 获取路由参数
const { id } = Astro.params;

// 验证ID参数
if (!id || isNaN(Number(id))) {
  return Astro.redirect('/404');
}

const toolId = Number(id);
---

<Layout title="应急工具详情 - 威胁情报数据中心">
  <Header client:load />

  <main class="pt-16 lg:pt-20">
    <!-- 面包屑导航 -->
    <div class="bg-base-100">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-4">
        <Breadcrumb
          items={[
            { label: '首页', href: '/' },
            { label: '应急工具', href: '/tools' },
            { label: '工具详情', current: true }
          ]}
          client:load
        />
      </div>
    </div>

    <!-- 工具详情内容 -->
    <div class="bg-base-100">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <ToolDetail toolId={toolId} client:load />
      </div>
    </div>
  </main>

  <Footer4Col client:load />
</Layout>