@import "tailwindcss";
@import "highlight.js/styles/github-dark.css";

@plugin "flyonui" {
  themes: black --default, dark --prefersdark;
}

@plugin "@iconify/tailwind4";
@plugin "@tailwindcss/typography";
@import "flyonui/variants.css";
/* FlyonUI JS 组件变体样式 */
@import "flyonui/src/vendor/apexcharts.css";
/* ApexCharts 样式 */

@source "./node_modules/flyonui/flyonui.js";
/* FlyonUI JS 组件 */

@theme inline {
  /* 映射FlyonUI语义化颜色到Tailwind CSS变量 */
  --color-background: var(--color-base-100);
  --color-foreground: var(--color-base-content);
  --color-card: var(--color-base-100);
  --color-card-foreground: var(--color-base-content);
  --color-popover: var(--color-base-100);
  --color-popover-foreground: var(--color-base-content);
  --color-primary: var(--color-primary);
  --color-primary-foreground: var(--color-primary-content);
  --color-secondary: var(--color-secondary);
  --color-secondary-foreground: var(--color-secondary-content);
  --color-muted: var(--color-base-200);
  --color-muted-foreground: var(--color-base-content);
  --color-accent: var(--color-accent);
  --color-accent-foreground: var(--color-accent-content);
  --color-destructive: var(--color-error);
  --color-destructive-foreground: var(--color-error-content);
  --color-border: var(--color-base-300);
  --color-input: var(--color-base-200);
  --color-ring: var(--color-primary);
  --color-chart-1: var(--color-primary);
  --color-chart-2: var(--color-secondary);
  --color-chart-3: var(--color-accent);
  --color-chart-4: var(--color-info);
  --color-chart-5: var(--color-success);
  --radius-sm: calc(var(--radius-field) - 2px);
  --radius-md: var(--radius-field);
  --radius-lg: var(--radius-box);
  --radius-xl: calc(var(--radius-box) + 4px);
  --color-sidebar: var(--color-base-100);
  --color-sidebar-foreground: var(--color-base-content);
  --color-sidebar-primary: var(--color-primary);
  --color-sidebar-primary-foreground: var(--color-primary-content);
  --color-sidebar-accent: var(--color-base-200);
  --color-sidebar-accent-foreground: var(--color-base-content);
  --color-sidebar-border: var(--color-base-300);
  --color-sidebar-ring: var(--color-primary);
}

/* 使用FlyonUI Black主题的官方颜色配置 */
/* 所有颜色变量由FlyonUI Black主题自动提供 */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    /* {{CHENGQI:
    Action: Modified
    Timestamp: 2025-05-29 14:20:00 +08:00
    Reason: 增强字体清晰度和平滑度
    Principle_Applied: KISS (通过综合方案提升字体渲染质量)
    Optimization: 添加多属性组合优化
    }} */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-smooth: always;
    text-shadow: 0 0 0.5px rgba(255, 255, 255, 0.1);
  }

  /* Unovis 图表样式 */
  :root {
    --vis-tooltip-background-color: none !important;
    --vis-tooltip-border-color: none !important;
    --vis-tooltip-text-color: none !important;
    --vis-tooltip-shadow-color: none !important;
    --vis-tooltip-backdrop-filter: none !important;
    --vis-tooltip-padding: none !important;

    --vis-primary-color: var(--primary);
    --vis-secondary-color: var(--chart-2);
    --vis-text-color: var(--muted-foreground);
  }

  /* Spline Hero 组件样式 */
  .neumorphic-button::after {
    content: '';
    @apply absolute inset-0 opacity-0 transition-opacity duration-300 bg-gradient-to-br from-primary/20 to-transparent rounded-full;
  }

  .neumorphic-button:hover::after {
    @apply opacity-100;
  }

  /* 添加全局字体优化 */
  :root {
    /* {{CHENGQI:
    Action: Added
    Timestamp: 2025-05-29 14:20:00 +08:00
    Reason: 引入更清晰的字体栈
    Principle_Applied: KISS (使用系统原生字体确保最佳渲染)
    }} */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji";
  }
}

.md-container h1,
.md-container h2,
.md-container h3,
.md-container h4,
.md-container h5,
.md-container h6,
.md-container ul,
.md-container li,
.md-container p,
.md-container blockquote,
.md-container pre,
.md-container code,
.md-container table,
.md-container th,
.md-container td,
.md-container a {
  all: revert;
}

/* .md-container ul li:has(a)::marker {
  color: #155dfc;
} */

.md-container ul li::marker {
  color: #155dfc;
}

.md-container a {
  color: #155dfc;
  line-height: 32px;
}

/* Markdown 内容样式增强 */
.prose {
  @apply text-base-content;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  @apply text-base-content font-bold mt-8 mb-4 first:mt-0;
}

.prose h1 {
  @apply text-3xl lg:text-4xl;
}

.prose h2 {
  @apply text-2xl lg:text-3xl;
}

.prose h3 {
  @apply text-xl lg:text-2xl;
}

.prose p {
  @apply mb-6 leading-relaxed text-base-content;
}

.prose ul,
.prose ol {
  @apply mb-6 space-y-2 pl-6;
}

.prose ul li {
  @apply relative;
}

.prose ul li::before {
  content: '•';
  @apply text-primary font-bold absolute -left-4;
}

.prose ol {
  @apply list-decimal list-outside;
}

.prose ol li {
  @apply pl-2;
}

.prose ol li::marker {
  @apply text-primary font-bold;
}

.prose blockquote {
  @apply border-l-4 border-primary pl-4 italic text-base-content/80 my-6;
}

.prose code {
  @apply bg-base-200 text-base-content px-1 py-0.5 rounded text-sm;
}

.prose pre {
  @apply bg-base-200 text-base-content p-4 rounded-lg overflow-x-auto my-6;
}

.prose pre code {
  @apply bg-transparent p-0;
}

.prose a {
  @apply text-primary hover:text-primary/80 underline;
}

.prose strong {
  @apply font-bold text-base-content;
}

.prose em {
  @apply italic text-base-content;
}

/* 代码语法高亮样式优化 */
.prose .hljs {
  @apply bg-base-200 text-base-content rounded-lg p-4 overflow-x-auto my-6;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.prose .hljs code {
  @apply bg-transparent p-0 text-inherit;
  font-family: inherit;
}

/* 覆盖highlight.js的默认颜色，使其适应主题 */
.prose .hljs-keyword,
.prose .hljs-selector-tag,
.prose .hljs-literal,
.prose .hljs-section,
.prose .hljs-link {
  @apply text-primary;
}

.prose .hljs-string,
.prose .hljs-title,
.prose .hljs-name,
.prose .hljs-type,
.prose .hljs-attribute,
.prose .hljs-symbol,
.prose .hljs-bullet,
.prose .hljs-addition,
.prose .hljs-variable,
.prose .hljs-template-tag,
.prose .hljs-template-variable {
  @apply text-success;
}

.prose .hljs-comment,
.prose .hljs-quote,
.prose .hljs-deletion,
.prose .hljs-meta {
  @apply text-base-content/60;
}

.prose .hljs-number,
.prose .hljs-regexp,
.prose .hljs-literal {
  @apply text-warning;
}

.prose .hljs-built_in,
.prose .hljs-builtin-name,
.prose .hljs-class .hljs-title {
  @apply text-info;
}