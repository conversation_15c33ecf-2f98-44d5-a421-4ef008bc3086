// API响应通用类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 威胁情报相关类型
export interface ThreatIntelligence {
  id: number;
  title: string;
  type: string;
  severity: '严重' | '高' | '中' | '低';
  source: string;
  status: '待处理' | '处理中' | '已处理' | '已关闭';
  description: string;
  detailed_analysis?: string;
  indicators: {
    ip?: string[];
    domain?: string[];
    hash?: string[];
    url?: string[];
  };
  tags: string[];
  confidence: number;
  created_at: string;
  updated_at: string;
  analyst: string;
  coverImage?: string;
  related_incidents?: Array<{
    id: number;
    title: string;
    date: string;
  }>;
}

// 漏洞相关类型
export interface Vulnerability {
  id: number;
  cve_id: string;
  title: string;
  severity: '严重' | '高' | '中' | '低';
  cvss_score: number;
  cvss_vector?: string;
  category: string;
  affected_systems: string[];
  vendor: string;
  product: string;
  version: string;
  description: string;
  technical_details?: string;
  impact_analysis?: string;
  status: '新发现' | '已确认' | '修复中' | '已修复' | '已忽略';
  published_date: string;
  discovered_date: string;
  last_modified: string;
  exploit_available: boolean;
  patch_available: boolean;
  patch_info?: {
    patch_id: string;
    patch_url: string;
    patch_date: string;
    patch_description: string;
  };
  references: string[];
  tags: string[];
  affected_assets?: Array<{
    asset_id: number;
    asset_name: string;
    asset_type: string;
    risk_level: string;
  }>;
  remediation_steps?: string[];
}

// 安全事件相关类型
export interface SecurityEvent {
  id: number;
  event_id: string;
  title: string;
  type: string;
  severity: '严重' | '高' | '中' | '低';
  status: '新事件' | '处理中' | '已处理' | '已关闭' | '误报';
  source_ip: string;
  target_ip: string;
  source_system: string;
  target_system: string;
  description: string;
  detailed_analysis?: string;
  detection_time: string;
  first_seen: string;
  last_seen: string;
  event_count: number;
  risk_score: number;
  analyst: string;
  tags: string[];
  coverImage?: string;
  timeline?: Array<{
    timestamp: string;
    action: string;
    user: string;
  }>;
  related_events?: Array<{
    id: number;
    title: string;
    type: string;
    time: string;
  }>;
  indicators?: {
    ip_addresses?: string[];
    domains?: string[];
    file_hashes?: string[];
    urls?: string[];
  };
  mitigation_actions?: string[];
  evidence_files?: Array<{
    filename: string;
    size: number;
    hash: string;
    upload_time: string;
  }>;
}

// 仪表板相关类型
export interface DashboardOverview {
  threat_intelligence: {
    total: number;
    new_today: number;
    high_severity: number;
    active_campaigns: number;
  };
  vulnerabilities: {
    total: number;
    critical: number;
    unpatched: number;
    new_this_week: number;
  };
  security_events: {
    total_today: number;
    critical_events: number;
    in_progress: number;
    resolved_today: number;
  };
  system_health: {
    sensors_online: number;
    sensors_total: number;
    data_sources_active: number;
    data_sources_total: number;
    system_uptime: number;
  };
  recent_alerts: Array<{
    id: number;
    title: string;
    type: string;
    severity: string;
    time: string;
    status: string;
  }>;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  nickname?: string;
  email?: string;
  phone?: string;
  company_name?: string;
  avatar?: string;
  department?: string;
  bio?: string;
  is_active?: boolean;
  agree_newsletter?: boolean;
  last_login_method?: 'account' | 'phone' | 'wechat';
  date_joined?: string;
  last_login?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  auth_token: string;
  user?: User;
}

// 谈判记录相关类型
export interface NegotiationMessage {
  id: number;
  sender: 'attacker' | 'victim';
  sender_name: string;
  sender_avatar?: string;
  content: string;
  message_type: 'text' | 'file' | 'payment_proof' | 'threat' | 'demand';
  timestamp: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  metadata?: {
    file_name?: string;
    file_size?: number;
    amount?: number;
    currency?: string;
    deadline?: string;
  };
}

// 谈判记录消息格式（基于后端模型）
export interface NegotiationMessage {
  party: 'Victim' | string; // 支持动态组织名称
  content: string;
  timestamp: string;
}

// 谈判记录类型（基于后端NegotiationRecord模型）
export interface NegotiationRecord {
  id: number;
  group: number;
  group_name?: string;
  paid: boolean;
  initialransom?: string;
  negotiatedransom?: string;
  ransom_reduction_percentage?: number;
  message_count: number;
  messages: NegotiationMessage[];
  victim_messages_count?: number;
  attacker_messages_count?: number;
  created_at: string;
  updated_at?: string;
}

// 应急工具类型
export interface Tool {
  description: string;
  id: number;
  name: string;
  content: string;
  file?: string;
  created_at: string;
  updated_at: string;
  ransomware_group: Pick<RansomwareGroup, 'id' | 'name' | 'slug' | 'status' | 'threat_level' | 'description'>;
  view_count: number;
}

// 位置/站点相关类型
export interface Location {
  id: number;
  title: string;
  type: 'website' | 'payment' | 'blog' | 'chat' | 'api' | string;
  available: boolean;
  last_visit: string;
  lastscrape: string;
  fqdn: string;
  favicon?: string;
  screenshot?: string;
}

// 勒索软件组织相关类型（基于后端模型字段）
export interface RansomwareGroup {
  // ===== 基本信息字段 =====
  id: number;
  name: string;
  slug: string;
  logo?: string;
  avg_delay_days?: string;
  info_stealer_percentage?: string; // 后端是CharField，所以是字符串类型
  aliases?: string[];
  description: string;
  external_information_source?: string;
  status: 'active' | 'inactive' | 'disbanded' | 'unknown';
  first_seen: string | null;
  last_activity: string | null;
  created_at: string;
  updated_at?: string;

  // ===== 技术特征字段 =====
  tools_used?: any; // JSON字段，包含工具分类信息
  locations?: any[]; // JSON字段，包含暗网站点信息

  // ===== 关联信息字段 =====
  threat_level: 'critical' | 'high' | 'medium' | 'low';
  victim_count: number;
  data_sources?: string[]; // JSON字段，数据来源列表

  // ===== TTPs 和漏洞信息字段 =====
  ttps?: any[]; // JSON字段，基于MITRE ATT&CK框架的信息
  vulnerabilities?: any[]; // JSON字段，CVE漏洞信息

  // ===== 关联模型字段 =====
  tools?: Tool[]; // 应急工具
  negotiation_records?: NegotiationRecord[]; // 谈判记录
  ransom_notes?: Array<{
    id: number;
    group: number;
    group_name: string;
    note_name: string;
    extension: string;
    content?: string;
    content_preview: string;
    created_at: string;
  }>; // 勒索信
  ioc_indicators?: Array<{
    id: number;
    ioc_types: string[];
    iocs: {
      [key: string]: string[];
    };
    created_at: string;
    updated_at: string;
  }>; // IOC指标
  victims?: Array<{
    id: number;
    post_title: string;
    discovered: string | null;
    description: string;
    website: string;
    published: string | null;
    post_url: string;
    country: string;
    activity: string;
    duplicates: any[];
    extrainfos: any;
    screenshot: string;
    infostealer: any;
    press: any;
    permalink: string;
    created_at: string;
    updated_at: string;
  }>; // 受害者信息

  // ===== 兼容性字段（用于向后兼容，可能从其他字段派生） =====
  coverImage?: string; // 可以使用logo字段
}

// 博客相关类型

export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  description: string;
  order: number;
  post_count: number;
}

export interface BlogTag {
  id: number;
  name: string;
  slug: string;
  description: string;
  color: string;
  post_count: number;
}

export interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  content?: string;
  cover_image: string;
  category: string;
  tags: string[];
  view_count: number;
  reading_time: number;
  meta_description?: string;
  meta_keywords?: string;
  created_at: string;
  updated_at: string;
  is_published: boolean;
  prev_post?: {
    id: number;
    title: string;
    category: string;
  };
  next_post?: {
    id: number;
    title: string;
    category: string;
  };
}

export interface BlogApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  pagination?: {
    count: number;
    page: number;
    page_size: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
    next_page: number | null;
    previous_page: number | null;
  };
}

// 受害者相关类型
export interface Victim {
  id: number;
  post_title: string;
  discovered: string | null;
  description: string;
  website: string;
  attack_date: string | null;
  published: string | null; // 向后兼容字段，映射自attack_date
  post_url: string;
  country: string;
  activity: string;
  duplicates: any[];
  extrainfos: any;
  screenshot: string;
  infostealer: any;
  press: any;
  permalink: string;
  created_at: string;
  updated_at: string;
  // 关联信息
  group_name?: string;
  group_slug?: string;
  // 向后兼容字段
  modifications?: Array<{
    date: string;
    description: string;
  }>;
}
