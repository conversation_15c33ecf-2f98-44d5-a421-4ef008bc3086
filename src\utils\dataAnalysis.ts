// 数据分析工具类
export interface VictimData {
  post_title: string;
  group_name: string;
  discovered: string;
  description: string;
  published: string;
  post_url: string;
  country: string;
  activity: string;
  website: string;
  duplicates: any[];
}

export interface CountryStats {
  country: string;
  countryName: string;
  count: number;
  percentage: number;
}

export interface GroupStats {
  group: string;
  count: number;
  percentage: number;
}

export interface ActivityStats {
  activity: string;
  activityName: string;
  count: number;
  percentage: number;
}

export interface TimeStats {
  year: string;
  month: string;
  count: number;
}

export interface MonthlyTrend {
  month: string;
  count: number;
}

export class DataAnalyzer {
  private data: VictimData[];

  // 国家代码到中文名称的映射
  private countryNameMap: { [key: string]: string } = {
    'US': '美国', 'CN': '中国', 'DE': '德国', 'GB': '英国', 'JP': '日本',
    'CA': '加拿大', 'AU': '澳大利亚', 'FR': '法国', 'IT': '意大利', 'RU': '俄罗斯',
    'BR': '巴西', 'IN': '印度', 'KR': '韩国', 'ES': '西班牙', 'NL': '荷兰',
    'SE': '瑞典', 'NO': '挪威', 'DK': '丹麦', 'FI': '芬兰', 'CH': '瑞士',
    'AT': '奥地利', 'BE': '比利时', 'PL': '波兰', 'CZ': '捷克', 'HU': '匈牙利',
    'PT': '葡萄牙', 'GR': '希腊', 'IE': '爱尔兰', 'IL': '以色列', 'TR': '土耳其',
    'ZA': '南非', 'EG': '埃及', 'NG': '尼日利亚', 'KE': '肯尼亚', 'MA': '摩洛哥',
    'MX': '墨西哥', 'AR': '阿根廷', 'CL': '智利', 'CO': '哥伦比亚', 'PE': '秘鲁',
    'TH': '泰国', 'VN': '越南', 'MY': '马来西亚', 'SG': '新加坡', 'ID': '印度尼西亚',
    'PH': '菲律宾', 'TW': '台湾', 'HK': '香港', 'NZ': '新西兰', 'UA': '乌克兰',
    'RO': '罗马尼亚', 'BG': '保加利亚', 'HR': '克罗地亚', 'SI': '斯洛文尼亚', 'SK': '斯洛伐克',
    'LT': '立陶宛', 'LV': '拉脱维亚', 'EE': '爱沙尼亚', 'IS': '冰岛', 'LU': '卢森堡',
    'MT': '马耳他', 'CY': '塞浦路斯', 'AE': '阿联酋', 'SA': '沙特阿拉伯', 'QA': '卡塔尔',
    'KW': '科威特', 'BH': '巴林', 'OM': '阿曼', 'JO': '约旦', 'LB': '黎巴嫩',
    'SY': '叙利亚', 'IQ': '伊拉克', 'IR': '伊朗', 'AF': '阿富汗', 'PK': '巴基斯坦',
    'BD': '孟加拉国', 'LK': '斯里兰卡', 'NP': '尼泊尔', 'MM': '缅甸', 'KH': '柬埔寨',
    'LA': '老挝', 'MN': '蒙古', 'KZ': '哈萨克斯坦', 'UZ': '乌兹别克斯坦', 'TM': '土库曼斯坦',
    'KG': '吉尔吉斯斯坦', 'TJ': '塔吉克斯坦', 'GE': '格鲁吉亚', 'AM': '亚美尼亚', 'AZ': '阿塞拜疆',
    'BY': '白俄罗斯', 'MD': '摩尔多瓦', 'RS': '塞尔维亚', 'BA': '波黑', 'ME': '黑山',
    'MK': '北马其顿', 'AL': '阿尔巴尼亚', 'XK': '科索沃', 'EC': '厄瓜多尔', 'BO': '玻利维亚',
    'PY': '巴拉圭', 'UY': '乌拉圭', 'VE': '委内瑞拉', 'GY': '圭亚那', 'SR': '苏里南',
    'FK': '福克兰群岛', 'GF': '法属圭亚那'
  };

  // 行业名称到中文名称的映射
  private activityNameMap: { [key: string]: string } = {
    'Business Services': '商业服务',
    'Manufacturing': '制造业',
    'Technology': '科技',
    'Healthcare': '医疗保健',
    'Healthcare and Public Health': '医疗卫生',
    'Financial': '金融',
    'Financial Services': '金融服务',
    'Education': '教育',
    'Education Facilities': '教育机构',
    'Government Facilities': '政府机构',
    'Public Sector': '公共部门',
    'Energy': '能源',
    'Transportation Systems': '交通运输',
    'Transportation/Logistics': '运输物流',
    'Communication': '通信',
    'Telecommunication': '电信',
    'Information Technology': '信息技术',
    'Critical Manufacturing': '关键制造业',
    'Commercial Facilities': '商业设施',
    'Emergency Services': '应急服务',
    'Food and Agriculture': '食品农业',
    'Agriculture and Food Production': '农业食品生产',
    'Water and Wastewater Systems': '水务系统',
    'Construction': '建筑',
    'Hospitality and Tourism': '酒店旅游',
    'Consumer Services': '消费服务',
    'Government': '政府',
    'Not Found': '未分类'
  };

  constructor(data: VictimData[]) {
    this.data = data;
  }

  // 获取国家中文名称
  private getCountryName(countryCode: string): string {
    return this.countryNameMap[countryCode] || countryCode;
  }

  // 获取行业中文名称
  private getActivityName(activity: string): string {
    return this.activityNameMap[activity] || activity;
  }

  // 获取国家分布统计
  getCountryStats(): CountryStats[] {
    const countryMap = new Map<string, number>();
    let totalWithCountry = 0;

    this.data.forEach(item => {
      if (item.country && item.country.trim() !== '') {
        const country = item.country.trim();
        countryMap.set(country, (countryMap.get(country) || 0) + 1);
        totalWithCountry++;
      }
    });

    return Array.from(countryMap.entries())
      .map(([country, count]) => ({
        country,
        countryName: this.getCountryName(country),
        count,
        percentage: Number(((count / totalWithCountry) * 100).toFixed(2))
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 15); // 取前15个国家
  }

  // 获取勒索组织统计
  getGroupStats(): GroupStats[] {
    const groupMap = new Map<string, number>();
    
    this.data.forEach(item => {
      if (item.group_name && item.group_name.trim() !== '') {
        const group = item.group_name.trim();
        groupMap.set(group, (groupMap.get(group) || 0) + 1);
      }
    });

    const total = this.data.length;
    return Array.from(groupMap.entries())
      .map(([group, count]) => ({
        group,
        count,
        percentage: Number(((count / total) * 100).toFixed(2))
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // 取前10个组织
  }

  // 获取行业分布统计
  getActivityStats(): ActivityStats[] {
    const activityMap = new Map<string, number>();
    let totalWithActivity = 0;

    this.data.forEach(item => {
      if (item.activity && item.activity.trim() !== '' && item.activity !== 'Not Found') {
        const activity = item.activity.trim();
        activityMap.set(activity, (activityMap.get(activity) || 0) + 1);
        totalWithActivity++;
      }
    });

    return Array.from(activityMap.entries())
      .map(([activity, count]) => ({
        activity,
        activityName: this.getActivityName(activity),
        count,
        percentage: Number(((count / totalWithActivity) * 100).toFixed(2))
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 12); // 取前12个行业
  }

  // 获取时间趋势统计（按年月）
  getTimeStats(): TimeStats[] {
    const timeMap = new Map<string, number>();

    this.data.forEach(item => {
      if (item.published && item.published.trim() !== '') {
        try {
          const date = new Date(item.published);
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear().toString();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const key = `${year}-${month}`;
            timeMap.set(key, (timeMap.get(key) || 0) + 1);
          }
        } catch (e) {
          // 忽略无效日期
        }
      }
    });

    return Array.from(timeMap.entries())
      .map(([key, count]) => {
        const [year, month] = key.split('-');
        return { year, month, count };
      })
      .sort((a, b) => {
        const aDate = new Date(parseInt(a.year), parseInt(a.month) - 1);
        const bDate = new Date(parseInt(b.year), parseInt(b.month) - 1);
        return aDate.getTime() - bDate.getTime();
      });
  }

  // 获取最近12个月的趋势
  getRecentMonthlyTrend(): MonthlyTrend[] {
    const timeStats = this.getTimeStats();
    const now = new Date();
    const monthlyData: MonthlyTrend[] = [];

    // 生成最近12个月的数据
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const year = date.getFullYear().toString();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const monthKey = `${year}-${month}`;
      
      const stat = timeStats.find(s => `${s.year}-${s.month}` === monthKey);
      const monthName = date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'short' });
      
      monthlyData.push({
        month: monthName,
        count: stat ? stat.count : 0
      });
    }

    return monthlyData;
  }

  // 获取总体统计信息
  getOverallStats() {
    const totalRecords = this.data.length;
    const uniqueCountries = new Set(this.data.filter(d => d.country && d.country.trim() !== '').map(d => d.country)).size;
    const uniqueGroups = new Set(this.data.filter(d => d.group_name && d.group_name.trim() !== '').map(d => d.group_name)).size;
    const uniqueActivities = new Set(this.data.filter(d => d.activity && d.activity.trim() !== '' && d.activity !== 'Not Found').map(d => d.activity)).size;

    // 计算最早和最新的记录时间
    const validDates = this.data
      .map(d => d.published)
      .filter(d => d && d.trim() !== '')
      .map(d => new Date(d))
      .filter(d => !isNaN(d.getTime()))
      .sort((a, b) => a.getTime() - b.getTime());

    const earliestDate = validDates.length > 0 ? validDates[0] : null;
    const latestDate = validDates.length > 0 ? validDates[validDates.length - 1] : null;

    return {
      totalRecords,
      uniqueCountries,
      uniqueGroups,
      uniqueActivities,
      earliestDate: earliestDate ? earliestDate.toLocaleDateString('zh-CN') : '未知',
      latestDate: latestDate ? latestDate.toLocaleDateString('zh-CN') : '未知'
    };
  }

  // 获取国家威胁分布数据（用于地图）
  getCountryThreatMap(): Map<string, number> {
    const countryMap = new Map<string, number>();

    this.data.forEach(item => {
      if (item.country && item.country.trim() !== '') {
        const country = item.country.trim();
        countryMap.set(country, (countryMap.get(country) || 0) + 1);
      }
    });

    return countryMap;
  }

  // 获取地理分布的详细统计（包含更多国家）
  getGeographicDistribution(): CountryStats[] {
    const countryMap = new Map<string, number>();
    let totalWithCountry = 0;

    this.data.forEach(item => {
      if (item.country && item.country.trim() !== '') {
        const country = item.country.trim();
        countryMap.set(country, (countryMap.get(country) || 0) + 1);
        totalWithCountry++;
      }
    });

    return Array.from(countryMap.entries())
      .map(([country, count]) => ({
        country,
        countryName: this.getCountryName(country),
        count,
        percentage: Number(((count / totalWithCountry) * 100).toFixed(2))
      }))
      .sort((a, b) => b.count - a.count);
  }


}
