#!/usr/bin/env python
"""
测试用户注册功能的脚本
"""
import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000"

def test_send_verification_code():
    """测试发送验证码"""
    print("1. 测试发送验证码...")
    
    url = f"{BASE_URL}/auth/send-verification-code/"
    data = {
        "phone": "13800138000",
        "code_type": "register"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    return response.status_code == 200

def test_user_registration():
    """测试用户注册"""
    print("\n2. 测试用户注册...")
    
    url = f"{BASE_URL}/auth/register/"
    data = {
        "username": "testuser001",
        "company_name": "测试科技有限公司",
        "phone": "13800138000",
        "verification_code": "123456",  # 在实际测试中需要使用真实验证码
        "password": "testpass123",
        "confirm_password": "testpass123",
        "agree_terms": True,
        "agree_newsletter": False
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    return response.status_code == 200

def test_user_login_before_activation():
    """测试未激活用户登录"""
    print("\n3. 测试未激活用户登录...")
    
    url = f"{BASE_URL}/auth/token/login/"
    data = {
        "username": "testuser001",
        "password": "testpass123"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    # 应该返回错误，提示账号待审核
    return response.status_code == 400

def test_activate_user():
    """激活用户（需要管理员权限）"""
    print("\n4. 激活用户...")
    print("注意：这需要在Django管理后台手动操作，或者通过管理员API")
    print("将用户的 is_active 字段设置为 True")
    
    # 这里可以添加管理员API调用来激活用户
    # 或者提示手动操作
    return True

def test_user_login_after_activation():
    """测试激活后用户登录"""
    print("\n5. 测试激活后用户登录...")
    print("注意：需要先在管理后台激活用户")
    
    url = f"{BASE_URL}/auth/token/login/"
    data = {
        "username": "testuser001",
        "password": "testpass123"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    
    return response.status_code == 200

def main():
    """主测试函数"""
    print("开始测试用户注册功能...")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("发送验证码", test_send_verification_code),
        ("用户注册", test_user_registration),
        ("未激活用户登录", test_user_login_before_activation),
        ("激活用户", test_activate_user),
        ("激活后用户登录", test_user_login_after_activation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, "通过" if result else "失败"))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, f"异常: {e}"))
        
        time.sleep(1)  # 避免请求过快
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    for test_name, result in results:
        print(f"{test_name}: {result}")

if __name__ == "__main__":
    main()
