"""
全局异常处理器
"""
from rest_framework.views import exception_handler
from rest_framework.exceptions import NotAuthenticated, AuthenticationFailed, PermissionDenied
from rest_framework.response import Response
from rest_framework import status
from django.http import Http404
from django.core.exceptions import ValidationError, PermissionDenied
from .response import StandardResponse
import logging

logger = logging.getLogger(__name__)


def custom_exception_handler_bak(exc, context):
    """
    自定义异常处理器
    
    将所有异常转换为统一的响应格式
    
    Args:
        exc: 异常对象
        context: 异常上下文
        
    Returns:
        Response: 统一格式的错误响应
    """
    # 调用DRF默认的异常处理器
    response = exception_handler(exc, context)
    
    # 记录异常日志
    logger.info(f"API异常: {exc}", exc_info=True)
    
    if response is not None:
        # DRF能处理的异常
        custom_response_data = {
            'success': False,
            'message': '请求处理失败',
            'data': None
        }
        
        # 根据异常类型设置具体的错误消息
        if response.status_code == status.HTTP_404_NOT_FOUND:
            custom_response_data['message'] = '资源未找到'
        elif response.status_code == status.HTTP_403_FORBIDDEN:
            custom_response_data['message'] = '权限不足'
        elif response.status_code == status.HTTP_401_UNAUTHORIZED:
            custom_response_data['message'] = '未授权访问'
        elif response.status_code == status.HTTP_400_BAD_REQUEST:
            custom_response_data['message'] = '请求参数错误'
            # 如果有详细的验证错误信息，添加到data字段
            if hasattr(response, 'data') and response.data:
                custom_response_data['data'] = response.data
        elif response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED:
            custom_response_data['message'] = '请求方法不允许'
        elif response.status_code >= 500:
            custom_response_data['message'] = '服务器内部错误'
        
        response.data = custom_response_data
        return response
    
    # DRF无法处理的异常，手动处理
    if isinstance(exc, Http404):
        return StandardResponse.not_found()
    elif isinstance(exc, PermissionDenied):
        return StandardResponse.forbidden()
    elif isinstance(exc, ValidationError):
        return StandardResponse.validation_error(
            message="数据验证失败",
            errors=str(exc)
        )
    else:
        # 其他未知异常
        return StandardResponse.server_error(
            message="服务器内部错误，请稍后重试"
        )


class CustomException(Exception):
    _default_code = 400

    def __init__(
        self,
        message: str = "",
        status_code=status.HTTP_400_BAD_REQUEST,
        data=None,
        code: int = _default_code,
        success=False,
    ):

        self.code = code
        self.status = status_code
        self.message = message
        if data is None:
            self.data = {"message": message, "success": success}
        else:
            self.data = data

    def __str__(self):
        return self.message


def custom_exception_handler(exc, context):
    # 自定义 CustomException 保持原样
    if isinstance(exc, CustomException):
        return Response(data=exc.data, status=exc.status)

    # 补充处理 DRF 常见异常
    if isinstance(exc, NotAuthenticated):
        return Response(
            data={"message": '未登录', "success": False},
            status=status.HTTP_401_UNAUTHORIZED
        )
    elif isinstance(exc, AuthenticationFailed):
        return Response(
            data={"message": '登录信息无效或已过期', "success": False},
            status=status.HTTP_401_UNAUTHORIZED
        )
    elif isinstance(exc, PermissionDenied):
        return Response(
            data={"message": '没有权限执行该操作', "success": False},
            status=status.HTTP_403_FORBIDDEN
        )

    # 其他 DRF 异常
    response = exception_handler(exc, context)

    if response is not None:
        response.data = {
            "message": response.data.get("detail", "系统错误"),
            "success": False
        }

    return response


def extract_first_error(errors: dict):
    """
    提取 serializer.errors 中的第一个错误提示
    """
    if isinstance(errors, dict):
        for field, msgs in errors.items():
            if isinstance(msgs, list) and msgs:
                return str(msgs[0])
            return str(msgs)
    return "参数错误"


def handle_404(request, exception):
    """
    处理404错误
    
    Args:
        request: 请求对象
        exception: 异常对象
        
    Returns:
        Response: 统一格式的404响应
    """
    return StandardResponse.not_found(message="请求的页面不存在")


def handle_500(request):
    """
    处理500错误
    
    Args:
        request: 请求对象
        
    Returns:
        Response: 统一格式的500响应
    """
    return StandardResponse.server_error(message="服务器内部错误，请稍后重试")
