"""
自定义MDEditor图片上传视图
解决MDEditor图片上传到阿里云OSS的问题
"""
import os
import uuid
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.conf import settings
import logging
from .oss_utils import get_public_url

logger = logging.getLogger(__name__)


@csrf_exempt
@require_http_methods(["POST"])
def upload_image(request):
    """
    自定义MDEditor图片上传处理
    将图片上传到阿里云OSS
    """
    try:
        # 检查是否有文件上传
        if 'editormd-image-file' not in request.FILES:
            return JsonResponse({
                'success': 0,
                'message': '没有找到上传的图片文件'
            })
        
        uploaded_file = request.FILES['editormd-image-file']
        
        # 验证文件类型
        allowed_formats = getattr(settings, 'MDEDITOR_CONFIGS', {}).get('default', {}).get('upload_image_formats', 
                                 ["jpg", "jpeg", "gif", "png", "bmp", "webp"])
        
        file_extension = uploaded_file.name.split('.')[-1].lower()
        if file_extension not in allowed_formats:
            return JsonResponse({
                'success': 0,
                'message': f'不支持的文件格式。支持的格式：{", ".join(allowed_formats)}'
            })
        
        # 验证文件大小（默认5MB）
        max_size = 5 * 1024 * 1024  # 5MB
        if uploaded_file.size > max_size:
            return JsonResponse({
                'success': 0,
                'message': '文件大小超过限制（最大5MB）'
            })
        
        # 生成唯一文件名
        file_name = f"{uuid.uuid4().hex}.{file_extension}"
        
        # 获取图片保存文件夹
        image_folder = getattr(settings, 'MDEDITOR_CONFIGS', {}).get('default', {}).get('image_folder', 'editor')
        file_path = f"{image_folder}/{file_name}"
        
        # 使用Django的默认存储器保存文件（会自动使用阿里云OSS）
        saved_path = default_storage.save(file_path, ContentFile(uploaded_file.read()))

        # 获取文件的公共URL（永不过期）
        file_url = get_public_url(saved_path)

        logger.info(f"MDEditor图片上传成功: {file_path} -> {file_url}")

        # 返回MDEditor期望的格式
        return JsonResponse({
            'success': 1,
            'message': '图片上传成功',
            'url': file_url
        })
        
    except Exception as e:
        logger.error(f"MDEditor图片上传失败: {str(e)}")
        return JsonResponse({
            'success': 0,
            'message': f'图片上传失败: {str(e)}'
        })
