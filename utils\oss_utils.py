"""
阿里云OSS工具函数
为公共读取的OSS bucket生成公共URL
"""
from django.conf import settings
from django.core.files.storage import default_storage


def get_public_url(file_path):
    """
    生成阿里云OSS的公共访问URL（不带签名，永不过期）

    Args:
        file_path (str): 文件在OSS中的路径

    Returns:
        str: 公共访问URL
    """
    if not file_path:
        return ''

    # 如果已经是完整URL，直接返回
    if file_path.startswith('http'):
        return file_path

    # 获取OSS配置
    oss_config = getattr(settings, 'ALIYUN_OSS', {})
    bucket_name = oss_config.get('BUCKET_NAME', '')
    endpoint = oss_config.get('ENDPOINT', '')

    if not bucket_name or not endpoint:
        # 如果配置不完整，回退到默认存储器的URL
        return default_storage.url(file_path)

    # 移除endpoint中的https://前缀
    endpoint_domain = endpoint.replace('https://', '').replace('http://', '')

    # 构建公共URL
    # 格式：https://{bucket_name}.{endpoint_domain}/{file_path}
    public_url = f"https://{bucket_name}.{endpoint_domain}/{file_path}"

    return public_url


def is_oss_url_expired(url):
    """
    检查OSS签名URL是否过期

    Args:
        url (str): OSS URL

    Returns:
        bool: 是否过期
    """
    if not url or 'Expires=' not in url:
        return False

    try:
        from urllib.parse import urlparse, parse_qs
        import time

        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        expires = query_params.get('Expires', [])
        if not expires:
            return False

        expire_timestamp = int(expires[0])
        current_timestamp = int(time.time())

        return current_timestamp >= expire_timestamp
    except (ValueError, IndexError):
        return False


def refresh_oss_url_if_needed(url):
    """
    如果URL过期，则刷新为公共URL

    Args:
        url (str): 原始URL

    Returns:
        str: 刷新后的公共URL
    """
    if not url:
        return ''

    # 检查是否过期
    if not is_oss_url_expired(url):
        return url

    try:
        # 从URL中提取文件路径
        from urllib.parse import urlparse
        parsed_url = urlparse(url)

        # 提取bucket和文件路径
        if '.oss-' in parsed_url.netloc:
            # 格式：bucket.oss-region.aliyuncs.com
            file_path = parsed_url.path.lstrip('/')
            return get_public_url(file_path)
    except Exception:
        pass

    return url
