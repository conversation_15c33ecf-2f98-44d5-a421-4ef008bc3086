"""
自定义分页器，支持统一响应格式
"""
from rest_framework.pagination import PageNumberPagination, LimitOffsetPagination
from rest_framework.response import Response
from .response import StandardResponse
from typing import Dict, Any


class StandardPageNumberPagination(PageNumberPagination):
    """
    标准分页器
    
    继承DRF的PageNumberPagination，返回符合统一格式的分页响应
    """
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    page_query_param = 'page'
    
    def get_paginated_response(self, data: Any) -> Response:
        """
        返回分页响应
        
        Args:
            data: 序列化后的数据
            
        Returns:
            Response: 统一格式的分页响应
        """
        pagination_info = self.get_pagination_info()
        
        return StandardResponse.success(
            data=data,
            message="获取数据成功",
            pagination=pagination_info
        )
    
    def get_pagination_info(self) -> Dict[str, Any]:
        """
        获取分页信息
        
        Returns:
            Dict: 分页信息字典
        """
        return {
            "count": self.page.paginator.count,
            "page": self.page.number,
            "page_size": self.get_page_size(self.request),
            "total_pages": self.page.paginator.num_pages,
            "has_next": self.page.has_next(),
            "has_previous": self.page.has_previous(),
            "next_page": self.page.next_page_number() if self.page.has_next() else None,
            "previous_page": self.page.previous_page_number() if self.page.has_previous() else None,
        }


class StandardLimitOffsetPagination(LimitOffsetPagination):
    """
    基于偏移量的分页器（可选）
    
    提供limit/offset风格的分页
    """
    default_limit = 20
    limit_query_param = 'limit'
    max_limit = 100
    offset_query_param = 'offset'
    
    def get_paginated_response(self, data: Any) -> Response:
        """
        返回分页响应
        
        Args:
            data: 序列化后的数据
            
        Returns:
            Response: 统一格式的分页响应
        """
        pagination_info = {
            "count": self.count,
            "limit": self.limit,
            "offset": self.offset,
            "has_next": self.get_next_link() is not None,
            "has_previous": self.get_previous_link() is not None,
        }
        
        return StandardResponse.success(
            data=data,
            message="获取数据成功",
            pagination=pagination_info
        )
