"""
自定义权限类和权限工具
"""
from rest_framework.permissions import BasePermission, IsAuthenticated, AllowAny


class DetailViewRequiresAuth(BasePermission):
    """
    详情视图需要认证的权限类
    
    - retrieve 操作需要用户登录
    - 其他操作允许匿名访问
    """
    
    def has_permission(self, request, view):
        """
        检查用户是否有权限访问视图
        
        Args:
            request: HTTP请求对象
            view: 视图对象
            
        Returns:
            bool: 是否有权限
        """
        # 如果是详情查看操作，需要用户认证
        if view.action == 'retrieve':
            return request.user and request.user.is_authenticated
        
        # 其他操作允许匿名访问
        return True


def get_detail_auth_permissions():
    """
    获取详情接口认证权限的便捷函数
    
    Returns:
        list: 权限类列表
    """
    return [DetailViewRequiresAuth()]


def get_permissions_for_action(action, authenticated_actions=None):
    """
    根据动作获取相应的权限类

    Args:
        action: 当前动作名称
        authenticated_actions: 需要认证的动作列表，默认为 ['retrieve', 'basic']

    Returns:
        list: 权限类列表
    """
    if authenticated_actions is None:
        authenticated_actions = ['retrieve', 'basic']

    if action in authenticated_actions:
        return [IsAuthenticated()]

    return [AllowAny()]
