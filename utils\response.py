"""
统一API响应格式封装
"""
from typing import Any, Dict, Optional, Union
from rest_framework.response import Response
from rest_framework import status
from drf_spectacular.utils import extend_schema_serializer
from rest_framework import serializers


class StandardResponseSerializer(serializers.Serializer):
    """
    标准API响应序列化器，用于drf-spectacular文档生成
    """
    success = serializers.BooleanField(help_text="请求是否成功")
    message = serializers.CharField(help_text="响应消息")
    data = serializers.JSONField(help_text="响应数据", allow_null=True) # type: ignore
    pagination = serializers.JSONField(help_text="分页信息", allow_null=True, required=False)


class PaginationInfoSerializer(serializers.Serializer):
    """
    分页信息序列化器
    """
    count = serializers.IntegerField(help_text="总记录数")
    page = serializers.IntegerField(help_text="当前页码")
    page_size = serializers.IntegerField(help_text="每页记录数")
    total_pages = serializers.IntegerField(help_text="总页数")
    has_next = serializers.BooleanField(help_text="是否有下一页")
    has_previous = serializers.BooleanField(help_text="是否有上一页")
    next_page = serializers.IntegerField(help_text="下一页页码", allow_null=True)
    previous_page = serializers.IntegerField(help_text="上一页页码", allow_null=True)


class StandardResponse:
    """
    标准API响应封装类
    
    提供统一的API响应格式：
    {
        "success": bool,
        "message": str,
        "data": any,
        "pagination": dict (可选)
    }
    """
    
    @staticmethod
    def success(
        data: Any = None,
        message: str = "操作成功",
        pagination: Optional[Dict] = None,
        status_code: int = status.HTTP_200_OK
    ) -> Response:
        """
        成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            pagination: 分页信息
            status_code: HTTP状态码
            
        Returns:
            Response: DRF响应对象
        """
        response_data = {
            "success": True,
            "message": message,
            "data": data
        }
        
        if pagination is not None:
            response_data["pagination"] = pagination
            
        return Response(response_data, status=status_code)
    
    @staticmethod
    def error(
        message: str = "操作失败",
        data: Any = None,
        status_code: int = status.HTTP_400_BAD_REQUEST
    ) -> Response:
        """
        错误响应
        
        Args:
            message: 错误消息
            data: 错误详情数据
            status_code: HTTP状态码
            
        Returns:
            Response: DRF响应对象
        """
        response_data = {
            "success": False,
            "message": message,
            "data": data
        }
        
        return Response(response_data, status=status_code)
    
    @staticmethod
    def not_found(message: str = "资源未找到") -> Response:
        """404错误响应"""
        return StandardResponse.error(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND
        )
    
    @staticmethod
    def forbidden(message: str = "权限不足") -> Response:
        """403错误响应"""
        return StandardResponse.error(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN
        )
    
    @staticmethod
    def unauthorized(message: str = "未授权访问") -> Response:
        """401错误响应"""
        return StandardResponse.error(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED
        )
    
    @staticmethod
    def validation_error(message: str = "数据验证失败", errors: Any = None) -> Response:
        """数据验证错误响应"""
        return StandardResponse.error(
            message=message,
            data=errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    @staticmethod
    def server_error(message: str = "服务器内部错误") -> Response:
        """500错误响应"""
        return StandardResponse.error(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


def response_wrapper(func):
    """
    响应包装装饰器
    
    自动将视图函数的返回值包装为标准响应格式
    """
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            # 如果已经是Response对象，直接返回
            if isinstance(result, Response):
                return result
            
            # 否则包装为成功响应
            return StandardResponse.success(data=result)
            
        except Exception as e:
            # 捕获异常并返回错误响应
            return StandardResponse.server_error(message=str(e))
    
    return wrapper
