"""
自定义序列化器字段
为OSS图片生成公共URL（永不过期）
"""
from rest_framework import serializers
from .oss_utils import get_public_url


class PublicImageField(serializers.ImageField):
    """
    公共图片字段，生成永不过期的公共URL

    注意：需要确保OSS bucket设置为公共读取权限
    """

    def to_representation(self, value):
        """将图片字段转换为公共URL"""
        if not value:
            return None

        # 获取文件路径
        if hasattr(value, 'name'):
            file_path = value.name
        else:
            file_path = str(value)

        # 生成公共URL
        return get_public_url(file_path)
