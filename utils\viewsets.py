"""
自定义视图集基类，支持统一响应格式
"""
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.http import Http404
from django.core.exceptions import ValidationError
from .response import StandardResponse
from .pagination import StandardPageNumberPagination
from typing import Any


class StandardModelViewSet(viewsets.ModelViewSet):
    """
    标准模型视图集
    
    继承DRF的ModelViewSet，自动应用统一响应格式
    """
    pagination_class = StandardPageNumberPagination
    
    def list(self, request, *args, **kwargs) -> Response:
        """
        列表视图
        
        Returns:
            Response: 统一格式的列表响应
        """
        try:
            queryset = self.filter_queryset(self.get_queryset())
            
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = self.get_serializer(queryset, many=True)
            return StandardResponse.success(
                data=serializer.data,
                message="获取列表成功"
            )
        except Exception as e:
            return StandardResponse.server_error(message=f"获取列表失败: {str(e)}")
    
    def retrieve(self, request, *args, **kwargs) -> Response:
        """
        详情视图
        
        Returns:
            Response: 统一格式的详情响应
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return StandardResponse.success(
                data=serializer.data,
                message="获取详情成功"
            )
        except Http404:
            return StandardResponse.not_found(message="资源未找到")
        except Exception as e:
            return StandardResponse.server_error(message=f"获取详情失败: {str(e)}")
    
    def create(self, request, *args, **kwargs) -> Response:
        """
        创建视图
        
        Returns:
            Response: 统一格式的创建响应
        """
        try:
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                self.perform_create(serializer)
                return StandardResponse.success(
                    data=serializer.data,
                    message="创建成功",
                    status_code=status.HTTP_201_CREATED
                )
            else:
                return StandardResponse.validation_error(
                    message="数据验证失败",
                    errors=serializer.errors
                )
        except ValidationError as e:
            return StandardResponse.validation_error(
                message="数据验证失败",
                errors=str(e)
            )
        except Exception as e:
            return StandardResponse.server_error(message=f"创建失败: {str(e)}")
    
    def update(self, request, *args, **kwargs) -> Response:
        """
        更新视图
        
        Returns:
            Response: 统一格式的更新响应
        """
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            
            if serializer.is_valid():
                self.perform_update(serializer)
                return StandardResponse.success(
                    data=serializer.data,
                    message="更新成功"
                )
            else:
                return StandardResponse.validation_error(
                    message="数据验证失败",
                    errors=serializer.errors
                )
        except Http404:
            return StandardResponse.not_found(message="资源未找到")
        except ValidationError as e:
            return StandardResponse.validation_error(
                message="数据验证失败",
                errors=str(e)
            )
        except Exception as e:
            return StandardResponse.server_error(message=f"更新失败: {str(e)}")
    
    def destroy(self, request, *args, **kwargs) -> Response:
        """
        删除视图
        
        Returns:
            Response: 统一格式的删除响应
        """
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return StandardResponse.success(
                message="删除成功",
                status_code=status.HTTP_204_NO_CONTENT
            )
        except Http404:
            return StandardResponse.not_found(message="资源未找到")
        except Exception as e:
            return StandardResponse.server_error(message=f"删除失败: {str(e)}")


class StandardReadOnlyModelViewSet(viewsets.ReadOnlyModelViewSet):
    """
    标准只读模型视图集
    
    继承DRF的ReadOnlyModelViewSet，自动应用统一响应格式
    适用于只提供读取功能的API
    """
    pagination_class = StandardPageNumberPagination
    
    def list(self, request, *args, **kwargs) -> Response:
        """
        列表视图
        
        Returns:
            Response: 统一格式的列表响应
        """
        try:
            queryset = self.filter_queryset(self.get_queryset())
            
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = self.get_serializer(queryset, many=True)
            return StandardResponse.success(
                data=serializer.data,
                message="获取列表成功"
            )
        except Exception as e:
            return StandardResponse.server_error(message=f"获取列表失败: {str(e)}")
    
    def retrieve(self, request, *args, **kwargs) -> Response:
        """
        详情视图
        
        Returns:
            Response: 统一格式的详情响应
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return StandardResponse.success(
                data=serializer.data,
                message="获取详情成功"
            )
        except Http404:
            return StandardResponse.not_found(message="资源未找到")
        except Exception as e:
            return StandardResponse.server_error(message=f"获取详情失败: {str(e)}")


class StandardGenericViewSet(viewsets.GenericViewSet):
    """
    标准通用视图集
    
    提供基础的视图集功能，可以根据需要添加具体的action
    """
    pagination_class = StandardPageNumberPagination
    
    def handle_exception(self, exc: Exception) -> Response:
        """
        统一异常处理
        
        Args:
            exc: 异常对象
            
        Returns:
            Response: 统一格式的错误响应
        """
        if isinstance(exc, Http404):
            return StandardResponse.not_found()
        elif isinstance(exc, ValidationError):
            return StandardResponse.validation_error(errors=str(exc))
        else:
            return StandardResponse.server_error(message=str(exc))
